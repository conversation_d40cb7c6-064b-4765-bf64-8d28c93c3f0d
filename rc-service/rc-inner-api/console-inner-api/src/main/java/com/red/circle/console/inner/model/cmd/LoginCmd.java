package com.red.circle.console.inner.model.cmd;

import com.red.circle.framework.core.dto.CommonCommand;
import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 登陆参数.
 *
 * <AUTHOR> on 2023/5/11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class LoginCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 账号.
   */
  @NotBlank(message = "username required.")
  private String username;

  /**
   * 密码.
   */
  @NotBlank(message = "password required.")
  private String password;

}
