package com.red.circle.console.inner.error;

import com.red.circle.framework.dto.IResponseErrorCode;
import com.red.circle.framework.dto.ResErrorCode;

/**
 * 后台管理业务错误编码：8000 ～ 8999.
 *
 * <AUTHOR> on 2023/5/11
 */
@ResErrorCode(describe = "后台管理业务错误", minCode = 10000, maxCode = 20000)
public enum ConsoleErrorCode implements IResponseErrorCode {

  /**
   * 未找到该用户.
   */
  USER_NOT_FOUND(10000, "The user was not found"),

  /**
   * 未找到该菜单.
   */
  MENU_NOT_FOUND(10001, "The menu was not found"),

  /**
   * 用户名密码错误
   */
  USERNAME_OR_PASSWORD_IS_WRONG(10002,
      "Incorrect username and password"),

  /**
   * 用户被禁用
   */
  USER_IS_DISABLED(10003, "User disabled"),

  /**
   * 原密码不正确
   */
  ORIGINAL_PASSWORD_IS_INCORRECT(10004,
      "The original password is incorrect"),

  /**
   * 用户名已存在
   */
  USERNAME_ALREADY_EXISTS(10005, "Username already exists"),

  /**
   * 输入用户ID超出了定投数量限制.
   */
  EXCEED_SPECIFY_QUANTITY_ERROR(10006,
      "The entered user ID exceeds the fixed investment quantity limit"),

  /**
   * 账户不存在.
   */
  ACCOUNT_DOES_NOT_EXIST(10007, "Account does not exist"),

  /**
   * 记录不存在.
   */
  NOT_FOUND_RECORD(10008, "Record does not exist"),

  /**
   * 活动已结束！.
   */
  ACTIVITY_CLOSE(10009, "The event is over!"),

  /**
   * 活动奖品已发送！.
   */
  ACTIVITY_REWARD_CLOSE(10010, "Event prizes have been sent!"),

  /**
   * 暂时只支持ASWAT礼物配置.
   */
  SUPPORT_ASWAT_ERROR(10011, "Currently only supports ASWAT gift configuration"),

  /**
   * 已处理
   */
  PROCESSED(10012, "processed"),

  /**
   * 数值为空或小于零.
   */
  NUMBER_VIOLATION_ERROR(10013, "Value is empty or less than zero"),

  /**
   * 请填写完整内容.
   */
  DATE_INCOMPLETE_ERROR(10014, "Please fill in the complete content"),

  /**
   * 业务相关场景组合已存在.
   */
  BUSINESS_EXISTS(10015, "Business-related scenario combinations already exist"),

  /**
   * 未知类型.
   */
  UNKNOWN_FAILURE(10016, "unknown type"),

  /**
   * 平台资源已存在.
   */
  RESOURCES_ALREADY_EXISTS(10017, "Platform resources already exist"),

  /**
   * 未选择系统平台
   */
  SYSTEM_NOT_SELECTED_ERROR(10018, "No system platform is selected"),

  /**
   * 一批最多赠送15个短账户.
   */
  ACCOUNT_QUANTITY_EXCESS_ERROR(10019,
      "A maximum of 15 short accounts can be given away in one batch"),

  /**
   * 资源已下架或不存在.
   */
  RESOURCES_SHELF_NOT_EXISTENT(10020, "The resource has been removed or does not exist"),

  /**
   * 没有数据.
   */
  NOT_DATA(10021, "Not data."),

  /**
   * 最多只能赠送30天.
   */
  DAY_EXCEED_LIMIT_ERROR(10022,
      "Gifts can only be given for a maximum of 30 days"),

  /**
   * 未找到相关信息.
   */
  NOT_FOUND_INFO(10023, "Not found info"),

  /**
   * 输入语言应为中文.
   */
  ZH_CN_LANGUAGE(10024, "Input language should be Chinese"),

  /**
   * 用户系统不一致.
   */
  SYS_ORIGIN_NOT_EQ(10025, "User system inconsistency"),

  /**
   * 用户已存在.
   */
  USER_ALREADY_EXISTS(10026, "User already exists"),

  /**
   * 没有找到用户信息.
   */
  USER_INFO_NOT_FOUND(10028, "User info not found"),

  /**
   * 没找到房间信息.
   */
  NOT_FOUND_ROOM(10029, "Room not found."),

  /**
   * 禁止操作房主.
   */
  NOT_ROLE_CHANGE_HOMEOWNER(10030, "Prohibit operating the homeowner"),

  /**
   * 归属系统不一致.
   */
  INCONSISTENT_ATTRIBUTION_SYSTEM(10031, "Inconsistent attribution system"),

  /**
   * 时间超出范围
   */
  TIME_OUT(10032, "Time out"),

  /**
   * 请须选择时间
   */
  PLEASE_SELECT_TIME(10033, "Please select time"),

  /**
   * 用户动态不能重复插入
   */
  USER_DYNAMICS_CANNOT_BE_INSERTED_REPEATEDLY(10034, "User dynamics cannot be inserted repeatedly");

  private final Integer code;

  private final String message;

  ConsoleErrorCode(Integer code, String message) {
    this.code = code;
    this.message = message;
  }


  @Override
  public Integer getCode() {
    return this.code;
  }

  @Override
  public String getMessage() {
    return this.message;
  }

  @Override
  public String getErrorCodeName() {
    return this.name();
  }

}
