package com.red.circle.console.inner.model.dto;

import com.red.circle.framework.dto.DTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 后台，用户资料.
 *
 * <AUTHOR> on 2023/5/11
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AdminUserDTO extends DTO {

  private static final long serialVersionUID = 1L;

  /**
   * 主键.
   */
  private Integer id;
  /**
   * 登陆名.
   */
  private String loginName;
  /**
   * 昵称.
   */
  private String nickname;
  /**
   * 邮箱
   */
  private String email;

  /**
   * 手机
   */
  private String phone;
  /**
   * IP地址
   */
  private String ip;

}
