package com.red.circle.game.fruit.inner.model.dto;

import com.red.circle.framework.core.dto.CommonCommand;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2023/12/20  14:01
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageGameUserDaysDTO extends CommonCommand {

  /**
   * 系统来源
   */
  private String sysOrigin;

  /**
   * 用户id.
   */
  private Long userId;

  /**
   * 日期.
   */
  private String date;

  /**
   * 下注次数.
   */
  private Integer betSize;

  /**
   * 日下注.
   */
  private BigDecimal betAmount;

  /**
   * 日得奖.
   */
  private BigDecimal earning;

  /**
   * 日盈利.
   */
  private BigDecimal earnings;

  /**
   * 盈利率.
   */
  private BigDecimal earningsProfit;

  /**
   * 历史盈利.
   */
  private BigDecimal earningsSum;

  /**
   * 创建时间
   */
  private Date createTime;
}
