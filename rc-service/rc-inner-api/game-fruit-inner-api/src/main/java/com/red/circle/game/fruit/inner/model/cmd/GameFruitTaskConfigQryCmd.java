package com.red.circle.game.fruit.inner.model.cmd;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.core.dto.PageCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 任务配置基本信息
 *
 * <AUTHOR> on 2023/12/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GameFruitTaskConfigQryCmd extends PageCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 赏金id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long bountyId;

}
