package com.red.circle.game.fruit.inner.model.cmd;

import com.red.circle.framework.core.dto.PageCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 赏金任务配置基本信息
 *
 * <AUTHOR> on 2023/12/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GameFruitBountyConfigQryCmd extends PageCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 归属系统.
   */
  private String sysOrigin;

  /**
   * 类型(每日、每周、每周(争霸赛)).
   */
  private String bountyType;

}
