package com.red.circle.game.fruit.inner.model.cmd;

import com.red.circle.framework.core.dto.PageCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> 2023/12/18  12:13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PageDaysRecordQryCmd extends PageCommand {

  @Serial
  private static final long serialVersionUID = 1L;
  /**
   * 日期排序 true ASC false DESC
   */
  private Boolean date;

  /**
   * 日新增排序
   */
  private Boolean newNum;

  /**
   * 日活跃排序
   */
  private Boolean playerNum;

  /**
   * 日下注排序
   */
  private Boolean userExpend;

  /**
   * 日中奖排序
   */
  private Boolean platformExpend;

  /**
   * 日盈利排序
   */
  private Boolean profit;

  /**
   * 日盈利率排序
   */
  private Boolean earningsRate;
}
