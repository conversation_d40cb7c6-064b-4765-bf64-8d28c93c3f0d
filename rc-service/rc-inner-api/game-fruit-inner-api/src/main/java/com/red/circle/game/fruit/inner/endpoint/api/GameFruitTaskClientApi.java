package com.red.circle.game.fruit.inner.endpoint.api;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.game.fruit.inner.model.cmd.GameFruitBountyConfigQryCmd;
import com.red.circle.game.fruit.inner.model.cmd.GameFruitTaskConfigQryCmd;
import com.red.circle.game.fruit.inner.model.dto.GameFruitAwardPictureDTO;
import com.red.circle.game.fruit.inner.model.dto.GameFruitBountyConfigDTO;
import com.red.circle.game.fruit.inner.model.dto.GameFruitTaskConfigDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 摩天轮游戏任务相关
 *
 * <AUTHOR> on 2023/12/13
 */
public interface GameFruitTaskClientApi {

  String API_PREFIX = "/game/fruit/task";

  @PostMapping("/getGameFruitBountyConfig")
  ResultResponse<PageResult<GameFruitBountyConfigDTO>> getGameFruitBountyConfig(
      @RequestBody GameFruitBountyConfigQryCmd query);

  @PostMapping("/addGameFruitBountyConfig")
  ResultResponse<Void> addGameFruitBountyConfig(@RequestBody GameFruitBountyConfigDTO param);

  @GetMapping("/deleteGameFruitBountyConfig")
  ResultResponse<Void> deleteGameFruitBountyConfig(@RequestParam("id") Long id);

  @PostMapping("/getGameFruitTaskConfig")
  ResultResponse<PageResult<GameFruitTaskConfigDTO>> getGameFruitTaskConfig(
      @RequestBody GameFruitTaskConfigQryCmd query);

  @PostMapping("/addGameFruitTaskConfig")
  ResultResponse<Void> addGameFruitTaskConfig(@RequestBody GameFruitTaskConfigDTO param);

  @GetMapping("/deleteGameFruitTaskConfig")
  ResultResponse<Void> deleteGameFruitTaskConfig(@RequestParam("id") Long id);

  @GetMapping("/getGameFruitImages")
  ResultResponse<List<GameFruitAwardPictureDTO>> getGameFruitImages(
      @RequestParam(value = "sysOrigin", required = false) String sysOrigin);
}
