package com.red.circle.game.fruit.inner.model.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 游戏配置.
 * </p>
 *
 * <AUTHOR> on 2023-12-19 15:03
 */
@Getter
@Setter
public class GameCodeManageDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 游戏id.
   */
  private Long gameId;

  /**
   * 游戏编码.
   */
  private String gameCode;


}
