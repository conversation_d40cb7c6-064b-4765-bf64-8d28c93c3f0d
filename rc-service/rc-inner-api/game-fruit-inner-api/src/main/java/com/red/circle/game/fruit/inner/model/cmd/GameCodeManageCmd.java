package com.red.circle.game.fruit.inner.model.cmd;

import com.red.circle.framework.core.dto.CommonCommand;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 游戏配置.
 * </p>
 *
 * <AUTHOR> on 2023-12-19 15:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GameCodeManageCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 游戏id.
   */
  private Long gameId;

  /**
   * 游戏名称.
   */
  private String gameName;

  /**
   * 来源.
   */
  private String origin;

  /**
   * 游戏编码.
   */
  private String gameCode;


}
