package com.red.circle.game.fruit.inner.endpoint;

import com.red.circle.common.business.core.constant.ServiceNameConstant;
import com.red.circle.game.fruit.inner.endpoint.api.GameFruitHistoryClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 摩天轮游戏记录相关
 *
 * <AUTHOR> on 2023/12/13
 */
@FeignClient(contextId = "gameFruitHistoryClient",
    name = ServiceNameConstant.SERVICE_GAME_FRUIT,
    path = GameFruitHistoryClientApi.API_PREFIX)
public interface GameFruitHistoryClient extends GameFruitHistoryClientApi {

}
