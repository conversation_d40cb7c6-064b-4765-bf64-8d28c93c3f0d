package com.red.circle.game.fruit.inner.model.dto;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 每日游戏记录.
 * </p>
 *
 * <AUTHOR> on 2023-11-30 19:18
 */
@Getter
@Setter
@Accessors(chain = true)
public class PageGameFruitDaysRecordDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 平台名称
   */
  private String sysOrigin;

  /**
   * 总下注金额.
   */
  private BigDecimal userExpend;

  /**
   * 平台总支出.
   */
  private BigDecimal platformExpend;

  /**
   * 参与人数.
   */
  private Integer playerNum;

  /**
   * 新增人数.
   */
  private Integer newNum;

  /**
   * 开出披萨次数.
   */
  private Integer pizzaNum;


  /**
   * 开出蔬菜次数.
   */
  private Integer vegetableNum;

  /**
   * 年月日.
   */
  private String date;

  /**
   * 盈利
   */
  private BigDecimal profit;

  /**
   * 盈利率
   */
  private BigDecimal earningsRate;


}
