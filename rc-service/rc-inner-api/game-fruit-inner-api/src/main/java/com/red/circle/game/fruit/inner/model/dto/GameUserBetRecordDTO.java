package com.red.circle.game.fruit.inner.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> 2023/12/19  14:07
 */
@Data
public class GameUserBetRecordDTO implements Serializable {

  /**
   * 系统来源
   */
  private String sysOrigin;

  /**
   * 游戏轮数
   */
  private Integer round;

  /**
   * 用户id
   */
  private Long userId;


  /**
   * 最后下注时间
   */
  private Date lastBetTime;

  /**
   * 下注奖品
   */
  private String betPrize;

  /**
   * 开奖奖品
   */
  private String lotteryPrize;

  /**
   * 下注金额
   */
  private BigDecimal betAmount;

  /**
   * 获得奖励
   */
  private BigDecimal awards;
}
