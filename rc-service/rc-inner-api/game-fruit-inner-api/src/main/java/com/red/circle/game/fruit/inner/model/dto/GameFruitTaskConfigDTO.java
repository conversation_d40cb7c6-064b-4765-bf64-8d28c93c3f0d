package com.red.circle.game.fruit.inner.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.core.dto.CommonCommand;
import java.io.Serial;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 摩天轮游戏赏金任务配置.
 * </p>
 *
 * <AUTHOR> on 2023-12-13 17:43
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class GameFruitTaskConfigDTO extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 归属系统.
   */
  private String sysOrigin;

  /**
   * 赏金id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long bountyId;

  /**
   * 资源组id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long resourceGroupId;
  /**
   * 任务类型.
   */
  private String taskType;

  /**
   * 数值.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long quantity;

  /**
   * 周争霸赛类型完成天数.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long quantityDays;

  /**
   * 游戏奖项id.
   */
  private List<String> gameAwardId;

  /**
   * 排序.
   */
  private Integer sort;

  /**
   * 创建时间.
   */
  private Date createTime;


}
