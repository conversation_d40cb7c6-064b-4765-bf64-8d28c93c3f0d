package com.red.circle.game.fruit.inner.model.dto;

import com.red.circle.framework.dto.PageResult;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> 2023/12/29  15:04
 */
@Data
@Accessors(chain = true)
public class DaysRecordDTO {

  /**
   * 每日游戏数据
   */
  private PageResult<PageGameFruitDaysRecordDTO> pageResult;

  /**
   * 筛选下注总和
   */
  private BigDecimal filterBetAmount;

  /**
   * 筛选奖励总和
   */
  private BigDecimal filterAward;

  /**
   * 筛选参与人数总和
   */
  private Integer filterActiveCount;

  /**
   * 下注总和
   */
  private BigDecimal sumBetAmount;

  /**
   * 奖励总和
   */
  private BigDecimal sumAward;

  /**
   * 总参与人数总和
   */
  private Integer sumActiveCount;
}
