package com.red.circle.game.fruit.inner.endpoint.api;


import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.game.fruit.inner.model.cmd.GameCodeManageCmd;
import com.red.circle.game.fruit.inner.model.cmd.GameFruitPrizeCmd;
import com.red.circle.game.fruit.inner.model.cmd.GameFruitRuleCmd;
import com.red.circle.game.fruit.inner.model.dto.GameCodeManageDTO;
import com.red.circle.game.fruit.inner.model.dto.GameFruitPrizeDTO;
import com.red.circle.game.fruit.inner.model.dto.GameFruitRuleDTO;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 摩天轮游戏配置相关
 *
 * <AUTHOR> on 2023/12/13
 */
public interface GameFruitConfigClientApi {

  String API_PREFIX = "/game/fruit/config";

  @PostMapping("/updateRule")
  ResultResponse<Void> updateRule(@RequestBody GameFruitRuleCmd cmd);

  @GetMapping("/getRule")
  ResultResponse<GameFruitRuleDTO> getRule(@RequestParam("sysOrigin") String sysOrigin);

  @PostMapping("/addPrize")
  ResultResponse<Void> addPrize(@RequestBody GameFruitPrizeCmd cmd);

  @PostMapping("/updatePrize")
  ResultResponse<Void> updatePrize(@RequestBody GameFruitPrizeCmd cmd);

  @GetMapping("/listPrize")
  ResultResponse<List<GameFruitPrizeDTO>> listPrize(@RequestParam("sysOrigin") String sysOrigin);

  @PostMapping("/mapGameCodeManage")
  ResultResponse<Map<Long, GameCodeManageDTO>> mapGameCodeManage(@RequestBody Set<Long> gameIds);

  @PostMapping("/addGameCode")
  ResultResponse<Void> addGameCode(@RequestBody GameCodeManageCmd gameCodeManage);

  @GetMapping("/delGameCode")
  ResultResponse<Void> delGameCode(@RequestParam("gameId") Long gameId);
}
