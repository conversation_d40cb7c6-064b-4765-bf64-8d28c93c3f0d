package com.red.circle.game.fruit.inner.endpoint.api;


import com.red.circle.common.business.dto.cmd.PageUserIdCmd;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.game.fruit.inner.model.cmd.GameDataStatisticalCmd;
import com.red.circle.game.fruit.inner.model.cmd.PageDaysRecordQryCmd;
import com.red.circle.game.fruit.inner.model.dto.DaysRecordDTO;
import com.red.circle.game.fruit.inner.model.dto.GameDataStatisticalDTO;
import com.red.circle.game.fruit.inner.model.dto.PageGameUserDaysDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 摩天轮游戏配置相关
 *
 * <AUTHOR> on 2023/12/13
 */
public interface GameFruitHistoryClientApi {

  String API_PREFIX = "/game/fruit/history";

  @PostMapping("/pageGameFruitDaysRecord")
  ResultResponse<DaysRecordDTO> pageGameFruitDaysRecord(
      @RequestBody PageDaysRecordQryCmd cmd);

  @PostMapping("/pageUserBetRecord")
  ResultResponse<GameDataStatisticalDTO> pageUserBetRecord(
      @RequestBody GameDataStatisticalCmd cmd);

  @PostMapping("/pageDaysUserData")
  ResultResponse<PageResult<PageGameUserDaysDTO>> pageDaysUserData(@RequestBody PageUserIdCmd cmd);
}
