package com.red.circle.game.fruit.inner.model.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 摩天轮游戏奖品信息.
 * </p>
 *
 * <AUTHOR> on 2023-12-18
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class GameFruitAwardPictureDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  private Long id;


  /**
   * 图片.
   */
  private String img;


}
