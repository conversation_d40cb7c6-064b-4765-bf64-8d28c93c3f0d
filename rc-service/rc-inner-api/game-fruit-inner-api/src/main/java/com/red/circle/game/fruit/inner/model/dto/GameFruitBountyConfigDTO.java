package com.red.circle.game.fruit.inner.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.core.dto.CommonCommand;
import java.io.Serial;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 摩天轮游戏赏金任务配置.
 * </p>
 *
 * <AUTHOR> on 2023-12-13 17:43
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class GameFruitBountyConfigDTO extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 归属系统.
   */
  private String sysOrigin;

  /**
   * 类型(每日、每周、每周(争霸赛)).
   */
  private String bountyType;

  /**
   * 名称.
   */
  private String name;


  /**
   * 排序.
   */
  private Integer sort;

  /**
   * 操作人
   */
  private Long operationUser;

  /**
   * 创建时间.
   */
  private Date createTime;


}
