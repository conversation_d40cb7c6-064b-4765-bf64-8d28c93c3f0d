package com.red.circle.game.fruit.inner.model.cmd;

import com.red.circle.common.business.dto.cmd.HistoryRangeTimeQryPageCmd;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 游戏数据汇总
 *
 * <AUTHOR> 2023/12/19  14:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GameDataStatisticalCmd extends HistoryRangeTimeQryPageCmd {

  /**
   * 用戶id
   */
  private Long userId;

  /**
   * 游戏编码
   */
  private String gameCode;

  /**
   * 系统来源
   */
  private String sysOrigin;
}
