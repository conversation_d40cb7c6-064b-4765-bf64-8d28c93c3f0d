package com.red.circle.game.fruit.inner.model.dto;

import com.red.circle.framework.dto.PageResult;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 游戏数据汇总
 *
 * <AUTHOR> 2023/12/19  14:27
 */
@Data
@Accessors(chain = true)
public class GameDataStatisticalDTO implements Serializable {

  /**
   * 用户下注记录
   */
  private PageResult<GameUserBetRecordDTO> gameUserBetRecords;

  /**
   * 搜索结果下注金币
   */
  private BigDecimal betAmount;

  /**
   * 搜索结果奖励
   */
  private BigDecimal awards;

  /**
   * 搜索结果参与人数
   */
  private Integer playerNum;
}
