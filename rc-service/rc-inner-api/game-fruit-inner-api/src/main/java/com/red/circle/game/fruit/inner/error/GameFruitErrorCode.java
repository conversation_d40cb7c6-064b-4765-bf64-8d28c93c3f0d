package com.red.circle.game.fruit.inner.error;


import com.red.circle.framework.dto.IResponseErrorCode;
import com.red.circle.framework.dto.ResErrorCode;

/**
 * 用户相关code， 范围： 8600 ～ 8700 状态.
 *
 * <AUTHOR> on 2023/12/15
 */
@ResErrorCode(describe = "摩天轮游戏", minCode = 8600, maxCode = 8700)
public enum GameFruitErrorCode implements IResponseErrorCode {


  /**
   * 摩天轮游戏赏金任务未配置
   */
  GAME_FRUIT_BOUNTY_INFO_NOT_FOUND(8600, "Game Fruit Bounty Not Found"),

  /**
   * 摩天轮游戏任务奖励未找到
   */
  GAME_FRUIT_TASK_AWARD_INFO_NOT_FOUND(8601, "Game Fruit Task Award Not Found"),

  /**
   * 摩天轮游戏任务奖励已领取
   */
  GAME_FRUIT_TASK_AWARD_IS_RECEIVED(8602, "Game Fruit Task Award is Received"),

  /**
   * 摩天轮游戏任务奖励领取成功
   */
  GAME_FRUIT_TASK_AWARD_RECEIVE_SUCCESS(8603, "Game Fruit Task Award  Receive Success"),

  /**
   * 摩天轮游戏任务未完成，不可领取
   */
  GAME_FRUIT_TASK_AWARD_NOT_COMPLETED(8604, "Game Fruit Task Not Completed");


  private final Integer code;
  private final String message;

  GameFruitErrorCode(Integer code, String message) {
    this.code = code;
    this.message = message;
  }

  @Override
  public Integer getCode() {
    return this.code;
  }

  @Override
  public String getMessage() {
    return message;
  }

  @Override
  public String getErrorCodeName() {
    return this.name();
  }

}
