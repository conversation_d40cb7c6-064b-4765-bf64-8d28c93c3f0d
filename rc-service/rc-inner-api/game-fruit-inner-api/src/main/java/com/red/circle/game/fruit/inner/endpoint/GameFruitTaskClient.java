package com.red.circle.game.fruit.inner.endpoint;

import com.red.circle.common.business.core.constant.ServiceNameConstant;
import com.red.circle.game.fruit.inner.endpoint.api.GameFruitTaskClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 摩天轮游戏任务相关
 *
 * <AUTHOR> on 2023/12/13
 */
@FeignClient(contextId = "gameFruitTaskClient",
    name = ServiceNameConstant.SERVICE_GAME_FRUIT,
    path = GameFruitTaskClientApi.API_PREFIX)
public interface GameFruitTaskClient extends GameFruitTaskClientApi {

}
