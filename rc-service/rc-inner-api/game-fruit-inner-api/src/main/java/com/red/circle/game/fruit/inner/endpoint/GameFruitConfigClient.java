package com.red.circle.game.fruit.inner.endpoint;

import com.red.circle.common.business.core.constant.ServiceNameConstant;
import com.red.circle.game.fruit.inner.endpoint.api.GameFruitConfigClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 摩天轮游戏配置相关
 *
 * <AUTHOR> on 2023/12/13
 */
@FeignClient(contextId = "gameFruitConfigClient",
    name = ServiceNameConstant.SERVICE_GAME_FRUIT,
    path = GameFruitConfigClientApi.API_PREFIX)
public interface GameFruitConfigClient extends GameFruitConfigClientApi {

}
