package com.red.circle.game.fruit.inner.model.cmd;

import com.red.circle.framework.core.dto.CommonCommand;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 摩天轮游戏奖品.
 * </p>
 *
 * <AUTHOR> on 2023-11-30 19:18
 */
@Getter
@Setter
public class GameFruitPrizeCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 系统来源
   */
  private String sysOrigin;

  /**
   * 奖品名称.
   */
  private String prizeName;

  /**
   * 下注类型 1 蔬菜 2 披萨
   */
  private Integer type;

  /**
   * 图片.
   */
  private String prizeImg;

  /**
   * 奖励倍数.
   */
  private Integer award;

  /**
   * 概率.
   */
  private BigDecimal prob;

  /**
   * 排序.
   */
  private Integer sort;


}
