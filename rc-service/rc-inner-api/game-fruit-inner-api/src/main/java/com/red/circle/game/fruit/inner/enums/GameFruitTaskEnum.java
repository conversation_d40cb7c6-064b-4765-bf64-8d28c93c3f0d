package com.red.circle.game.fruit.inner.enums;

import java.util.Objects;

/**
 * 摩天轮游戏任务相关.
 *
 * <AUTHOR> on 2023/12/18
 */
public enum GameFruitTaskEnum {
  /**
   * 日任务全完成.
   */
  TASK_COMPLETE_DAY,

  /**
   * 周任务全完成.
   */
  TASK_COMPLETE_WEEK,

  /**
   * 周争霸赛.
   */
  WEEK_COMPETITION,

  /**
   * 盈利轮数.
   */
  NUMBER_OF_PROFIT_ROUNDS,

  /**
   * 盈利金币数.
   */
  NUMBER_OF_PROFIT_COINS,

  /**
   * 游戏连赢轮数.
   */
  NUMBER_OF_WINNING_ROUNDS,

  /**
   * 一周玩游戏天数.
   */
  NUMBER_OF_DAYS_WEEK,

  /**
   * 游戏中中奖某奖项需达到的次数.
   */
  WINNING_ONE_OF_THE_PRIZES_ONE,

  /**
   * 游戏中中奖某奖项需达到的次数(蔬菜或披萨).
   */
  WINNING_ONE_OF_THE_PRIZES_TWO;

  public boolean eq(String name) {
    return Objects.equals(this.name(), name);
  }

}
