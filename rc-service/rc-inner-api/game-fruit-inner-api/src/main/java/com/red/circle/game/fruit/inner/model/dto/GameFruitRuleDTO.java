package com.red.circle.game.fruit.inner.model.dto;

import com.red.circle.framework.core.dto.CommonCommand;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 摩天轮游戏规则.
 * </p>
 *
 * <AUTHOR> on 2023-11-30 19:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GameFruitRuleDTO extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 1L;

  private Long id;

  /**
   * 投注区间.
   */
  private String wager;

  /**
   * 蔬菜图片.
   */
  private String vegetableImg;

  /**
   * 蔬菜开奖盈利.
   */
  private BigDecimal vegetableLotteryProfit;

  /**
   * 蔬菜开奖次数.
   */
  private Integer vegetableNumber;

  /**
   * 披萨图片.
   */
  private String pizzaImg;

  /**
   * 披萨开奖盈利.
   */
  private BigDecimal pizzaLotteryProfit;

  /**
   * 披萨开奖次数.
   */
  private Integer pizzaNumber;

  /**
   * 返奖率最小值(例如为0.98:1-0.98=亏损率).
   */
  private BigDecimal lossRatio;

  /**
   * 返奖率最打值（例如为1.05:1.05-1=盈利率）.
   */
  private BigDecimal profitRatio;

  /**
   * 当日亏损多少得奖.
   */
  private BigDecimal todayLossLottery;

  /**
   * 亏损率百分之多少得奖.
   */
  private BigDecimal lossRatioLottery;

  /**
   * 单项奖下注多少显示热门
   */
  private Integer hotBetAmount;

  /**
   * 游戏规则.
   */
  private String rule;
}
