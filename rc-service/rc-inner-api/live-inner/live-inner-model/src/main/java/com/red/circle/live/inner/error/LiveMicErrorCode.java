package com.red.circle.live.inner.error;

import com.red.circle.framework.dto.IResponseErrorCode;
import com.red.circle.framework.dto.ResErrorCode;

/**
 * <AUTHOR> on 2023/12/8
 */

public enum LiveMicErrorCode implements IResponseErrorCode {

  /**
   * 麦克风已上锁.
   */
  MIC_LOCKED(3300, "Microphone locked"),

  /**
   * 权限不足.
   */
  MIC_PERMISSIONS(3301, "Insufficient permissions"),

  /**
   * 麦克风索引不存在.
   */
  MIC_INDEX_NOT_FOUND(3302, "Microphone index not found"),

  /**
   * 不可已操作自己.
   */
  MIC_NOT_OPS_SELF(3303, "Can't operate yourself"),

  /**
   * 麦克风已占用.
   */
  MICROPHONE_OCCUPIED(3304, "Microphone occupied");

  private final Integer code;

  private final String message;

  LiveMicErrorCode(Integer code, String message) {
    this.code = code;
    this.message = message;
  }

  @Override
  public Integer getCode() {
    return this.code;
  }

  @Override
  public String getMessage() {
    return this.message;
  }

  @Override
  public String getErrorCodeName() {
    return this.name();
  }
}
