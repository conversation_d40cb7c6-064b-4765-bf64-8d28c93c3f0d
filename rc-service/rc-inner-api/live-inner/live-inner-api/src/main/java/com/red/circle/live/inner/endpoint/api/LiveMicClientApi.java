package com.red.circle.live.inner.endpoint.api;

import com.red.circle.framework.dto.ResultResponse;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 直播麦克风.
 *
 * <AUTHOR> on 2023/12/13
 */
public interface LiveMicClientApi {

  String API_PREFIX = "/live/mic/client";

  /**
   * 创建麦克风.
   */
  @GetMapping("/create")
  ResultResponse<Void> create(
      @RequestParam("roomId") Long roomId,
      @RequestParam("micSize") Integer micSize
  );

  /**
   * 修改麦克风数量.
   */
  @GetMapping("/updateMickSize")
  ResultResponse<Void> updateMickSize(
      @RequestParam("roomId") Long roomId,
      @RequestParam("micSize") Integer micSize
  );

  /**
   * 刷新麦克风用户角色.
   */
  @GetMapping("/refreshMicUserRoles")
  ResultResponse<Void> refreshMicUserRoles(
      @RequestParam("roomId") Long roomId,
      @RequestParam("userId") Long userId
  );

  /**
   * 房间在线人数.
   */
  @GetMapping("/getLiveRoomUserSize")
  ResultResponse<Long> getLiveRoomUserSize(@RequestParam("roomId") Long roomId);

  /**
   * 房间在线用户.
   */
  @GetMapping("/getLiveRoomUserIds")
  ResultResponse<List<Long>> getLiveRoomUserIds(
      @RequestParam("roomId") Long roomId,
      @RequestParam("size") Integer size);

}
