package com.red.circle.live.inner.endpoint;

import com.red.circle.live.inner.endpoint.api.LiveMicClientApi;
import com.red.circle.live.inner.endpoint.api.LiveRoomUserClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 房间用户.
 *
 * <AUTHOR> on 2023/12/26
 */
@FeignClient(name = "liveRoomUserClient", url = "${feign.live.url}" +
        LiveRoomUserClientApi.API_PREFIX)
public interface LiveRoomUserClient extends LiveRoomUserClientApi {

}
