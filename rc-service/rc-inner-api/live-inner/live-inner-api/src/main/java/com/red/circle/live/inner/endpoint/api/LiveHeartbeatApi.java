package com.red.circle.live.inner.endpoint.api;


import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.live.inner.model.cmd.LiveHeartbeatAddCmd;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface LiveHeartbeatApi {

  String API_PREFIX = "/live/heartbeat/client";

  /**
   * 订单信息查询.
   */
  @PostMapping("/create")
  ResultResponse<Void> create(
      @RequestBody @Validated LiveHeartbeatAddCmd qryCmd);



}
