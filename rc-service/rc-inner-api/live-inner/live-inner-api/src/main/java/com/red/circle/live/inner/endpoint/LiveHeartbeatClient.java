package com.red.circle.live.inner.endpoint;

import com.red.circle.common.business.core.constant.ServiceNameConstant;
import com.red.circle.live.inner.endpoint.api.LiveHeartbeatApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 房间用户心动值
 */
//@FeignClient(contextId = "liveHeartbeatClient",
//    name = ServiceNameConstant.SERVICE_LIVE,
//    path = LiveHeartbeatApi.API_PREFIX)
public interface LiveHeartbeatClient extends LiveHeartbeatApi{

}
