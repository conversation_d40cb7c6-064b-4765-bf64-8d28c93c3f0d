<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <artifactId>live-inner-api</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>com.red.circle</groupId>
      <artifactId>live-inner-model</artifactId>
    </dependency>
  </dependencies>


  <parent>
    <artifactId>live-inner</artifactId>
    <groupId>com.red.circle</groupId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>

</project>
