package com.red.circle.auth.inner.model.dto.activity.rule;

import com.red.circle.tool.core.json.JacksonUtils;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 爆水晶游戏规则.
 *
 * <AUTHOR> on 2021/7/1
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GameBurstCrystalContent implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 等级.
   */
  private Integer level;

  /**
   * 里程碑.
   */
  private BigDecimal milestone;

  /**
   * 小号图标.
   */
  private String smallIcon;

  /**
   * 中号图标.
   */
  private String mediumIcon;

  /**
   * 动画资源图.
   */
  private String sourceUrl;

  public static GameBurstCrystalContent serializable(String content) {
    return JacksonUtils.readValue(content, GameBurstCrystalContent.class);
  }

}
