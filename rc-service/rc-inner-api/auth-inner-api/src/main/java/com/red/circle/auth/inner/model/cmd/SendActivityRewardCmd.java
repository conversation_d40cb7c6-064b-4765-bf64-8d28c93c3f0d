package com.red.circle.auth.inner.model.cmd;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.SendPropsOrigin;
import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2023/6/5
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class SendActivityRewardCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 跟踪id.
   */
  @NotNull
  @JsonSerialize(using = ToStringSerializer.class)
  private Long trackId;

  /**
   * 来源.
   */
  @NotNull
  private SendPropsOrigin origin;

  /**
   * 来源系统.
   */
  @NotNull
  private SysOriginPlatformEnum sysOrigin;

  /**
   * 道具资源组id.
   */
  @NotNull
  @JsonSerialize(using = ToStringSerializer.class)
  private Long sourceGroupId;

  /**
   * 接收用户id.
   */
  @NotNull
  @JsonSerialize(using = ToStringSerializer.class)
  private Long acceptUserId;

}
