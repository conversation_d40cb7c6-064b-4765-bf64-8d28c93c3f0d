package com.red.circle.auth.inner.model.dto.activity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 活动道具奖励配置.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityRewardProps implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 分组id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long groupId;

  /**
   * 类型.
   */
  private String type;

  /**
   * 明细类型.
   */
  private String detailType;

  /**
   * 内容.
   */
  private String content;

  /**
   * 排序.
   */
  private Integer sort;

  /**
   * 封面.
   */
  private String cover;

  /**
   * 数量.
   */
  private Integer quantity;

  /**
   * 金额.
   */
  private BigDecimal amount;

  /**
   * 备注.
   */
  private String remark;

}
