package com.red.circle.auth.inner.endpoint.api;

import com.red.circle.framework.core.security.UserCredential;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 认证服务.
 *
 * <AUTHOR> on 2023/8/16
 */
public interface AuthClientApi {

  String API_PREFIX = "/auth/client";

  /**
   * 移除登录令牌.
   */
  @GetMapping("/removeToken")
  ResultResponse<Void> removeToken(@RequestParam("userId") Long userId);

  /**
   * 获取用户认证.
   */
  @GetMapping("/getUserCredential")
  ResultResponse<UserCredential> getUserCredential(@RequestParam("userId") Long userId);


  /**
   * 获取用户认证.
   */
  @GetMapping("/getUserCredentialByToken")
  ResultResponse<UserCredential> getUserCredential(@RequestParam("token") String token);


  /**
   * 创建用户新凭证(存在返回，不存在创建).
   */
  @GetMapping("/createIfAbsentUserCredentialToken")
  ResultResponse<String> createIfAbsentUserCredentialToken(@RequestParam("userId") Long userId);


}
