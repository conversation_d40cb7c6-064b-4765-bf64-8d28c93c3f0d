package com.red.circle.auth.inner.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.tool.core.json.JacksonUtils;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 道具规则.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityPropsRuleDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 系统平台.
   */
  private String sysOrigin;

  /**
   * 活动类型.
   */
  private String activityType;

  /**
   * 资源组ID.
   */
  private Long resourceGroupId;

  /**
   * 数据.
   */
  private String jsonData;

  /**
   * 排序.
   */
  private Integer sort;

  public <T> T toObject(Class<T> clazz) {
    return JacksonUtils.readValue(jsonData, clazz);
  }

}
