package com.red.circle.auth.inner.model.cmd;

import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2023/8/11
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class SendRandomCheckInCmd extends Command {

  @NotBlank(message = "sysOrigin required.")
  private String sysOrigin;

  @NotNull(message = "acceptUserId required.")
  private Long acceptUserId;

  @NotNull(message = "trackId required.")
  private Long trackId;

}
