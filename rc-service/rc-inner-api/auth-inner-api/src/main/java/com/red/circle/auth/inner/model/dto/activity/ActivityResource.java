package com.red.circle.auth.inner.model.dto.activity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 活动资源.
 *
 * <AUTHOR> on 2021/6/21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityResource implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 活动规则。
   */
  private ActivityPropsRule rule;

  /**
   * 道具组.
   */
  private ActivityPropsGroup propsGroup;

  /**
   * 活动奖励道具.
   */
  @JsonIgnore
  public List<ActivityRewardProps> getPropsGroupActivityRewardProps() {
    return this.propsGroup.getActivityRewardProps();
  }

  /**
   * 获取道具组id.
   */
  @JsonIgnore
  public Long getPropsGroupId() {
    return this.propsGroup.getId();
  }

}
