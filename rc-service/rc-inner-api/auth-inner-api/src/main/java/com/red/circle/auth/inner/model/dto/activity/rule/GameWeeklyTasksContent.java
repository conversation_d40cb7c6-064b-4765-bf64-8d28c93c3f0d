package com.red.circle.auth.inner.model.dto.activity.rule;

import com.red.circle.tool.core.json.JacksonUtils;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 每周游戏任务.
 *
 * <AUTHOR> on 2022/7/14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class GameWeeklyTasksContent implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 游戏配置ID.
   */
  private Long gameConfId;

  /**
   * 目标.
   */
  private Long target;

  public static GameWeeklyTasksContent serializable(String json) {
    return JacksonUtils.readValue(json, GameWeeklyTasksContent.class);
  }

}
