package com.red.circle.auth.inner.model.dto.activity.rule;

import com.red.circle.tool.core.json.JacksonUtils;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 公共.
 *
 * <AUTHOR> on 2023/2/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CommonContent implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 数量.
   */
  private Long quantity;

  public static CommonContent serializable(String content) {
    return JacksonUtils.readValue(content, CommonContent.class);
  }

}
