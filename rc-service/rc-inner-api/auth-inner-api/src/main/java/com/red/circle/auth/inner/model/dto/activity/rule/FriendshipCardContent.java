package com.red.circle.auth.inner.model.dto.activity.rule;

import com.red.circle.tool.core.json.JacksonUtils;
import java.io.Serializable;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户游戏关系卡内容.
 *
 * <AUTHOR> on 2022/7/11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FriendshipCardContent implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 数量.
   */
  private Long quantity;

  public static FriendshipCardContent toFriendshipCardContent(String content) {

    return Optional.ofNullable(JacksonUtils.readValue(content, FriendshipCardContent.class))
        .orElse(new FriendshipCardContent());
  }
}
