package com.red.circle.auth.inner.model.dto.activity.rule;

import com.red.circle.tool.core.json.JacksonUtils;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * cp奖励.
 *
 * <AUTHOR> on 2021/6/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class CpRewardContent implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 数量.
   */
  private BigDecimal quantity;

  public static CpRewardContent serializable(String content) {
    return JacksonUtils.readValue(content, CpRewardContent.class);
  }

}
