package com.red.circle.auth.inner.model.dto.activity.rule;

import com.red.circle.tool.core.json.JacksonUtils;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 世界杯内容.
 *
 * <AUTHOR> on 2022/11/07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WorldCupContent implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 数量.
   */
  private BigDecimal quantity;

  public static WorldCupContent serializable(String content) {
    return JacksonUtils.readValue(content, WorldCupContent.class);
  }
}
