package com.red.circle.auth.inner.model.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.FieldDefaults;

/**
 * 邀请用户注册记录.
 *
 * <AUTHOR> on 2022/9/9
 */
@Data
@Accessors(chain = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class InviteExtractRecordCountDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 唯一标识符.
   */
  String id;

  /**
   * 用户id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long userId;

  /**
   * 数量 .
   */
  @JsonSerialize(using = ToStringSerializer.class)
  Long quantity;

  /**
   * 系统
   */
  String sysOrigin;

  /**
   * 创建时间
   */
  Timestamp createTime;

  /**
   * 过期时间.
   */
  Timestamp expiredTime;

}
