package com.red.circle.auth.inner.model.dto.activity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 活动道具分组.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ActivityPropsGroup implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 名称.
   */
  private String name;

  /**
   * 0.下架 1.上架.
   */
  private Boolean shelfStatus;

  /**
   * 活动奖励道具.
   */
  private List<ActivityRewardProps> activityRewardProps;

}
