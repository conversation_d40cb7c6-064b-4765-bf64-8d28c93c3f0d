package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.ImOperationClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * im 运营数据.
 *
 * <AUTHOR> on 2023/11/16
 */
@FeignClient(name = "imOperationClient", url = "${feign.external.url}" +
        ImOperationClientApi.API_PREFIX)
public interface ImOperationClient extends ImOperationClientApi {

}
