package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.cmd.message.GroupMemberAddCmd;
import com.red.circle.external.inner.model.cmd.message.GroupMemberRemoveCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * im群成员.
 *
 * <AUTHOR> on 2023/12/28
 */
public interface ImGroupMemberClientApi {

  String API_PREFIX = "/im/group/member/client";

  /**
   * 添加群成员, 一次最多100个.
   */
  @PostMapping("/addGroupMember")
  ResultResponse<Void> addGroupMember(@RequestBody GroupMemberAddCmd cmd);


  /**
   * 移除群成员, 一次最多100个.
   */
  @PostMapping("/removeGroupMember")
  ResultResponse<Void> removeGroupMember(@RequestBody GroupMemberRemoveCmd cmd);

}
