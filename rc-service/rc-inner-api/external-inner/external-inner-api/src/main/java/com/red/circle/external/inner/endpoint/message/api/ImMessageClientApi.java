package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * im 消息服务.
 *
 * <AUTHOR> on 2023/7/12
 */
public interface ImMessageClientApi {

  String API_PREFIX = "/im-message/client";

  /**
   * 发送文本消息.
   */
  @GetMapping("/sendMessageText")
  ResultResponse<Void> sendMessageText(@RequestParam("fromAccount") String fromAccount,
      @RequestParam("toAccount") String toAccount,
      @RequestParam("text") String text);

  /**
   * 发送文本消息.
   */
  @GetMapping("/sendMessageText/long")
  ResultResponse<Void> sendMessageText(@RequestParam("fromAccount") Long fromAccount,
      @RequestParam("toAccount") Long toAccount,
      @RequestParam("text") String text);

}
