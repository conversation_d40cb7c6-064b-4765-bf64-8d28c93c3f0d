package com.red.circle.external.inner.endpoint.google.api;

import com.red.circle.external.inner.model.dto.SysTransactionLanguageDTO;
import com.red.circle.framework.dto.ResultResponse;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> on 2023/11/8
 */
public interface GoogleTransactionClientApi {

  String API_PREFIX = "/google/transaction";

  @GetMapping("/translate")
  ResultResponse<String> translate(@RequestParam("title") String title,
      @RequestParam("zhCn") String zhCn, @RequestParam("key1") String key1);

  @GetMapping("/getContentLang")
  ResultResponse<String> getContentLang(@RequestParam("content") String content);

  @PostMapping("/getTranslatedTextBatch")
  ResultResponse<Map<String, String>> getTranslatedTextBatch(
      @RequestBody SysTransactionLanguageDTO transactionLanguageDTO);
}
