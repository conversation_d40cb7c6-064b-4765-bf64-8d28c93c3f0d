package com.red.circle.external.inner.endpoint.tencent.api;

import com.red.circle.external.inner.model.cmd.message.google.GooglePushCmd;
import com.red.circle.external.inner.model.cmd.message.google.ImRoomRemoveUserCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> on  2024/3/25
 */
public interface ImClientApi {

  String API_PREFIX = "/im-trtc";

  @PostMapping("/room/remove-user")
  ResultResponse<Boolean> removeUser(@RequestBody @Validated ImRoomRemoveUserCmd cmd);

}
