package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.NewsletterClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 通讯消息服务.
 *
 * <AUTHOR> on 2023/8/15
 */
@FeignClient(name = "newsletterClient", url = "${feign.external.url}" +
        NewsletterClientApi.API_PREFIX)
public interface NewsletterClient extends NewsletterClientApi {

}
