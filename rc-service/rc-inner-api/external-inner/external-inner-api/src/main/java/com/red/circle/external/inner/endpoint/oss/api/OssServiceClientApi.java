package com.red.circle.external.inner.endpoint.oss.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * oss 服务api.
 *
 * <AUTHOR> on 2023/10/15
 */
public interface OssServiceClientApi {

  String API_PREFIX = "/oss/client";

  /**
   * sts临时令牌.
   */
  @GetMapping("/sts")
  ResultResponse<Object> sts();

  /**
   * 移除文件.
   */
  @GetMapping("/remove")
  ResultResponse<Void> remove(@RequestParam("url") String url);

  /**
   * 获取访问连接.
   */
  @GetMapping("/getAccessUrl")
  ResultResponse<String> getAccessUrl(@RequestParam("key") String key);

  /**
   * 获取视频封面图.
   *
   * @param key 文件key
   * @return 全链接
   */
  @GetMapping("/getVideoCover")
  ResultResponse<String> getVideoCover(@RequestParam("key") String key);


  /**
   * 图片处理后另存为持久化.
   *
   * @param source    原图地址
   * @param target    处理后转存地址
   * @param styleType 处理类型格式
   */
  @GetMapping("/processFileSaveAs")
  ResultResponse<Void> processFileSaveAs(@RequestParam("source") String source,
      @RequestParam("target") String target,
      @RequestParam("styleType") String styleType
  );

  /**
   * 图片处理压缩.
   *
   * @return 图片地址
   */
  @GetMapping("/processImgSaveAsCompressZoom")
  ResultResponse<String> processImgSaveAsCompressZoom(
      @RequestParam("source") String source,
      @RequestParam("target") String target,
      @RequestParam("height") Integer height
  );

  /**
   * 图片处理压缩.
   *
   * @return 图片地址
   */
  @GetMapping("/processImgSaveAsCompressZoom/simple")
  ResultResponse<String> processImgSaveAsCompressZoom(
      @RequestParam("source") String source,
      @RequestParam("height") Integer height
  );

}
