package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.cmd.message.SendSmsCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 验证码服务.
 *
 * <AUTHOR> on 2023/8/14
 */
public interface SmsClientApi {

  String API_PREFIX = "/sms/client";

  /**
   * 发送验证码.
   */
  @PostMapping("/sendCode")
  ResultResponse<Void> sendCode(@RequestBody SendSmsCmd cmd);

  /**
   * 检查验证码是否正确.
   */
  @PostMapping("/checkCode")
  ResultResponse<Boolean> checkCode(@RequestBody SendSmsCmd cmd);

}
