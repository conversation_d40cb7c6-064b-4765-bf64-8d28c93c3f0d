package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.cmd.message.RegisterAccountCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * im账号管理.
 *
 * <AUTHOR> on 2023/7/12
 */
public interface ImAccountClientApi {

  String API_PREFIX = "/im-account/client";

  /**
   * 创建用户签名.
   */
  @GetMapping("/createUserSig")
  ResultResponse<String> createUserSig(@RequestParam("userId") Long userId);

  /**
   * 使用该接口将用户登录状态失效.
   */
  @GetMapping("/kick")
  ResultResponse<Void> kick(@RequestParam("userId") Long userId);

  /**
   * 注册账号.
   */
  @PostMapping("/register")
  ResultResponse<Void> register(@RequestBody @Validated RegisterAccountCmd cmd);

  /**
   * 更新IM资料设置-头像.
   */
  @PostMapping("/portraitSetAvatar")
  ResultResponse<Void> portraitSetAvatar(
      @RequestParam("userId") Long userId,
      @RequestParam("avatar") String avatar
  );

  /**
   * 更新IM资料设置-昵称.
   */
  @PostMapping("/portraitSetNickname")
  ResultResponse<Void> portraitSetNickname(
      @RequestParam("userId") Long userId,
      @RequestParam("nickname") String nickname
  );

}
