package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.ImAccountClientApi;
import com.red.circle.external.inner.endpoint.message.api.ImGroupAvClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * im 群组服务(av) .
 *
 * <AUTHOR> on 2023/10/7
 */
@FeignClient(name = "imGroupAvClient", url = "${feign.external.url}" +
        ImAccountClientApi.API_PREFIX)
public interface ImGroupAvClient extends ImGroupAvClientApi {

}
