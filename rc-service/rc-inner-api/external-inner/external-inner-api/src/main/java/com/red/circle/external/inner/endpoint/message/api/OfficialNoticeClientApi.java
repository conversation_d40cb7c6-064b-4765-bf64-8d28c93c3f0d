package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.cmd.message.notice.official.NoticeExtTemplateCustomizeCmd;
import com.red.circle.external.inner.model.cmd.message.notice.official.NoticeExtTemplateTypeCmd;
import com.red.circle.external.inner.model.enums.message.OfficialNoticeTypeEnum;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 官方通知.
 *
 * <AUTHOR> on 2023/7/12
 */
public interface OfficialNoticeClientApi {

  String API_PREFIX = "/official-notice/client";

  /**
   * 发送官方通知(自定义).
   */
  @PostMapping("/send/customize/template")
  ResultResponse<Void> send(@RequestBody @Validated NoticeExtTemplateCustomizeCmd cmd);

  /**
   * 发送官方通知(类型).
   */
  @PostMapping("/send/type/template")
  ResultResponse<Void> send(@RequestBody @Validated NoticeExtTemplateTypeCmd cmd);

  /**
   * 发送官方通知.
   */
  @GetMapping("/send/lang/simple-template")
  ResultResponse<Void> send(
      @RequestParam("toAccount") Long toAccount,
      @RequestParam("langCode") String langCode,
      @RequestParam("officialNoticeTypeEnum") OfficialNoticeTypeEnum officialNoticeTypeEnum
  );

  /**
   * 发送官方通知.
   */
  @GetMapping("/send/simple-template")
  ResultResponse<Void> send(
      @RequestParam("toAccount") Long toAccount,
      @RequestParam("officialNoticeTypeEnum") OfficialNoticeTypeEnum officialNoticeTypeEnum
  );

}
