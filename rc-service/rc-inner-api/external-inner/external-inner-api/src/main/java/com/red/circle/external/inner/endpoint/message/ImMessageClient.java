package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.ImMessageClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * im 消息服务.
 *
 * <AUTHOR> on 2023/7/12
 */
@FeignClient(name = "imMessageClient", url = "${feign.external.url}" +
        ImMessageClientApi.API_PREFIX)
public interface ImMessageClient extends ImMessageClientApi {

}
