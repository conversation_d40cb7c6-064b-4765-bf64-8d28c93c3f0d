package com.red.circle.external.inner.endpoint.push;

import com.red.circle.external.inner.endpoint.push.api.GoogleFirebasePushClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on 2023/11/8
 */
@FeignClient(name = "pushSendClient", url = "${feign.external.url}" +
        GoogleFirebasePushClientApi.API_PREFIX)
public interface GoogleFirebasePushClient extends GoogleFirebasePushClientApi {

}
