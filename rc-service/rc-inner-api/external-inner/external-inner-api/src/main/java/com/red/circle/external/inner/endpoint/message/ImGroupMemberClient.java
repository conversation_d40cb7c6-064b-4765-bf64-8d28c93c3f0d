package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.ImGroupMemberClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on 2023/12/28
 */
@FeignClient(name = "imGroupMemberClient", url = "${feign.external.url}" +
        ImGroupMemberClientApi.API_PREFIX)
public interface ImGroupMemberClient extends ImGroupMemberClientApi {

}
