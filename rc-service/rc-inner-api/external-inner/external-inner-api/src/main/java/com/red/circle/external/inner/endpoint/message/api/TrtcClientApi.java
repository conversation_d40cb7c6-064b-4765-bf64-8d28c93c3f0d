package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.cmd.message.TrtcRemoveUserCmd;
import com.red.circle.external.inner.model.cmd.message.TrtcUserBlockedCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * trtc 服务.
 *
 * <AUTHOR> on 2023/10/17
 */
public interface TrtcClientApi {

  String API_PREFIX = "/trtc/client";

  /**
   * 接口说明：将用户从房间移出，适用于主播/房主/管理员踢人等场景。支持所有平台，Android、iOS、Windows 和 macOS 需升级到 TRTC SDK 6.6及以上版本.
   *
   * @return true 成功 false 是吧
   */
  @PostMapping("/removeUser")
  ResultResponse<Boolean> removeUser(@RequestBody @Validated TrtcRemoveUserCmd cmd);

  /**
   * 此接⼝被设置为 不对外公开。 通过云API设置mute/unmute状态，适⽤于主播/房主/管理员等通过后台禁⾔/解禁⾔某个⽤户的场景。⽀持所有平 台，Android、iOS、Windows
   * 、 macOS 、WebSDK及⼩程序。注：当房间号为数字类型时请使⽤该API.
   *
   * @return true 成功， false 失败
   */
  @PostMapping("/setUserBlocked")
  ResultResponse<Boolean> setUserBlocked(@RequestBody @Validated TrtcUserBlockedCmd cmd);

  /**
   * 解散房间.
   */
  @GetMapping("/dismissRoom")
  ResultResponse<Boolean> dismissRoom(@RequestParam("roomId") String roomId);

  /**
   * 创建声网令牌.
   */
  @GetMapping("/createAgoraToken")
  ResultResponse<String> createAgoraToken(
      @RequestParam("userId") Long userId,
      @RequestParam("type") String channelName
  );

  @GetMapping("/createAgoraTokenGoUp")
  ResultResponse<String> createAgoraTokenGoUp(
          @RequestParam("userId") Long userId,
          @RequestParam("type") String channelName
  );

}
