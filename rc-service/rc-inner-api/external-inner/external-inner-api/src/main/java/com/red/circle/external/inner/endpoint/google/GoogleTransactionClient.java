package com.red.circle.external.inner.endpoint.google;

import com.red.circle.external.inner.endpoint.google.api.GoogleTransactionClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on 2023/11/8
 */
@FeignClient(name = "googleTransactionClient", url = "${feign.external.url}" +
        GoogleTransactionClientApi.API_PREFIX)
public interface GoogleTransactionClient extends GoogleTransactionClientApi {

}
