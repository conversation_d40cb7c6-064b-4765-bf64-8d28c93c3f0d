package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 声网.
 *
 * <AUTHOR> on 2023/7/12
 */
public interface AgoraClientApi {

    String API_PREFIX = "/agora/client";

    /**
     * 创建规则
     * @param cname
     * @param uid
     * @return
     */
    @GetMapping("/kicking/rule")
    ResultResponse<Void> kickingRule(@RequestParam("cname") String cname, @RequestParam("uid") String uid);

    /**
     * 查询用户列表
     * @param cname
     * @return
     */
    @GetMapping("/channel/user")
    ResultResponse<List<Long>> channelUser(@RequestParam("cname") String cname);
}
