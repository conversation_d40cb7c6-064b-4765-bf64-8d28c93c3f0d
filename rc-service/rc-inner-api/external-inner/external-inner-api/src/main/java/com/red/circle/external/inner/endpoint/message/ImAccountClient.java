package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.ImAccountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * im账号管理.
 *
 * <AUTHOR> on 2023/7/12
 */
@FeignClient(name = "imAccountClient", url = "${feign.external.url}" +
        ImAccountClientApi.API_PREFIX)
public interface ImAccountClient extends ImAccountClientApi {

}
