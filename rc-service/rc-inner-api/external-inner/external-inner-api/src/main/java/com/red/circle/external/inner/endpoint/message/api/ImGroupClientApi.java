package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.cmd.message.BroadcastGroupMsgBodyCmd;
import com.red.circle.external.inner.model.cmd.message.CreateGroupAddMemberCmd;
import com.red.circle.external.inner.model.cmd.message.CreateGroupCmd;
import com.red.circle.external.inner.model.cmd.message.CustomGroupMsgBodyCmd;
import com.red.circle.external.inner.model.cmd.message.DismissGroupCmd;
import com.red.circle.external.inner.model.cmd.message.GroupBanCmd;
import com.red.circle.external.inner.model.cmd.message.GroupUnbanCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * im 群组服务.
 *
 * <AUTHOR> on 2023/8/16
 */
public interface ImGroupClientApi {

  String API_PREFIX = "/im-group/client";

  /**
   * 群广播通知，通知指定群（自定义消息，内部通讯.
   */
  @PostMapping("/sendMessageBroadcast")
  ResultResponse<Void> sendMessageBroadcast(@RequestBody @Validated BroadcastGroupMsgBodyCmd cmd);

  /**
   * 发送自定义消息.
   */
  @PostMapping("/sendCustomMessage")
  ResultResponse<Void> sendCustomMessage(
      @RequestParam("groupId") String groupId,
      @RequestBody @Validated CustomGroupMsgBodyCmd cmd);

  /**
   * 群成员封禁.
   *
   * @return true 成功，false 失败.
   */
  @PostMapping("/banGroupMember")
  ResultResponse<Boolean> banGroupMember(@RequestBody @Validated GroupBanCmd cmd);

  /**
   * 解封成员封禁.
   *
   * @return true 成功，false 失败.
   */
  @PostMapping("/unBanGroupMember")
  ResultResponse<Boolean> unBanGroupMember(@RequestBody @Validated GroupUnbanCmd cmd);

  /**
   * 创建群.
   */
  @PostMapping("/createPublicGroup")
  ResultResponse<Boolean> createPublicGroup(@RequestBody @Validated CreateGroupCmd cmd);

  /**
   * 创建群.
   */
  @PostMapping("/createAVChatRoomGroup")
  ResultResponse<Boolean> createAVChatRoomGroup(@RequestBody @Validated CreateGroupCmd cmd);

  /**
   * 创建群.
   */
  @PostMapping("/addGroupMembers")
  ResultResponse<Boolean> addGroupMembers(@RequestBody @Validated CreateGroupAddMemberCmd cmd);

  /**
   * 解散群.
   */
  @PostMapping("/destroyGroup")
  ResultResponse<Void> destroyGroup(@RequestBody @Validated DismissGroupCmd cmd);

}
