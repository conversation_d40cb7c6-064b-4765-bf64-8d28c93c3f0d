package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.dto.OperationActiveIndexDTO;
import com.red.circle.framework.dto.ResultResponse;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * im 运营数据.
 *
 * <AUTHOR> on 2023/11/16
 */
public interface ImOperationClientApi {

  String API_PREFIX = "/im-operation/client";

  /**
   * 获取最近30天运营数据.
   */
  @GetMapping("/getLatest30Days")
  ResultResponse<List<OperationActiveIndexDTO>> getLatest30Days();

}
