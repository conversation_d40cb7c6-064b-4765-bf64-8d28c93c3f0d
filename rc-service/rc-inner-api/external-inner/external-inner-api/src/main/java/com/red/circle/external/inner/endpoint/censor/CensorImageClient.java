package com.red.circle.external.inner.endpoint.censor;

import com.red.circle.external.inner.endpoint.censor.api.CensorImageClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 图片安全鉴定.
 *
 * <AUTHOR> on 2023/10/17
 */
@FeignClient(name = "censorImageClient", url = "${feign.external.url}" +
        CensorImageClientApi.API_PREFIX)
public interface CensorImageClient extends CensorImageClientApi {

}
