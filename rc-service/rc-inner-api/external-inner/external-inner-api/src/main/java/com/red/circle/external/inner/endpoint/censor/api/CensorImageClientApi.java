package com.red.circle.external.inner.endpoint.censor.api;

import com.red.circle.external.inner.model.dto.CensorImageResponseDTO;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 图片安全鉴定.
 *
 * <AUTHOR> on 2023/10/17
 */
public interface CensorImageClientApi {

  String API_PREFIX = "/censor-image/client";

  /**
   * 鉴定.
   *
   * @param detectUrl 被鉴定的图片url
   */
  @GetMapping("/auditing")
  ResultResponse<CensorImageResponseDTO> auditing(@RequestParam("detectUrl") String detectUrl);

}
