package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * im 群组服务(av).
 *
 * <AUTHOR> on 2023/10/7
 */
public interface ImGroupAvClientApi {

  String API_PREFIX = "/im-group-av/client";


  /**
   * 获取av group 在线成员数量.
   *
   * @param groupId 群id
   * @return ignore
   */
  @GetMapping("/getAvRoomOnlineMemberNum")
  ResultResponse<Long> getOnlineMemberNum(@RequestParam("groupId") String groupId);

}
