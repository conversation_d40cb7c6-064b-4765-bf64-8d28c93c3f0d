package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.ImGroupClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * im 群组服务.
 *
 * <AUTHOR> on 2023/8/16
 */
@FeignClient(name = "imGroupClient", url = "${feign.external.url}" +
        ImGroupClientApi.API_PREFIX)
public interface ImGroupClient extends ImGroupClientApi {

}
