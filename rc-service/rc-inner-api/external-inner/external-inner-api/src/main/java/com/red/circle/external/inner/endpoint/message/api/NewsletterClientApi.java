package com.red.circle.external.inner.endpoint.message.api;

import com.red.circle.external.inner.model.cmd.message.NewsletterResultCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 通讯消息服务.
 *
 * <AUTHOR> on 2023/8/15
 */
public interface NewsletterClientApi {

  String API_PREFIX = "/newsletter/client";

  /**
   * 发送通讯消息.
   */
  @PostMapping("/send")
  ResultResponse<Void> send(@RequestBody @Validated NewsletterResultCmd cmd);

}
