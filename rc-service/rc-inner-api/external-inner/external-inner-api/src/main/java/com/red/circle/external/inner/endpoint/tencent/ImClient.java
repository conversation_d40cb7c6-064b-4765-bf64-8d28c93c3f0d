package com.red.circle.external.inner.endpoint.tencent;

import com.red.circle.common.business.core.constant.ServiceNameConstant;
import com.red.circle.external.inner.endpoint.push.api.GoogleFirebasePushClientApi;
import com.red.circle.external.inner.endpoint.tencent.api.ImClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on  2024/3/25
 */
@FeignClient(name = "ImClient", url = "${feign.external.url}" + ImClientApi.API_PREFIX)
public interface ImClient extends ImClientApi {

}
