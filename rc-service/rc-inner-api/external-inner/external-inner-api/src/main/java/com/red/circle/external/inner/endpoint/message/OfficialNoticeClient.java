package com.red.circle.external.inner.endpoint.message;

import com.red.circle.external.inner.endpoint.message.api.OfficialNoticeClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 官方通知.
 *
 * <AUTHOR> on 2023/7/12
 */
@FeignClient(name = "officialNoticeClient", url = "${feign.external.url}" +
        OfficialNoticeClientApi.API_PREFIX)
public interface OfficialNoticeClient extends OfficialNoticeClientApi {

}
