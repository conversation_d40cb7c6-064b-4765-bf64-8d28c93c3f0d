package com.red.circle.external.inner.endpoint.push.api;


import com.red.circle.external.inner.model.cmd.message.google.GooglePushCmd;
import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> on 2023/11/8
 */
public interface GoogleFirebasePushClientApi {

  String API_PREFIX = "/push-send/client";

  @PostMapping("/broadcast")
  ResultResponse<Void> broadcast(@RequestBody GooglePushCmd cmd);
}
