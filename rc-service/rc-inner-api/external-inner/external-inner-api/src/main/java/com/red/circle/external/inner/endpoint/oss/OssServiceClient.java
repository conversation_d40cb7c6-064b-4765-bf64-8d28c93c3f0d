package com.red.circle.external.inner.endpoint.oss;

import com.red.circle.external.inner.endpoint.oss.api.OssServiceClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * oss 服务.
 *
 * <AUTHOR> on 2023/10/15
 */
@FeignClient(name = "ossServiceClient", url = "${feign.external.url}" +
        OssServiceClientApi.API_PREFIX)
public interface OssServiceClient extends OssServiceClientApi {

}
