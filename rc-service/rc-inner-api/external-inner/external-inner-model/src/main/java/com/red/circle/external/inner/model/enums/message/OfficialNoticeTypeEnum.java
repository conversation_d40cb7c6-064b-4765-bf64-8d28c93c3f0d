package com.red.circle.external.inner.model.enums.message;

import com.red.circle.component.core.i18n.I18nMessageUtils;
import java.util.Locale;

/**
 * .
 * <a href="https://www.tapd.cn/********/markdown_wikis/show/#11********001002683">官方通知类型</a>
 *
 * <AUTHOR> on 2020/2/7 10:00
 */
public enum OfficialNoticeTypeEnum {

  /**
   * 文本类型通知.
   */
  REGISTER_WELCOME_MESSAGE,

  /**
   * 举报奖励通知.
   */
  REPORT_REWARD,

  /**
   * 邀请好友已注册通知.
   */
  @Deprecated
  INVITE_FRIENDS_REGISTER,

  /**
   * 照片墙违规.
   */
  PHOTO_WALL_VIOLATION,

  /**
   * 头像违规.
   */
  AVATAR_VIOLATION,

  /**
   * 账号冻结.
   */
  ACCOUNT_FREEZE,

  /**
   * 解冻账号.
   */
  RELIEVE_ACCOUNT_FREEZE,

  /**
   * 昵称审核不通过.
   */
  NICKNAME_CHECK_FAIL,

  /**
   * 团队解散(经纪人).
   */
  @Deprecated
  TEAM_DISBAND_ORGANIZER,

  /**
   * 团队解散(团队成员).
   */
  @Deprecated
  TEAM_DISBAND_MEMBER,

  /**
   * 邀请加入团队.
   */
  @Deprecated
  INVITE_JOIN_TEAM,

  /**
   * 警告.
   */
  USER_WARNING,

  /**
   * 踢出团队.
   */
  @Deprecated
  OUT_TEAM,

  /**
   * 经纪人积分结算完成.
   */
  @Deprecated
  CACOO_INTEGRAL_SETTLE,

  /**
   * 房间自定义主题审批中.
   */
  ROOM_THEME_PENDING,

  /**
   * 房间自定义主题审批通过.
   */
  ROOM_THEME_PASS,

  /**
   * 房间自定义主题审批不通过.
   */
  ROOM_THEME_NOT_PASS,

  /**
   * 赠送道具.
   */
  GIVE_AWAY_PROPS,

  /**
   * 徽章激活.
   */
  BADGE_ACTIVATION,

  /**
   * cp关系解除.
   */
  CP_DISMISS,

  /**
   * 接收贵族vip.
   */
  ACCEPT_NOBLE_VIP,

  /**
   * 意见反馈.
   */
  FEEDBACK_PROCESS,

  /**
   * 退款扣除目标.
   */
  REFUND_DEDUCTION_TARGET,

  /**
   * 用户拒绝BD成为代理邀请.
   */
  REFUSE_BD_INVITE_AGENT,

  /**
   * 用户同意BD成为代理邀请.
   */
  AGREE_BD_INVITE_AGENT,

  /**
   * BD发出代理邀请.
   */
  BD_SEND_INVITE_AGENT,

  /**
   * BD LEADER发出BD邀请.
   */
  BD_LEADER_SEND_INVITE_BD,

  /**
   * 用户拒绝BD LEADER成为BD邀请.
   */
  REFUSE_BD_LEADER_INVITE_AGENT,

  /**
   * 用户同意BD LEADER成为BD邀请.
   */
  AGREE_BD_LEADER_INVITE_AGENT,

  /**
   * 用户申请加入团队发出红点消息.
   */
  USER_SEND_APPLY_TEAM,

  /**
   * 用户赠送cp祝福礼物
   */
  CP_BLESS,

  /**
   * 用户告白
   */
  USER_CONFESSION;


  public String getTitle(Locale locale) {
    return I18nMessageUtils.get(getProperty("title"), locale);
  }

  public String getBody(Locale locale) {
    return I18nMessageUtils.get(getProperty("body"), locale);
  }

  public String getTileFormat(Locale locale, Object param) {
    return I18nMessageUtils.getReplace(getProperty("title"), locale, param);
  }

  public String getBodyFormat(Locale locale, Object param) {
    return I18nMessageUtils.getReplace(getProperty("body"), locale, param);
  }

  private String getProperty(String property) {
    return this.name().concat(".").concat(property);
  }
}
