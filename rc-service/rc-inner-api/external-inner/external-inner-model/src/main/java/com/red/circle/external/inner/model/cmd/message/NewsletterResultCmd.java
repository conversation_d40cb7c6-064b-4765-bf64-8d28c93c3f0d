package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.external.inner.model.enums.message.NewsletterEvent;
import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2020/4/6 11:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class NewsletterResultCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 通知用户id.
   */
  @NotNull(message = "userIds required.")
  List<Long> userIds;

  /**
   * 通知事件.
   */
  NewsletterEvent event;

  /**
   * 数据.
   */
  Object data;

}
