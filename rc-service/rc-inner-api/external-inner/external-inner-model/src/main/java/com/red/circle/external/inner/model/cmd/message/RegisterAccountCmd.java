package com.red.circle.external.inner.model.cmd.message;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 注册账号.
 *
 * <AUTHOR> on 2023/7/12
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RegisterAccountCmd extends Command {

  /**
   * 头像.
   */
  private String avatar;

  /**
   * 用户名，长度不超过32字节.
   */
  @NotNull
  @JsonSerialize(using = ToStringSerializer.class)
  private Long identifier;

  /**
   * 昵称.
   */
  @NotBlank
  private String nickname;

  /**
   * 用户性别(0女，1男).
   */
  private Integer gender;

  /**
   * 语言code.
   */
  private String lang;
}
