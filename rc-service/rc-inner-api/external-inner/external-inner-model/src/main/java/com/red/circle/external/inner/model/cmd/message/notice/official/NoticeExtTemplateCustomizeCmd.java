package com.red.circle.external.inner.model.cmd.message.notice.official;

import com.red.circle.external.inner.model.enums.message.OfficialNoticeTypeEnum;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 通知扩展，模版参数(自定义参数).
 *
 * <AUTHOR> on 2023/10/23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class NoticeExtTemplateCustomizeCmd extends NoticeExtTemplateAbstract {

  /**
   * 标题
   */
  private String title;

  /**
   * 内容
   */
  private String content;


  public static NoticeExtTemplateCustomizeBuilder builder() {
    return new NoticeExtTemplateCustomizeBuilder();
  }

  public static class NoticeExtTemplateCustomizeBuilder {

    private Long id;
    private final List<Long> toAccounts = new ArrayList<>();
    private String langCode;
    private String noticeType;
    private Object expand;
    private String title;
    private String content;


    public NoticeExtTemplateCustomizeBuilder id(Long id) {
      this.id = id;
      return this;
    }

    public NoticeExtTemplateCustomizeBuilder toAccount(Long account) {
      this.toAccounts.add(account);
      return this;
    }

    public NoticeExtTemplateCustomizeBuilder toAccounts(List<Long> accounts) {
      this.toAccounts.addAll(accounts);
      return this;
    }

    public NoticeExtTemplateCustomizeBuilder langCode(String langCode) {
      this.langCode = langCode;
      return this;
    }

    public NoticeExtTemplateCustomizeBuilder noticeType(
        OfficialNoticeTypeEnum noticeType) {
      this.noticeType = noticeType.name();
      return this;
    }

    public NoticeExtTemplateCustomizeBuilder expand(Object expand) {
      this.expand = expand;
      return this;
    }

    public NoticeExtTemplateCustomizeBuilder title(String title) {
      this.title = title;
      return this;
    }

    public NoticeExtTemplateCustomizeBuilder content(String content) {
      this.content = content;
      return this;
    }

    public NoticeExtTemplateCustomizeCmd build() {
      NoticeExtTemplateCustomizeCmd cmd = new NoticeExtTemplateCustomizeCmd();
      cmd.setId(this.id);
      cmd.setToAccounts(this.toAccounts);
      cmd.setLangCode(this.langCode);
      cmd.setNoticeType(this.noticeType);
      cmd.setExpand(this.expand);
      cmd.setTitle(this.title);
      cmd.setContent(this.content);
      return cmd;
    }

  }
}
