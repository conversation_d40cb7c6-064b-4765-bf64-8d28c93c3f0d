package com.red.circle.external.inner.model.cmd.message.notice.official;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> on 2023/10/23
 */
@Data
public abstract class NoticeExtTemplateAbstract {

  /**
   * 消息id
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 通知用户.
   */
  @NotNull(message = "toAccounts required.")
  private List<Long> toAccounts;

  /**
   * 语言(默认查询用户当前登记语言).
   */
  private String langCode;

  /**
   * 通知消息类型
   */
  @NotBlank(message = "noticeType required.")
  private String noticeType;

  /**
   * 扩展对象
   */
  private Object expand;

}
