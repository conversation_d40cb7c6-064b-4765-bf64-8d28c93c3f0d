package com.red.circle.external.inner.model.dto;

import com.red.circle.framework.dto.DTO;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2023/11/10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SysTransactionLanguageDTO extends DTO {

  /**
   * 标题.
   */
  private String title;

  /**
   * 地区.
   */
  private String zhCn;

  /**
   * 数据.
   */
  private Map<String, String> language;


}
