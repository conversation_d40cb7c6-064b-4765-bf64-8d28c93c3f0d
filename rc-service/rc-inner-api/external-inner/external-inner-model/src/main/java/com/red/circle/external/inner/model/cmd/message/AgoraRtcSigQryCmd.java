package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2023/12/15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AgoraRtcSigQryCmd extends AppExtCommand {

  /**
   * 频道号.
   *
   * @eo.required
   */
  @NotNull(message = "channelName required")
  private String channelName;

}
