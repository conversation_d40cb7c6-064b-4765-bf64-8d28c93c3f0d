package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.external.inner.model.enums.message.GroupMessageTypeEnum;
import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> on 2021/6/16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomGroupMsgBodyCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 消息类型.
   */
  @NotNull(message = "type required.")
  private String type;

  /**
   * 数据结构.
   */
  private Object data;

  public static GroupMessageBodyBuilder builder() {
    return new GroupMessageBodyBuilder();
  }

  public static class GroupMessageBodyBuilder {

    private String type;
    private Object data;


    public GroupMessageBodyBuilder type(GroupMessageTypeEnum type) {
      this.type = type.name();
      return this;
    }

    public GroupMessageBodyBuilder type(String type) {
      this.type = type;
      return this;
    }

    public GroupMessageBodyBuilder data(Object data) {
      this.data = data;
      return this;
    }

    public CustomGroupMsgBodyCmd build() {
      CustomGroupMsgBodyCmd cmd = new CustomGroupMsgBodyCmd();
      cmd.setType(this.type);
      cmd.setData(this.data);
      return cmd;
    }

  }


}
