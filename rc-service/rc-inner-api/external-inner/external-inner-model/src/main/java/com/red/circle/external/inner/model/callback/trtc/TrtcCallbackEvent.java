package com.red.circle.external.inner.model.callback.trtc;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;
import lombok.Data;

/**
 * trtc 回调事件.
 *
 * <AUTHOR> on 2021/7/24
 */
@Data
public class TrtcCallbackEvent implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 事件组 ID.
   */
  @JsonProperty("EventGroupId")
  private Long eventGroupId;

  /**
   * 回调通知的事件类型.
   */
  @JsonProperty("EventType")
  private Integer eventType;

  /**
   * 事件回调服务器向您的服务器发出回调请求的 Unix 时间戳，单位为毫秒.
   */
  @JsonProperty("CallbackTs")
  private Long callbackTs;

  /**
   * 事件信息.
   */
  @JsonProperty("EventInfo")
  private EventInfo eventInfo;

  /**
   * 退出房间不等于超时的.
   */
  @JsonIgnore
  public boolean isReasonExitNotLeaveOvertime() {
    return Objects.equals(EventType.EVENT_TYPE_EXIT_ROOM.getValue(), eventType) && eventInfo
        .isReasonNotLeaveOvertime();
  }

  /**
   * 退出房间不等于超时的.
   */
  @JsonIgnore
  public boolean isReasonKill() {
    return Objects.equals(EventType.EVENT_TYPE_EXIT_ROOM.getValue(), eventType) && eventInfo
        .isReasonKill();
  }

  @JsonIgnore
  public String getEventTypeName() {
    return Arrays.stream(EventType.values())
        .filter(eventTypes -> Objects.equals(eventTypes.getValue(), eventType))
        .findFirst().map(EventType::name)
        .orElseThrow(() -> new IllegalArgumentException("param error."));
  }

  @JsonIgnore
  public String getRoomId() {
    return getEventInfo().getRoomId();
  }

  @JsonIgnore
  public Long getRoomIdLongValve() {
    return Long.valueOf(getEventInfo().getRoomId());
  }

  @JsonIgnore
  public Long getUserIdLongValue() {
    return Long.valueOf(getEventInfo().getUserId());
  }

  @JsonIgnore
  public String getTrtcEventKey() {
    return "TRTC_CB:".concat(eventInfo.getRoomId()) + "_".concat(eventInfo.getEventTs());
  }

  enum EventGroupId {
    /**
     * 房间事件组.
     */
    EVENT_GROUP_ROOM(1),

    /**
     * 媒体事件组.
     */
    EVENT_GROUP_MEDIA(2);

    private final int value;

    EventGroupId(int value) {
      this.value = value;
    }

    public int getValue() {
      return value;
    }
  }

  enum EventType {

    /**
     * 创建房间.
     */
    EVENT_TYPE_CREATE_ROOM(101),

    /**
     * 解散房间.
     */
    EVENT_TYPE_DISMISS_ROOM(102),

    /**
     * 进入房间.
     */
    EVENT_TYPE_ENTER_ROOM(103),

    /**
     * 退出房间.
     */
    EVENT_TYPE_EXIT_ROOM(104),

    /**
     * 切换角色.
     */
    EVENT_TYPE_CHANGE_ROLE(105);

    private final int value;

    EventType(int value) {
      this.value = value;
    }

    public int getValue() {
      return value;
    }
  }
}
