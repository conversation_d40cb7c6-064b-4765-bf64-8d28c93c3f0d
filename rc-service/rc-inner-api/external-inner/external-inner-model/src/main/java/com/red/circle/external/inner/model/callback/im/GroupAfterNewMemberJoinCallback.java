package com.red.circle.external.inner.model.callback.im;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新成员入群之后回调.
 *
 * <AUTHOR> on 2021/7/24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupAfterNewMemberJoinCallback extends CallbackCommandAbstract {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 要将其他用户拉入的群组 ID.
   */
  @JsonProperty("GroupId")
  private String groupId;

  /**
   * 请求创建的 群组类型介绍，例如 Public
   */
  @JsonProperty("Type")
  private String type;

  /**
   * 入群方式：Apply（申请入群）；Invited（邀请入群）.
   */
  @JsonProperty("JoinType")
  private String joinType;

  /**
   * 请求的操作者 UserID.
   */
  @JsonProperty("Operator_Account")
  private String operatorAccount;

  /**
   * 新入群成员 UserID 集合.
   */
  @JsonProperty("NewMemberList")
  private List<MemberProfile> newMemberList;

}
