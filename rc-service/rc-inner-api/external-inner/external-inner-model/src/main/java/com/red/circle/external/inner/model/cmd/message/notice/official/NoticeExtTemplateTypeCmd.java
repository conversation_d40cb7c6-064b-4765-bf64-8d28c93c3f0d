package com.red.circle.external.inner.model.cmd.message.notice.official;

import com.red.circle.external.inner.model.enums.message.OfficialNoticeTypeEnum;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 通知扩展，模版参数(类型参数).
 *
 * <AUTHOR> on 2023/10/23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class NoticeExtTemplateTypeCmd extends NoticeExtTemplateAbstract {

  /**
   * 模版参数.
   */
  private Object templateParam;

  public static NoticeExtTemplateTypeCmdBuilder builder() {
    return new NoticeExtTemplateTypeCmdBuilder();
  }

  public static class NoticeExtTemplateTypeCmdBuilder {

    private Long id;
    private final List<Long> toAccounts = new ArrayList<>();
    private String langCode;
    private String noticeType;
    private Object expand;
    private Object templateParam;


    public NoticeExtTemplateTypeCmdBuilder id(Long id) {
      this.id = id;
      return this;
    }

    public NoticeExtTemplateTypeCmdBuilder toAccount(Long account) {
      this.toAccounts.add(account);
      return this;
    }

    public NoticeExtTemplateTypeCmdBuilder toAccounts(List<Long> accounts) {
      this.toAccounts.addAll(accounts);
      return this;
    }

    public NoticeExtTemplateTypeCmdBuilder langCode(String langCode) {
      this.langCode = langCode;
      return this;
    }

    public NoticeExtTemplateTypeCmdBuilder noticeType(OfficialNoticeTypeEnum noticeType) {
      this.noticeType = noticeType.name();
      return this;
    }

    public NoticeExtTemplateTypeCmdBuilder expand(Object expand) {
      this.expand = expand;
      return this;
    }

    public NoticeExtTemplateTypeCmdBuilder templateParam(Object templateParam) {
      this.templateParam = templateParam;
      return this;
    }

    public NoticeExtTemplateTypeCmd build() {
      NoticeExtTemplateTypeCmd cmd = new NoticeExtTemplateTypeCmd();
      cmd.setId(this.id);
      cmd.setToAccounts(this.toAccounts);
      cmd.setLangCode(this.langCode);
      cmd.setNoticeType(this.noticeType);
      cmd.setExpand(this.expand);
      cmd.setTemplateParam(this.templateParam);
      return cmd;
    }

  }
}
