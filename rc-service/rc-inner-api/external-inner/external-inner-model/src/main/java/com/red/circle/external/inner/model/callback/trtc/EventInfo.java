package com.red.circle.external.inner.model.callback.trtc;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;

/**
 * 事件信息.
 *
 * <AUTHOR> on 2021/7/24
 */
@Data
public class EventInfo implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 房间名（类型与客户端房间号类型一致）.
   */
  @JsonProperty("RoomId")
  private String roomId;

  /**
   * 事件发生的 Unix 时间戳，单位为秒.
   */
  @JsonProperty("EventTs")
  private String eventTs;

  /**
   * 用户 ID。
   */
  @JsonProperty("UserId")
  private String userId;

  /**
   * 唯一标识符（option：房间事件组携带）.
   */
  @JsonProperty("UniqueId")
  private Long uniqueId;

  /**
   * 角色类型（option：进退房时携带）.
   * <p>MEMBER_TRTC_ANCHOR	20	主播</p>
   * <p>MEMBER_TRTC_VIEWER	21 观众</p>
   */
  @JsonProperty("Role")
  private Long role;

  /**
   * 终端类型（option：进房时携带）.
   * <p>TERMINAL_TYPE_WINDOWS	1	Windows 端</p>
   * <p>TERMINAL_TYPE_ANDROID	2	Android 端</p>
   * <p>TERMINAL_TYPE_IOS	3	iOS 端</p>
   * <p>TERMINAL_TYPE_LINUX	4	Linux 端</p>
   * <p>TERMINAL_TYPE_OTHER	100	其他</p>
   */
  @JsonProperty("TerminalType")
  private Integer terminalType;

  /**
   * 用户类型（option：进房时携带）.
   * <p>USER_TYPE_WEBRTC	1	webrtc</p>
   * <p>USER_TYPE_APPLET	2	小程序</p>
   * <p>USER_TYPE_NATIVE_SDK	3	Native SDK</p>
   */
  @JsonProperty("UserType")
  private Integer userType;

  /**
   * 具体原因 （option：进退房时携带）.
   * <p>进房 1：正常进房 2：切换网络 3：超时重试 4：跨房连麦进房</p>
   * <p>退房 1：正常退房 2：超时离开 3：房间用户被移出 4：取消连麦退房 5：强杀</p>
   */
  @JsonProperty("Reason")
  private Integer reason;

  public boolean isReasonNotLeaveOvertime() {
    return !Objects.equals(reason, ExitReason.LEAVE_OVERTIME.getValue());
  }

  public boolean isReasonKill() {
    return Objects.equals(reason, ExitReason.KILL.getValue());
  }

  /**
   * 终端类型.
   */
  enum UserType {
    /**
     * webrtc.
     */
    USER_TYPE_WEBRTC(1),
    /**
     * 小程序.
     */
    USER_TYPE_APPLET(2),
    /**
     * Native SDK.
     */
    USER_TYPE_NATIVE_SDK(3);

    private final Integer value;

    UserType(Integer value) {
      this.value = value;
    }

    public Integer getValue() {
      return value;
    }

  }

  /**
   * 终端类型.
   */
  enum TerminalType {
    /**
     * Windows 端.
     */
    TERMINAL_TYPE_WINDOWS(1),
    /**
     * Android 端.
     */
    TERMINAL_TYPE_ANDROID(2),
    /**
     * iOS 端.
     */
    TERMINAL_TYPE_IOS(3),

    /**
     * Linux 端.
     */
    TERMINAL_TYPE_LINUX(4),
    /**
     * 其他.
     */
    TERMINAL_TYPE_OTHER(100);

    private final Integer value;

    TerminalType(Integer value) {
      this.value = value;
    }

    public Integer getValue() {
      return value;
    }

  }

  /**
   * 角色类型.
   */
  enum Role {
    /**
     * 主播.
     */
    MEMBER_TRTC_ANCHOR(20),
    /**
     * 观众.
     */
    MEMBER_TRTC_VIEWER(21);

    private final Integer value;

    Role(Integer value) {
      this.value = value;
    }

    public Integer getValue() {
      return value;
    }

  }

  /**
   * 进入原因.
   */
  enum EnterReason {
    /**
     * 1：正常进房.
     */
    NORMALLY_ENTER_ROOM(1),
    /**
     * 2：切换网络.
     */
    SWITCH_NETWORK(2),
    /**
     * 3.超时重试.
     */
    RETRY_TIMEOUT(3),
    /**
     * 4：跨房连麦进房.
     */
    CROSS_ROOM_WITH_WHEAT_INTO_THE_ROOM(4);

    private final Integer value;

    EnterReason(Integer value) {
      this.value = value;
    }

    public Integer getValue() {
      return value;
    }
  }


  /**
   * 退出原因.
   */
  enum ExitReason {
    /**
     * 1：正常退出.
     */
    NORMALLY_EXIT_ROOM(1),
    /**
     * 2：超时离开.
     */
    LEAVE_OVERTIME(2),
    /**
     * 3.房间用户被移出.
     */
    ROOM_USER_IS_REMOVED(3),
    /**
     * 4：取消连麦退房.
     */
    CANCEL_MIC_CHECK_OUT(4),
    /**
     * 5：强杀.
     */
    KILL(5);

    private final Integer value;

    ExitReason(Integer value) {
      this.value = value;
    }

    public Integer getValue() {
      return value;
    }
  }
}
