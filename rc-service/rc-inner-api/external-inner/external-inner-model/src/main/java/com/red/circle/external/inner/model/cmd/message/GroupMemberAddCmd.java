package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2023/12/28
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class GroupMemberAddCmd extends Command {

  /**
   * 群id.
   */
  @NotBlank(message = "groupId required.")
  private String groupId;

  /**
   * 账号.
   */
  @NotNull(message = "accounts required.")
  private List<Long> accounts;

  /**
   * 是否静默.
   */
  private Boolean silenceType;

}
