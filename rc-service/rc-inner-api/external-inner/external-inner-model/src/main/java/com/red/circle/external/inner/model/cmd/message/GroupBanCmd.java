package com.red.circle.external.inner.model.cmd.message;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.dto.Command;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 群成员封禁.
 *
 * <AUTHOR> on 2023/8/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GroupBanCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 群组id.
   */
  private String groupId;

  /**
   * 用户id.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long userId;

  /**
   * 有效期.
   */
  private Integer duration;

  /**
   * 描述.
   */
  private String description;

}
