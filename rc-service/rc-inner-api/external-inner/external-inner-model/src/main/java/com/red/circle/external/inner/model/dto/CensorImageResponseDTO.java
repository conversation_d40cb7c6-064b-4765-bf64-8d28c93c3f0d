package com.red.circle.external.inner.model.dto;

import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import lombok.Data;

/**
 * 审核响应.
 *
 * <AUTHOR> on 2023/6/23
 */
@Data
public class CensorImageResponseDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1;

  /**
   * 坚定地址.
   */
  private String detectUrl;

  /**
   * 鉴定建议.
   */
  private CensorSuggestion suggestion;

  /**
   * 鉴定标签.
   */
  private String label;

  /**
   * 该字段为 Label 的子集，表示审核命中的具体审核类别。例如 Sexy，表示色情标签中的性感类别.
   */
  private String subLabel;

  /**
   * <p>审核结果命中审核信息的置信度，取值范围: 0~100.</p>
   * <p>越高代表该内容越有可能属于当前返回审核信息例如：色情 99，则表明该内容非常有可能属于色情内容</p>
   */
  private Long score;

  /**
   * 鉴定结果.
   */
  private List<CensorImageLabelResultDTO> results;

  public void addAllResult(List<CensorImageLabelResultDTO> labelResults) {
    if (CollectionUtils.isEmpty(results)) {
      results = CollectionUtils.newArrayList();
    }
    results.addAll(labelResults);
  }

  /**
   * 格式化label, 字符串标签.
   */
  public String formatLabelString() {
    List<String> labels = CollectionUtils.newArrayList();

    if (Objects.nonNull(getSuggestion())) {
      labels.add(Objects.toString(getSuggestion()));
    }

    if (StringUtils.isNotBlank(getLabel())) {
      labels.add("label=" + getLabel());
    }

    if (StringUtils.isNotBlank(getSubLabel())) {
      labels.add("subLabel=" + getSubLabel());
    }

    if (Objects.nonNull(getScore())) {
      labels.add("score=" + getScore());
    }

    return StringUtils.join(labels, ",");
  }

  public boolean checkBlock() {
    return Objects.equals(getSuggestion(), CensorSuggestion.Block);
  }

  public boolean checkPass() {
    return Objects.equals(getSuggestion(), CensorSuggestion.Pass);
  }
}
