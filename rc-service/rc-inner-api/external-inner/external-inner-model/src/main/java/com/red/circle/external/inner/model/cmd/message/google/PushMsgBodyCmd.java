package com.red.circle.external.inner.model.cmd.message.google;

import com.red.circle.component.core.enums.ISysOriginPlatform;
import com.red.circle.external.inner.model.enums.PushNoticeType;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 自定义消息参数主体.
 *
 * <AUTHOR> on 2021/4/7
 */
@Data
@Accessors(chain = true)
public class PushMsgBodyCmd implements Serializable {

  @Serial
  private static final long serialVersionUID = 3776448614204863896L;

  /**
   * 记录编号.
   */
  private Long id;

  /**
   * 通知系统.
   */
  private ISysOriginPlatform sysPlatform;

  /**
   * 通知类型.
   */
  private PushNoticeType noticeType;

  /**
   * 跳转链接.
   */
  private String link;

  /**
   * 扩展对象.
   */
  private Object expand;
}
