package com.red.circle.external.inner.model.callback.im;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * https://cloud.tencent.com/document/product/269/2716 单聊消息发送后回调.
 *
 * <AUTHOR> on 2022/3/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SingleCallbackAfterSendMsg extends CallbackCommandAbstract {

  /**
   * 消息发送者 UserID.
   */
  @JsonProperty("From_Account")
  private String fromAccount;

  /**
   * 消息接收者 UserID.
   */
  @JsonProperty("To_Account")
  private String toAccount;

  /**
   * 消息序列号，用于标记该条消息（32位无符号整数）.
   */
  @JsonProperty("MsgSeq")
  private String msgSeq;

  /**
   * 消息随机数，用于标记该条消息（32位无符号整数）.
   */
  @JsonProperty("MsgRandom")
  private String msgRandom;

  /**
   * 消息的发送时间戳，单位为秒 单聊消息优先使用 MsgTime 进行排序，同一秒发送的消息则按 MsgSeq 排序，MsgSeq 值越大消息越靠后.
   */
  @JsonProperty("MsgTime")
  private Long msgTime;

  /**
   * 该条消息的唯一标识，可根据该标识进行  REST API 撤回单聊消息.
   */
  @JsonProperty("MsgKey")
  private String msgKey;

  /**
   * 在线消息，为1，否则为0.
   */
  @JsonProperty("OnlineOnlyFlag")
  private Integer onlineOnlyFlag;

  /**
   * 该条消息的下发结果，0表示下发成功，非0表示下发失败，具体可参见 错误码.
   */
  @JsonProperty("SendMsgResult")
  private Integer sendMsgResult;

  /**
   * 该条消息下发失败的错误信息，若消息发送成功，则为"send msg succeed".
   */
  @JsonProperty("ErrorInfo")
  private String errorInfo;

  /**
   * To_Account 未读的单聊消息总数量（包含所有的单聊会话）。若该条消息下发失败（例如被脏字过滤），该字段值为-1.
   */
  @JsonProperty("UnreadMsgNum")
  private Integer unreadMsgNum;

  /**
   * 消息体，具体参见 消息格式描述.
   */
  @JsonProperty("MsgBody")
  private MsgBody msgBody;

  /**
   * 消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到.
   */
  @JsonProperty("CloudCustomData")
  private String cloudCustomData;

}
