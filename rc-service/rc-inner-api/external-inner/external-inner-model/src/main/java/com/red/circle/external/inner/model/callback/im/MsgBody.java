package com.red.circle.external.inner.model.callback.im;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2022/3/28
 */
@Data
@Accessors(chain = true)
public class MsgBody implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * TIM 消息对象类型， 目前支持的消息对象包括： TIMTextElem(文本消息)， TIMFaceElem(表情消息)， TIMLocationElem(位置消息)，
   * TIMCustomElem(自定义消息)
   */
  @JsonProperty("MsgType")
  private MessageTypeEnum msgType;

  /**
   * 对于每种 MsgType 用不同的 MsgContent 格式，具体可参考 消息格式描述
   */
  @JsonProperty("MsgContent")
  private Map<String, Object> msgContent;
}
