package com.red.circle.external.inner.model.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * https://cloud.tencent.com/document/product/269/4193 运营数据指标.
 *
 * <AUTHOR> on 2022/3/28
 */
@Data
public class OperationActiveIndexDTO implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 应用名称.
   */
  private String appName;

  /**
   * 应用 SDKAppID.
   */
  private String appId;

  /**
   * 所属客户名称.
   */
  private String company;

  /**
   * 活跃用户数.
   */
  private String activeUserNum;

  /**
   * 新增注册人数.
   */
  private String registUserNumOneDay;

  /**
   * 累计注册人数.
   */
  private String registUserNumTotal;

  /**
   * 登录次数.
   */
  private String loginTimes;

  /**
   * 登录人数.
   */
  private String loginUserNum;

  /**
   * 上行消息数.
   */
  private String upMsgNum;

  /**
   * 发消息人数.
   */
  private String sendMsgUserNum;

  /**
   * APNs 推送数.
   */
  private String apnsMsgNum;

  /**
   * 上行消息数（C2C）.
   */
  private String c2cUpMsgNum;

  /**
   * 下行消息数（C2C）.
   */
  private String c2cDownMsgNum;

  /**
   * 发消息人数（C2C）.
   */
  private String c2cSendMsgUserNum;

  /**
   * APNs 推送数（C2C）.
   */
  private String c2cAPNSMsgNum;

  /**
   * 最高在线人数.
   */
  private String maxOnlineNum;

  /**
   * 下行消息总数（C2C和群）.
   */
  private String downMsgNum;

  /**
   * 关系链对数增加量.
   */
  private String chainIncrease;

  /**
   * 关系链对数删除量.
   */
  private String chainDecrease;

  /**
   * 上行消息数（群）.
   */
  private String groupUpMsgNum;

  /**
   * 下行消息数（群）.
   */
  private String groupDownMsgNum;

  /**
   * 发消息人数（群）.
   */
  private String groupSendMsgUserNum;

  /**
   * APNs 推送数（群）.
   */
  private String groupAPNSMsgNum;

  /**
   * 发消息群组数.
   */
  private String groupSendMsgGroupNum;

  /**
   * 入群总数.
   */
  private String groupJoinGroupTimes;

  /**
   * 退群总数.
   */
  private String groupQuitGroupTimes;

  /**
   * 新增群组数.
   */
  private String groupNewGroupNum;

  /**
   * 累计群组数.
   */
  private String groupAllGroupNum;

  /**
   * 解散群个数.
   */
  private String groupDestroyGroupNum;

  /**
   * 回调请求数.
   */
  private String callbackReq;

  /**
   * 回调应答数.
   */
  private String callBackRsp;

  /**
   * 日期.
   */
  private String date;

}
