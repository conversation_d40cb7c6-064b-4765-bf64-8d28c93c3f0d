package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.external.inner.model.enums.message.OfficialNoticeTypeEnum;
import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 通知扩展，模版参数.
 *
 * <AUTHOR> on 2020/3/10 16:55
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class NoticeExtTemplateBatchCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 通知用户.
   */
  @NotNull
  private List<Long> toAccounts;

  /**
   * 语言(默认查询用户当前登记语言).
   */
  private String lang;

  /**
   * 通知消息类型
   */
  @NotNull
  private OfficialNoticeTypeEnum noticeType;

  /**
   * 模版参数.
   */
  private Object templateParam;

  /**
   * 扩展对象
   */
  private Object expand;

}
