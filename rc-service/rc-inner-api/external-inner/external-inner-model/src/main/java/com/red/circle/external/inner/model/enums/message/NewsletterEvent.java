package com.red.circle.external.inner.model.enums.message;

/**
 * <AUTHOR> on 2020/4/6 11:20
 */
public enum NewsletterEvent {

  /**
   * 视频违规.
   */
  VIDEO_VIOLATION,

  /**
   * 账号封禁.
   */
  ACCOUNT_ARCHIVE,

  /**
   * 封禁账号+设备.
   */
  ARCHIVE_DEVICE,

  /**
   * 解封设备.
   */
  UNTIE_DEVICE,

  /**
   * 解封账号.
   */
  UNTIE_ACOOUNT,

  /**
   * 解封设备+账户.
   */
  UNTIE_DEVICE_AND_ACCOUNT,

  /**
   * 账号冻结.
   */
  ACCOUNT_FREEZE,

  /**
   * 视频匹配成功通知v2.
   */
  VIDEO_MATCH_SUCCESS_V2,

  /**
   * 用户level等级改变.
   */
  USER_LEVEL_CHANGE,

  /**
   * 用户昵称警告.
   */
  NICKNAME_WARNING,

  /**
   * 头像警告.
   */
  AVATAR_WARNING,

  /**
   * 声音警告.
   */
  AUDIO_WARNING,

  /**
   * 照片墙警告.
   */
  PHOTO_WALL_WARNING,

  /**
   * 用户信息变更.
   */
  USER_INFO_CHANGE,

  /**
   * 任务完成.
   */
  TASK_COMPLETED,

  /**
   * 关闭房间.
   */
  ROOM_CLOSE,

  /**
   * 接收贵族vip.
   */
  ACCEPT_NOBLE_VIP,

  /**
   * 动态消息数量.
   */
  DYNAMIC_MESSAGE_QUANTITY,

  /**
   * 系统拉黑房间用户.
   */
  SYS_PULL_BLACK_ROOM_USER,

  /**
   * 系统移除房间用户.
   */
  SYS_REMOVE_ROOM_USER,

  /**
   * 关闭房间.
   */
  SYS_ROOM_CLOSE

}
