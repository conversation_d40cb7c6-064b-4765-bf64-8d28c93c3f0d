package com.red.circle.external.inner.model.callback.im;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR> on 2022/3/28
 */
@Data
public abstract class CallbackCommandAbstract implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 回调命令字. com.sugartime.app.common.external.tencent.im.callback.im.CallbackCommandEvent
   */
  @JsonProperty("CallbackCommand")
  private String callbackCommand;

}
