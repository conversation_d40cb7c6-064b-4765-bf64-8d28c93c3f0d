package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotBlank;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2024/1/2
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CreateGroupCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 群主 ID.
   */
  @NotBlank(message = "ownerAccount required.")
  private String ownerAccount;

  /**
   * 群组id.
   */
  @NotBlank(message = "groupId required.")
  private String groupId;

  /**
   * 必填, 群名称，最长30字节，使用 UTF-8 编码，1个汉字占3个字节.
   */
  private String name;

  /**
   * 群简介，最长240字节，使用 UTF-8 编码，1个汉字占3个字节.
   */
  private String introduction;

  /**
   * 群公告，最长300字节，使用 UTF-8 编码，1个汉字占3个字节.
   */
  private String notification;

  /**
   * 群头像 URL，最长100字节.
   */
  private String faceUrl;

}
