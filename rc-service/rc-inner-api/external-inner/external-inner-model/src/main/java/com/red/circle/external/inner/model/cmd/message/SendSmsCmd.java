package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 发送验证码.
 *
 * <AUTHOR> on 2021/3/25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class SendSmsCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 手机号码前缀.
   *
   * @eo.required
   */
  @NotNull(message = "phonePrefix required.")
  private Integer phonePrefix;

  /**
   * 手机号码.
   *
   * @eo.required
   */
  @NotNull(message = "phoneNumber required.")
  private String phoneNumber;

  /**
   * 验证码.
   */
  private String code;

  /**
   * 验证码类型(默认注册).
   */
  private String codeType;

  public String fullPhoneNumber() {
    return Objects.toString(phonePrefix).concat(Objects.toString(phoneNumber));
  }

}
