package com.red.circle.external.inner.model.cmd.message.google;

import com.red.circle.common.business.dto.cmd.AppExtCommand;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移除用户出房间.
 *
 * <AUTHOR> on 2021/6/5
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ImRoomRemoveUserCmd extends AppExtCommand {

  /**
   * 房间id.
   *
   * @eo.required
   */
  @NotNull(message = "roomId required.")
  private Long roomId;

  /**
   * 用户id.
   *
   * @eo.required
   */
  @NotNull(message = "userId required.")
  private Long userId;

}
