package com.red.circle.external.inner.model.enums.message;

/**
 * <AUTHOR> on 2021/6/16
 */
public enum GroupMessageTypeEnum {

  /**
   * 爆水晶-预热.
   */
  GAME_BURST_CRYSTAL_WARM_UP,

  /**
   * 爆水晶-结束.
   */
  GAME_BURST_CRYSTAL_CLOSE,

  /**
   * 爆水晶.
   */
  GAME_BURST_CRYSTAL,

  /**
   * 水果游戏V3.
   */
  GAME_FRUIT_V3,

  /**
   * 爆水晶游戏突破记录.
   */
  GAME_BURST_CRYSTAL_SPRINT,

  /**
   * 房间pk.
   */
  GAME_ROOM_PK,

  /**
   * 多种类水果游戏.
   */
  GAME_DOUBLE_LAYER_FRUIT,

  /**
   * 房间创建投票.
   */
  GAME_ROOM_CREATE_VOTE,

  /**
   * 房间投票结束.
   */
  GAME_ROOM_VOTE_END,

  /**
   * 房间内团队pk.
   */
  GAME_INDOOR_TEAM_PK,

  /**
   * 创建飞行棋游戏.
   */
  CREATE_GAME_LUDO,

  /**
   * 系统关闭房间.
   */
  SYS_CLOSE_ROOM,

  /**
   * 游戏房间管理, 处理通知.
   */
  GAME_ROOM_MANAGER_PROCESS,

  /**
   * 游戏播报.
   */
  GAME_BROADCAST,

  /**
   * 幸运礼物游戏通知.
   */
  GAME_LUCKY_GIFT,

  /**
   * 邀请红包提现通知.
   */
  RED_PACKET_INVITE_NEW_WITHDRAW,

  /**
   * 爆水晶宝箱通知.
   */
  GAME_BURST_CRYSTAL_BOX,

  /**
   *  =================================================================
   *  直播麦克风通知.
   *  =================================================================
   */

  /**
   * 麦克风用户变更.
   */
  MIC_CHANGE,

  /**
   * 刷新在线用户.
   */
  REFRESH_ONLINE_USER,

  /**
   * 麦位心动值
   */
  MIC_HEARTBEAT


}
