package com.red.circle.external.inner.model.dto;

import com.red.circle.external.inner.model.enums.CensorSuggestion;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR> on 2023/6/25
 */
@Data
public class CensorImageLabelResultDTO implements Serializable {

  private static final long serialVersionUID = 1;

  /**
   * 业务场景.
   */
  private String bizScene;

  /**
   * 标签.
   */
  private String label;

  /**
   * 该字段为 Label 的子集，表示审核命中的具体审核类别。例如 Sexy，表示色情标签中的性感类别.
   */
  private String subLabel;

  /**
   * 标签.
   */
  private Long score;

  /**
   * 识别场景.
   */
  private String scene;

  /**
   * 建议.
   */
  private CensorSuggestion suggestion;

}
