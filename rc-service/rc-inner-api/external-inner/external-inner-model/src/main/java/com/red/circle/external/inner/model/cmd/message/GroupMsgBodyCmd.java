package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.external.inner.model.enums.message.GroupMessageTypeEnum;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR> on 2021/6/16
 */
@Data
public class GroupMsgBodyCmd implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 消息类型.
   */
  private GroupMessageTypeEnum type;

  /**
   * 数据结构.
   */
  private Object data;

  public static GroupMsgBodyCmd createMsg(GroupMessageTypeEnum type, Object data) {
    return new GroupMsgBodyCmd(type, data);
  }

  public GroupMsgBodyCmd(
      GroupMessageTypeEnum type, Object data) {
    this.type = type;
    this.data = data;
  }
}
