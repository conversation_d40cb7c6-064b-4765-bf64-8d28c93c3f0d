package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2023/10/17
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TrtcUserBlockedCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 房间id.
   */
  @NotNull(message = "roomId required.")
  private Long roomId;

  /**
   * 用户id.
   */
  @NotNull(message = "userId required.")
  private Long userId;

  /**
   * 0表示unMute,1表示mute.
   */
  @NotNull(message = "mute required.")
  private Integer mute;

}
