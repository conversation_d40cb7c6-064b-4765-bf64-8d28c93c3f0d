package com.red.circle.external.inner.model.callback.im;

import java.util.Objects;

/**
 * 回调命令.
 *
 * <AUTHOR> on 2021/7/24
 */
public enum CallbackCommandEvent {

  /**
   * 状态变更回调.
   */
  STATE_STATE_CHANGE("State.StateChange"),

  /**
   * 添加好友之后回调.
   */
  SNS_CALLBACK_FRIEND_ADD("Sns.CallbackFriendAdd"),

  /**
   * 删除好友之后回调.
   */
  SNS_CALLBACK_FRIEND_DELETE("Sns.CallbackFriendDelete"),

  /**
   * 添加黑名单之后回调.
   */
  SNS_CALLBACK_BLACK_LIST_ADD("Sns.CallbackBlackListAdd"),

  /**
   * 删除黑名单之后回调.
   */
  SNS_CALLBACK_BLACK_LIST_DELETE("Sns.CallbackBlackListDelete"),

  /**
   * 发单聊消息之前回调.
   */
  C2C_CALLBACK_BEFORE_SEND_MSG("C2C.CallbackBeforeSendMsg"),

  /**
   * 发单聊消息之后回调.
   */
  C2C_CALLBACK_AFTER_SEND_MSG("C2C.CallbackAfterSendMsg"),

  /**
   * 创建群组之前回调.
   */
  GROUP_CALLBACK_BEFORE_CREATE_GROUP("Group.CallbackBeforeCreateGroup"),

  /**
   * 创建群组之后回调.
   */
  GROUP_CALLBACK_AFTER_CREATE_GROUP("Group.CallbackAfterCreateGroup"),

  /**
   * 申请入群之前回调.
   */
  GROUP_CALLBACK_BEFORE_APPLY_JOIN_GROUP("Group.CallbackBeforeApplyJoinGroup"),

  /**
   * 拉人入群之前回调.
   */
  GROUP_CALLBACK_BEFORE_INVITE_JOIN_GROUP("Group.CallbackBeforeInviteJoinGroup"),

  /**
   * 新成员入群之后回调.
   */
  GROUP_CALLBACK_AFTER_NEW_MEMBER_JOIN("Group.CallbackAfterNewMemberJoin"),

  /**
   * 群成员离开之后回调.
   */
  GROUP_CALLBACK_AFTER_MEMBER_EXIT("Group.CallbackAfterMemberExit"),

  /**
   * 群内发言之前回调.
   */
  GROUP_CALLBACK_BEFORE_SEND_MSG("Group.CallbackBeforeSendMsg"),

  /**
   * 群内发言之后回调.
   */
  GROUP_CALLBACK_AFTER_SEND_MSG("Group.CallbackAfterSendMsg"),

  /**
   * 群组满员之后回调.
   */
  GROUP_CALLBACK_AFTER_GROUP_FULL("Group.CallbackAfterGroupFull"),

  /**
   * 群组解散之后回调.
   */
  GROUP_CALLBACK_AFTER_GROUP_DESTROYED("Group.CallbackAfterGroupDestroyed"),

  /**
   * 群组资料修改之后回调.
   */
  GROUP_CALLBACK_AFTER_GROUP_INFO_CHANGED("Group.CallbackAfterGroupInfoChanged");

  private final String type;

  CallbackCommandEvent(String type) {
    this.type = type;
  }

  public String getType() {
    return type;
  }

  public boolean eqType(String type) {
    return Objects.equals(this.getType(), type);
  }
}
