package com.red.circle.external.inner.model.callback.im;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 群成员离开之后回调.
 *
 * <AUTHOR> on 2021/7/24
 */
@Data
public class GroupCallbackAfterMemberExit implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 回调命令.
   */
  @JsonProperty("CallbackCommand")
  private String callbackCommand;

  /**
   * 产生群消息的群组 ID.
   */
  @JsonProperty("GroupId")
  private String groupId;

  /**
   * 产生群消息的 群组类型介绍，例如 Public.
   */
  @JsonProperty("Type")
  private String type;

  /**
   * 成员离开方式：Kicked 为被群主移出群聊；Quit 为主动退群.
   */
  @JsonProperty("ExitType")
  private String exitType;

  /**
   * 退群者 UserID.
   */
  @JsonProperty("Operator_Account")
  private String operatorAccount;

  /**
   * 退出群的成员列表.
   */
  @JsonProperty("ExitMemberList")
  private List<MemberProfile> exitMemberList;

}
