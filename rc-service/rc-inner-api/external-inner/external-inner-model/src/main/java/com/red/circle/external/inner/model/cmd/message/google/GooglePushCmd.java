package com.red.circle.external.inner.model.cmd.message.google;

import com.google.common.collect.Lists;
import com.red.circle.component.core.enums.ISysOriginPlatform;
import com.red.circle.framework.core.dto.CommonCommand;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.io.Serial;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 简单的push参数.
 *
 * <AUTHOR>
 * @since 2020/2/4 11:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GooglePushCmd extends CommonCommand {

  @Serial
  private static final long serialVersionUID = 3776448614204863896L;

  /**
   * 系统.
   */
  private ISysOriginPlatform sysOrigin;

  /**
   * 标题.
   */
  private String title;

  /**
   * 副标题.
   */
  private String subTitle;

  /**
   * 类容.
   */
  private String body;

  /**
   * ios 设备号.
   */
  private List<String> iosDevices;

  /**
   * android 设备号.
   */
  private List<String> androidDevices;

  /**
   * 自定义参数.
   */
  private PushMsgBodyCmd customize;

  /**
   * 添加iOS设备.
   */
  public void addIosDevice(String device) {
    if (Objects.isNull(iosDevices)) {
      iosDevices = Lists.newArrayList();
    }
    iosDevices.add(device);
  }

  /**
   * 添加Android设备.
   */
  public void addAndroidDevice(String device) {
    if (Objects.isNull(androidDevices)) {
      androidDevices = Lists.newArrayList();
    }
    androidDevices.add(device);
  }

  public List<String> getAllDevices() {
    return Stream.of(iosDevices, androidDevices)
        .filter(CollectionUtils::isNotEmpty)
        .flatMap(Collection::stream)
        .collect(Collectors.toList());
  }

  public boolean isNotEmptyAndroidDevices() {
    return CollectionUtils.isNotEmpty(androidDevices);
  }

  public boolean isNotEmptyIosDevices() {
    return CollectionUtils.isNotEmpty(iosDevices);
  }
}
