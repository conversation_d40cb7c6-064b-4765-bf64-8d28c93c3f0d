package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> on 2024/1/2
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CreateGroupAddMemberCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 群组id.
   */
  @NotBlank(message = "groupId required.")
  private String groupId;

  /**
   * 用户id.
   */
  @NotNull(message = "userIds required.")
  private List<Long> userIds;

}
