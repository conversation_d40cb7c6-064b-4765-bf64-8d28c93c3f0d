package com.red.circle.external.inner.model.callback.im;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR> on 2021/7/24
 */
@Data
public class MemberProfile implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 成员账号.
   */
  @JsonProperty("Member_Account")
  private String memberAccount;

}
