package com.red.circle.external.inner.model.callback.im;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * https://cloud.tencent.com/document/product/269/2661 群聊消息发送后回调.
 *
 * <AUTHOR> on 2022/3/28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GroupCallbackAfterSendMsg extends CallbackCommandAbstract {

  /**
   * 产生群消息的群组 ID.
   */
  @JsonProperty("GroupId")
  private String groupId;

  /**
   * 产生群消息的 群组类型介绍，例如 Public. com.sugartime.app.common.external.tencent.im.enums.GroupType
   */
  @JsonProperty("Type")
  private String type;

  /**
   * 消息发送者 UserID.
   */
  @JsonProperty("From_Account")
  private String fromAccount;

  /**
   * 请求发起者 UserID，可以用来识别是否为管理员请求的.
   */
  @JsonProperty("Operator_Account")
  private String operatorAccount;

  /**
   * 发消息请求中的32位随机数.
   */
  @JsonProperty("Random")
  private String random;

  /**
   * 消息序列号，一条消息的唯一标示 群聊消息使用 MsgSeq 进行排序，MsgSeq 值越大消息越靠后.
   */
  @JsonProperty("MsgSeq")
  private String msgSeq;

  /**
   * 消息发送的时间戳，对应后台 Server 时间.
   */
  @JsonProperty("MsgTime")
  private Long msgTime;

  /**
   * 在线消息，为1，否则为0.
   */
  @JsonProperty("OnlineOnlyFlag")
  private Integer onlineOnlyFlag;

  /**
   * 消息体，具体参见 消息格式描述.
   */
  @JsonProperty("MsgBody")
  private MsgBody msgBody;

  /**
   * 消息自定义数据（云端保存，会发送到对端，程序卸载重装后还能拉取到.
   */
  @JsonProperty("CloudCustomData")
  private String cloudCustomData;

}
