package com.red.circle.external.inner.model.cmd.message;

import com.red.circle.framework.dto.Command;
import jakarta.validation.constraints.NotNull;
import java.io.Serial;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 接口：https://cloud.tencent.com/document/product/647/40496
 * <p>
 * 接口说明：将用户从房间移出，适用于主播/房主/管理员踢人等场景。支持所有平台，Android、iOS、Windows 和 macOS 需升级到 TRTC SDK 6.6及以上版本.
 * <p>
 * 默认接口请求频率限制：20次/秒.
 *
 * <AUTHOR> on 2023/10/17
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TrtcRemoveUserCmd extends Command {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 房间id.
   */
  @NotNull(message = "roomId required.")
  private Long roomId;

  /**
   * 用户id.
   */
  @NotNull(message = "userIds required.")
  private List<Long> userIds;

  /**
   * 调用来源，日志打印【可选】.
   */
  private String logOrigin;
}
