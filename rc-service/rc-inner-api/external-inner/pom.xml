<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>
  <artifactId>external-inner</artifactId>
  <description>服务内部通讯API， 对内部服务提供统一对外标准</description>
  <packaging>pom</packaging>

  <modules>
    <module>external-inner-api</module>
    <module>external-inner-model</module>
  </modules>


  <parent>
    <artifactId>rc-inner-api</artifactId>
    <groupId>com.red.circle</groupId>
    <version>${revision}</version>
    <relativePath>../../rc-inner-api/pom.xml</relativePath>
  </parent>

</project>
