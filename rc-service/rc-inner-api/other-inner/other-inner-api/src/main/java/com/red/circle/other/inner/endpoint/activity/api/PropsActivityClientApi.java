package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.other.inner.model.cmd.activity.SendActivityRewardCmd;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRuleConfigParamCmd;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRuleConfigQryCmd;
import com.red.circle.other.inner.model.dto.activity.props.ActivityPropsGroup;
import com.red.circle.other.inner.model.dto.activity.props.ActivityPropsRule;
import com.red.circle.other.inner.model.dto.activity.props.ActivityPropsRuleDTO;
import com.red.circle.other.inner.model.dto.activity.props.ActivityResource;
import com.red.circle.other.inner.model.dto.material.PropsActivityRuleConfigDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 道具活动服务.
 *
 * <AUTHOR> on 2023/6/5
 */
public interface PropsActivityClientApi {

  String API_PREFIX = "/props-activity-cnf/client";

  /**
   * 获取平台指定道具类型-规则.
   *
   * @param sysOrigin    平台来源
   * @param activityType 类型
   * @return list
   */
  @GetMapping("/listRule")
  ResultResponse<List<ActivityPropsRule>> listRule(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin,
      @RequestParam("activityType") PropsActivityTypeEnum activityType
  );

  /**
   * 爆水晶-获取大于指定等级-规则.
   *
   * @param sysOrigin 归属平台
   * @param level     等级
   * @return rule
   */
  @GetMapping("/getRuleGtLevelCrystal")
  ResultResponse<ActivityPropsRule> getRuleGtLevelCrystal(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin,
      @RequestParam("level") Integer level
  );

  /**
   * 爆水晶-第一个等级规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  @GetMapping("/getRuleCrystalFirst")
  ResultResponse<ActivityPropsRule> getRuleCrystalFirst(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin);

  /**
   * LuckyBox-第一个规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  @GetMapping("/getRuleLuckyBoxFirst")
  ResultResponse<ActivityPropsRule> getRuleLuckyBoxFirst(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin);

  /**
   * 摩天轮Win宝箱奖励最后一个规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  @GetMapping("/getRuleGameFruitBoxWinLast")
  ResultResponse<ActivityPropsRule> getRuleGameFruitBoxWinLast(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin);

  /**
   * 摩天轮轮数宝箱奖励最后一个规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  @GetMapping("/getRuleGameFruitBoxRoundsLast")
  ResultResponse<ActivityPropsRule> getRuleGameFruitBoxRoundsLast(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin);


  /**
   * 获取规则信息.
   */
  @GetMapping("/getByRuleId")
  ResultResponse<ActivityPropsRule> getByRuleId(@RequestParam("id") Long id);

  /**
   * 获取指定类型活动资源.
   *
   * @param sysOrigin    平台信息
   * @param activityType 活动类型
   * @return 活动资源组信息
   */
  @GetMapping("/listActivityResource")
  ResultResponse<List<ActivityResource>> listActivityResource(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin,
      @RequestParam("activityType") PropsActivityTypeEnum activityType
  );

  /**
   * 获取指定类型活动资源组id.
   *
   * @param sysOrigin    平台信息
   * @param activityType 活动类型
   * @return 活动资源组ids
   */
  @GetMapping("/listActivityResourceGroupIds")
  ResultResponse<List<Long>> listActivityResourceGroupIds(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin,
      @RequestParam("activityType") PropsActivityTypeEnum activityType
  );

  /**
   * 获取指定规则道具信息.
   *
   * @param ruleId 用户id
   * @return rule
   */
  @GetMapping("/getActivityResource")
  ResultResponse<ActivityResource> getActivityResource(@RequestParam("ruleId") Long ruleId);

  /**
   * 获得资源组详细信息
   *
   * @param sysOrigin 平台信息
   * @param groupIds  资源组ID集合
   * @return 资源组详细信息
   */
  @PostMapping("/mapActivityPropsGroup")
  ResultResponse<Map<Long, ActivityPropsGroup>> mapActivityPropsGroup(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("groupIds") Set<Long> groupIds
  );

  /**
   * 获得资源组详细信息
   *
   * @param sysOrigin 平台信息
   * @param groupId   资源组ID
   * @return 资源组详细信息
   */
  @PostMapping("/getActivityPropsGroup")
  ResultResponse<ActivityPropsGroup> getActivityPropsGroup(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("groupId") Long groupId
  );

  /**
   * 发送活动奖品.
   * <p>给指定用户发送， 道具组一组奖品</p>
   */
  @PostMapping("/sendActivityReward")
  ResultResponse<Void> sendActivityReward(@RequestBody @Validated SendActivityRewardCmd cmd);

  /**
   * 查询活动道具规则配置映射
   */
  @PostMapping("/mapRuleConfigByIds")
  ResultResponse<Map<Long, ActivityPropsRuleDTO>> mapRuleConfigByIds(
      @RequestBody Collection<Long> ids);

  /**
   * 分页-查询活动道具规则配置.
   */
  @PostMapping("/pagePropsActivityRuleConfig")
  ResultResponse<PageResult<PropsActivityRuleConfigDTO>> pagePropsActivityRuleConfig(
      @RequestBody @Validated PropsActivityRuleConfigQryCmd query);


  /**
   * 删除-查询活动道具规则配置.
   */
  @PostMapping("/propsActivityRuleConfigDeleteById")
  ResultResponse<Void> propsActivityRuleConfigDeleteById(@RequestParam("id") Long id);

  /**
   * 查询活动道具规则配置.
   */
  @PostMapping("/getPropsActivityRuleConfig")
  ResultResponse<PropsActivityRuleConfigDTO> getPropsActivityRuleConfig(
      @RequestBody @Validated PropsActivityRuleConfigParamCmd param);

  /**
   * 修改-查询活动道具规则配置.
   */
  @PostMapping("/updatePropsActivityRuleConfigById")
  ResultResponse<Void> updatePropsActivityRuleConfigById(
      @RequestBody @Validated PropsActivityRuleConfigParamCmd param);

  /**
   * 保存-查询活动道具规则配置.
   */
  @PostMapping("/savePropsActivityRuleConfig")
  ResultResponse<Void> savePropsActivityRuleConfig(
      @RequestBody @Validated PropsActivityRuleConfigParamCmd param);
}
