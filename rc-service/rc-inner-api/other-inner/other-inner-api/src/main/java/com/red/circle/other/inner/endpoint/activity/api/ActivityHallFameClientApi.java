package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.activity.ActivityHallFameQryCmd;
import com.red.circle.other.inner.model.dto.activity.ActivityHallFameDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 名人堂.
 *
 * <AUTHOR> on 2023/10/9
 */
public interface ActivityHallFameClientApi {

  String API_PREFIX = "/activity-hall-fame/client";

  @PostMapping("/save")
  ResultResponse<Void> saveOrUpdate(@RequestBody ActivityHallFameDTO dto);

  @GetMapping("/deleteById")
  ResultResponse<Void> deleteById(@RequestParam("id") Long id);

  @PostMapping("/pageQuery")
  ResultResponse<PageResult<ActivityHallFameDTO>> pageQuery(
      @RequestBody ActivityHallFameQryCmd qryCmd);

}
