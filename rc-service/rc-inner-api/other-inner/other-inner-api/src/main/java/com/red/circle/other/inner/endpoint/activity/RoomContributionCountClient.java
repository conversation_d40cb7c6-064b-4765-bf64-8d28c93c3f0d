package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.RoomContributionCountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 房间贡献统计.
 *
 * <AUTHOR> on 2023/11/23
 */
@FeignClient(name = "roomContributionCountClient", url = "${feign.other.url}" +
    RoomContributionCountClientApi.API_PREFIX)
public interface RoomContributionCountClient extends RoomContributionCountClientApi {

}
