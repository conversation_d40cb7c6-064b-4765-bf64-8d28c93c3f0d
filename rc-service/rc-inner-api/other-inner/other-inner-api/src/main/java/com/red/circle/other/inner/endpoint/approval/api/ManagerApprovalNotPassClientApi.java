package com.red.circle.other.inner.endpoint.approval.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.approval.TmpManagerApprovalCmd;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 系统管理员审批(提供给服务过渡使用).
 *
 * <AUTHOR> on 2023/12/27
 */
public interface ManagerApprovalNotPassClientApi {

  String API_PREFIX = "/manager-approval-not-pass/client";

  /**
   * app管理员资料相关审批.
   */
  @PostMapping("/approval")
  ResultResponse<Void> approval(@RequestBody @Validated TmpManagerApprovalCmd cmd);

}
