package com.red.circle.other.inner.endpoint.approval.api;

import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.common.business.core.enums.DataApprovalTypeEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.approval.ApprovalDataCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalOpsPageQryCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalPhotoWallApprovalTableQryCmd;
import com.red.circle.other.inner.model.cmd.approval.ProfileApprovalContentCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalUserPhotoWallDTO;
import com.red.circle.other.inner.model.dto.approval.ApprovalUserSettingDataDTO;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 审核信息设置.
 *
 * <AUTHOR> on 2023/5/25
 */
public interface ApprovalUserSettingDataClientApi {

  String API_PREFIX = "/approval-user-setting-data/client";

  /**
   * 提交审批信息.
   */
  @PostMapping("/submitApproval")
  ResultResponse<Void> submitApproval(@RequestBody @Validated ProfileApprovalContentCmd cmd);

  /**
   * 提交审批信息.
   */
  @PostMapping("/submitApprovalBatch")
  ResultResponse<Void> submitApprovalBatch(
      @RequestBody @Validated List<ProfileApprovalContentCmd> cmd);


  /**
   * 修改审批状态.
   */
  @PostMapping("/status/update")
  ResultResponse<Void> updateStatus(@RequestBody ApprovalDataCmd cmd);

  /**
   * 保存或修改审批类型.
   */
  @PostMapping("/status/save-or-update")
  ResultResponse<Void> saveOrUpdate(
      @RequestParam("userId") Long userId,
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("approveType") DataApprovalTypeEnum approveType
  );

  /**
   * 设置审批状态.
   */
  @PostMapping("/status/save")
  ResultResponse<Void> save(
      @RequestParam("userId") Long userId,
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("approveType") DataApprovalTypeEnum approveType,
      @RequestParam("approvalStatus") ApprovalStatusEnum approvalStatus
  );

  /**
   * 批量添加.
   */
  @PostMapping("/status/save-batch")
  ResultResponse<Void> saveBatch(
      @RequestParam("userId") Long userId,
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("approveTypes") List<DataApprovalTypeEnum> approveTypes
  );

  /**
   * 累计违规次数.
   */
  @PostMapping("/incr/not-pass-size")
  ResultResponse<Void> incrNotPassSize(
      @RequestParam("userId") Long userId,
      @RequestParam("approveType") DataApprovalTypeEnum approveType
  );

  /**
   * 修改状态和机器编码.
   */
  @PostMapping("/status-machine-label/update")
  ResultResponse<Boolean> updateStatusMachineLabel(
      @RequestParam("userId") Long userId,
      @RequestParam("approvalStatus") ApprovalStatusEnum approvalStatus,
      @RequestParam("approveType") DataApprovalTypeEnum approveType,
      @RequestParam("label") String label
  );

  /**
   * 验证是否审批通过.
   *
   * @return true 通过，false 不通过
   */
  @GetMapping("/check/pass")
  ResultResponse<Boolean> isPass(
      @RequestParam("userId") Long userId,
      @RequestParam("approvalType") DataApprovalTypeEnum approvalType
  );

  /**
   * 验证是否审不批通过或者没有上传.
   *
   * @param userId       用户id
   * @param approvalType 审批状态
   * @return true 是，false 否
   */
  @GetMapping("/check/none-or-not-pass")
  ResultResponse<Boolean> isNoneOrNotPass(
      @RequestParam("userId") Long userId,
      @RequestParam("approvalType") DataApprovalTypeEnum approvalType
  );

  @PostMapping("/pageUserDetailsPhotoWall")
  ResultResponse<PageResult<ApprovalUserPhotoWallDTO>> pageUserDetailsPhotoWall(
      @RequestBody ApprovalPhotoWallApprovalTableQryCmd query);

  /**
   * 获取审批表格(运营后台).
   */
  @PostMapping("/listOps")
  ResultResponse<PageResult<ApprovalUserSettingDataDTO>> listOps(
      @RequestBody ApprovalOpsPageQryCmd qryCmd);
}
