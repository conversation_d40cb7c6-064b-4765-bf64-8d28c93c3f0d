package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.enums.activity.AgentActivityCountType;
import com.red.circle.other.inner.model.dto.activity.AgentActivityCountDTO;
import java.util.Collection;
import java.util.Set;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 代理活动统计
 *
 * <AUTHOR> on 2023/8/15
 */
public interface AgentActivityCountClientApi {

  String API_PREFIX = "/agent-activity-count/client";

  /**
   * 减少代理积分.
   */
  @PostMapping("/reduceTarget")
  ResultResponse<Void> reduceTarget(@RequestBody Collection<Long> teamIds,
      @RequestParam("target") Long target);

  /**
   * 归零活动积分.
   */
  @GetMapping("/initZero")
  ResultResponse<Void> initZero(@RequestParam("acceptUserId") Long acceptUserId,
      @RequestParam("teamId") Long teamId);


  /**
   * 分页列表.
   */
  @GetMapping("/pageData")
  ResultResponse<PageResult<AgentActivityCountDTO>> pageData(
      @RequestParam("countType") AgentActivityCountType countType,
      @RequestParam("regionId") String regionId,
      @RequestParam("current") Integer current,
      @RequestParam("limitSize") Integer limitSize);

  /**
   * 归零活动积分团队.
   */
  @GetMapping("/initZeroTeam")
  ResultResponse<Void> initZeroTeam(@RequestParam("teamId") Long teamId);

  /**
   * 归零活动积分成员.
   */
  @PostMapping("/initZeroMember")
  ResultResponse<Void> initZeroMember(@RequestBody Set<Long> acceptUserIds);

  /**
   * 删除代理积分.
   */
  @PostMapping("/removeByTeamIds")
  ResultResponse<Void> removeByTeamIds(@RequestBody Collection<Long> teamIds);


  /**
   * 增加代理积分.
   *
   * @param teamId   团队
   * @param target   目标值
   * @param regionId 区域ID
   */
  @GetMapping("/incrTarget")
  ResultResponse<Void> incrTarget(@RequestParam("teamId") Long teamId,
      @RequestParam("target") Long target, @RequestParam("regionId") String regionId);
}
