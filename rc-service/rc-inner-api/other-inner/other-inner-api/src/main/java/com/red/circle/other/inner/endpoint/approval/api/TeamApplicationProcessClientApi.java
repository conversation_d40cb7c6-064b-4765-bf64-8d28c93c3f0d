package com.red.circle.other.inner.endpoint.approval.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.dto.team.TeamApplicationProcessDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

public interface TeamApplicationProcessClientApi {

    String API_PREFIX = "/team-process-not-pass/client";

    /**
     * 获取申请记录
     */
    @PostMapping("/getByIds")
    ResultResponse<List<TeamApplicationProcessDTO>> getByIds(@RequestParam("ids")Set<Long> ids);

    /**
     * 上个月申请退出团队的成员申请记录.
     *
     * @return 记录
     */
    @GetMapping("/getInfos")
    List<TeamApplicationProcessDTO> listLastMonthApplyOutTeamMember();

    /**
     * 查询申请离开团队的申请记录.
     *
     * @param day 大于创建申请离开时间多少天
     * @re
     * */
    @GetMapping("/getByDays")
    List<TeamApplicationProcessDTO> listApplyOutTeamMember(@RequestParam("day") Integer day);

    /**
     * 获取用户退出申请.
     */
    @GetMapping("/getByApplyId")
    TeamApplicationProcessDTO getQuitWaitApplyById(@RequestParam("id") Long id);

    /**
     * 根据id删除申请记录
     */
    @GetMapping("/deleteById")
    void deleteById(@RequestParam("id") Long id);

}
