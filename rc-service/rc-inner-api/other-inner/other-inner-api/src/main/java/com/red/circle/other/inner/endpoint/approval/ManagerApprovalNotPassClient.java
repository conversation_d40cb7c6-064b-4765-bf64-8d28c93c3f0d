package com.red.circle.other.inner.endpoint.approval;

import com.red.circle.other.inner.endpoint.approval.api.ManagerApprovalNotPassClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 系统管理员审批(提供给服务过渡使用).
 *
 * <AUTHOR> on 2023/12/27
 */
@FeignClient(name = "managerApprovalNotPassClient", url = "${feign.other.url}" +
    ManagerApprovalNotPassClientApi.API_PREFIX)
public interface ManagerApprovalNotPassClient extends ManagerApprovalNotPassClientApi {

}
