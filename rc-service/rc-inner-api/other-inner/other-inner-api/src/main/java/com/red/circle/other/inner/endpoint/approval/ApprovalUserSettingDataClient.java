package com.red.circle.other.inner.endpoint.approval;

import com.red.circle.other.inner.endpoint.approval.api.ApprovalUserSettingDataClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 审核信息设置.
 *
 * <AUTHOR> on 2023/5/25
 */
@FeignClient(name = "approvalUserSettingDataClient", url = "${feign.other.url}" +
    ApprovalUserSettingDataClientApi.API_PREFIX)
public interface ApprovalUserSettingDataClient extends ApprovalUserSettingDataClientApi {

}
