package com.red.circle.other.inner.endpoint.approval.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.dto.team.TeamTerminationFeeRecordDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.Serializable;
import java.util.List;


public interface TeamTerminationFeeRecordClientApi {

  String API_PREFIX = "/team-record-not-pass/client";

  /**
   * 获取申请记录
   */
  @GetMapping("/listByTimeGt24AndPayeeUserNull")
  ResultResponse<List<TeamTerminationFeeRecordDTO>> listByTimeGt24AndPayeeUserNull();

  /**
   * 根据 ID 查询.
   *
   * @param id 主键ID
   */
  @GetMapping("/getById")
  ResultResponse<TeamTerminationFeeRecordDTO> getById(Serializable id);


  @PostMapping("/updateSelectiveById")
  ResultResponse<TeamTerminationFeeRecordDTO> updateSelectiveById(
      @RequestBody TeamTerminationFeeRecordDTO teamTerminationFeeRecordDTO);
}
