package com.red.circle.other.inner.endpoint.activity.api;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> on @date 2024/3/14
 */
public interface ActivityAgentAnchorCountClientApi {

  String API_PREFIX = "/activity-agent-anchor-count/client";

  /**
   * 归零活动积分.
   */

  @RequestMapping("/initZero")
  void initZero(@RequestParam("acceptUserId") Long acceptUserId,
      @RequestParam("teamId") Long teamId);
}
