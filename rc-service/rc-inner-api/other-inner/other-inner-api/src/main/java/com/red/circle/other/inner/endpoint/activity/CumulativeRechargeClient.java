package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.CumulativeRechargeClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 累积充值.
 *
 * <AUTHOR> on 2023/8/18
 */
@FeignClient(name = "cumulativeRechargeClient", url = "${feign.other.url}" +
    CumulativeRechargeClientApi.API_PREFIX)
public interface CumulativeRechargeClient extends CumulativeRechargeClientApi {

}
