package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.dto.activity.RoomContributionActivityCountDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 房间贡献.
 *
 * <AUTHOR> on 2023/11/2
 */
public interface RoomContributionActivityCountClientApi {

  String API_PREFIX = "/room-contribution-activity-count/client";

  /**
   * 已领取分页记录.
   *
   * @param roomId    房间id
   * @param current   第几页
   * @param limitSize 每页显示数量
   * @return 结果
   */
  @GetMapping("/pageReceived")
  ResultResponse<PageResult<RoomContributionActivityCountDTO>> pageReceived(
      @RequestParam(value = "roomId", required = false) Long roomId,
      @RequestParam("current") Integer current,
      @RequestParam("limitSize") Integer limitSize
  );

}
