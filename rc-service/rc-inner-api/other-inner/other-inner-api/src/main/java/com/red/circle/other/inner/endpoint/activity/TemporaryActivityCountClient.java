package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.TemporaryActivityCountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 临时活动统计服务.
 *
 * <AUTHOR> on 2024/7/8
 */
@FeignClient(name = "temporaryActivityCountClient", url = "${feign.other.url}" +
        TemporaryActivityCountClientApi.API_PREFIX)
public interface TemporaryActivityCountClient extends TemporaryActivityCountClientApi {


}
