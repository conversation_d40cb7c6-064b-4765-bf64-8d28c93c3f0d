package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.dto.activity.ActivityGlobalizationDescriptionDTO;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 活动国际化语言描述 服务实现类.
 *
 * <AUTHOR> on 2023/10/8
 */
public interface ActivityGlobalizationDescriptionClientApi {

  String API_PREFIX = "/activity-globalization-description/client";

  @GetMapping("/listByRelationId")
  ResultResponse<List<ActivityGlobalizationDescriptionDTO>> listByRelationId(
      @RequestParam("relationId") Long relationId);

  @PostMapping("/save")
  ResultResponse<Void> saveOrUpdate(
      @RequestBody @Validated ActivityGlobalizationDescriptionDTO dto);

  @GetMapping("/deleteById")
  ResultResponse<Void> deleteById(@RequestParam("id") Long id);

}
