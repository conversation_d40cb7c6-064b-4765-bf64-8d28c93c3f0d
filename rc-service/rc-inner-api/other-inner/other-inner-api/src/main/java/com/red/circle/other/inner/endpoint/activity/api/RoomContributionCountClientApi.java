package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.live.RoomContributionCountPageQryCmd;
import com.red.circle.other.inner.model.dto.activity.RoomContributionCountDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 房间贡献统计.
 *
 * <AUTHOR> on 2023/11/23
 */
public interface RoomContributionCountClientApi {

  String API_PREFIX = "/room/contribution/count/client";

  /**
   * 分页数据.
   */
  @PostMapping("/pageOps")
  ResultResponse<PageResult<RoomContributionCountDTO>> pageOps(
      @RequestBody @Validated RoomContributionCountPageQryCmd qryCmd);

}
