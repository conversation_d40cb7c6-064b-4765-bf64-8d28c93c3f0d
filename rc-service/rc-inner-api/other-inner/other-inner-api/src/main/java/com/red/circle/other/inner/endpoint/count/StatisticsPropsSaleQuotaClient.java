package com.red.circle.other.inner.endpoint.count;

import com.red.circle.other.inner.endpoint.count.api.StatisticsPropsSaleQuotaClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 道具销售情况.
 *
 * <AUTHOR> on 2023/11/17
 */
@FeignClient(name = "statisticsPropsSaleQuotaClient", url = "${feign.other.url}" +
    StatisticsPropsSaleQuotaClientApi.API_PREFIX)
public interface StatisticsPropsSaleQuotaClient extends StatisticsPropsSaleQuotaClientApi {


}
