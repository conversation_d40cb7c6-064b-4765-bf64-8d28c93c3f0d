package com.red.circle.other.inner.endpoint.dynamic.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.dynamic.TopDynamicCmd;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 动态-缓存服务.
 *
 * <AUTHOR> on 2023/10/5
 */
public interface TopDynamicClientApi {

  String API_PREFIX = "/top/dynamic";

  @PostMapping("/setUpTop")
  ResultResponse<Void> setUpTop(@RequestBody TopDynamicCmd param);

  @GetMapping("/closeTop")
  ResultResponse<Void> closeTop(@RequestParam("dynamicId") Long dynamicId);
}
