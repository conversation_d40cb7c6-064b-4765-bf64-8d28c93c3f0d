package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.AgentActivityCountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on 2023/8/15
 */
@FeignClient(name = "agentActivityCountClient", url = "${feign.other.url}" +
        AgentActivityCountClientApi.API_PREFIX)
public interface AgentActivityCountClient extends AgentActivityCountClientApi {

}
