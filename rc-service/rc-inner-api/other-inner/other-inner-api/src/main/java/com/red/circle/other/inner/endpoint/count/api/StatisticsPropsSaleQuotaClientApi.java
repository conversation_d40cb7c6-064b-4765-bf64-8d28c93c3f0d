package com.red.circle.other.inner.endpoint.count.api;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.count.PropsSaleQuotaQryCmd;
import com.red.circle.other.inner.model.dto.count.StatisticsPropsSaleQuotaDTO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 道具销售情况.
 *
 * <AUTHOR> on 2023/11/17
 */
public interface StatisticsPropsSaleQuotaClientApi {

  String API_PREFIX = "/statistics/props/sale-quota/client";

  /**
   * 查询指定道具销售额.
   */
  @PostMapping("/listPropsSaleQuota")
  ResultResponse<List<StatisticsPropsSaleQuotaDTO>> listPropsSaleQuota(
      @RequestBody PropsSaleQuotaQryCmd qryCmd);

  /**
   * 获取指定年总额.
   */
  @GetMapping("/getYearPropsSaleQuota")
  ResultResponse<Long> getYearPropsSaleQuota(
      @RequestParam("sysOrigin") SysOriginPlatformEnum sysOrigin,
      @RequestParam("propsSourceId") Long propsSourceId
  );


}
