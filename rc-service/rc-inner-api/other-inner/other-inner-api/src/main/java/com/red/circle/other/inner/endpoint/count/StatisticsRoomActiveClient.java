package com.red.circle.other.inner.endpoint.count;

import com.red.circle.other.inner.endpoint.count.api.StatisticsRoomActiveClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 统计房间活跃.
 *
 * <AUTHOR> on 2023/11/16
 */
@FeignClient(name = "statisticsRoomActiveClient", url = "${feign.other.url}" +
    StatisticsRoomActiveClientApi.API_PREFIX)
public interface StatisticsRoomActiveClient extends StatisticsRoomActiveClientApi {

}
