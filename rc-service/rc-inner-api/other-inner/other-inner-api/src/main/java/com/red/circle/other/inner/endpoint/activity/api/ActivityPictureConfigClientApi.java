package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.activity.ActivityPictureQryCmd;
import com.red.circle.other.inner.model.dto.activity.ActivityPictureConfigDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 活动图片配置
 *
 * <AUTHOR> on 2024/1/29
 */
public interface ActivityPictureConfigClientApi {

  String API_PREFIX = "/activity-picture-config/client";

  @PostMapping("/getActivityPicture")
  ResultResponse<PageResult<ActivityPictureConfigDTO>> getActivityPicture(
      @RequestBody ActivityPictureQryCmd query);

  @PostMapping("/addActivityPicture")
  ResultResponse<Void> addActivityPicture(@RequestBody ActivityPictureConfigDTO dto);

  @GetMapping("/removeActivityPicture")
  ResultResponse<Void> removeActivityPicture(@RequestParam("id") Long id);

  @PostMapping("/updateActivityPicture")
  ResultResponse<Void> updateActivityPicture(@RequestBody ActivityPictureConfigDTO dto);
}
