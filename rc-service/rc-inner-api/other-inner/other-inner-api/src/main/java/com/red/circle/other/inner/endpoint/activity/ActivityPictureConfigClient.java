package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.ActivityPictureConfigClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 活动图片配置
 * <AUTHOR> on 2024/1/29
 */
@FeignClient(name = "activityPictureConfigClient", url = "${feign.other.url}" +
        ActivityPictureConfigClientApi.API_PREFIX)
public interface ActivityPictureConfigClient extends ActivityPictureConfigClientApi {

}
