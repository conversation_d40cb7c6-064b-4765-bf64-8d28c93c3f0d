package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.ActivityHallFameClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 名人堂.
 *
 * <AUTHOR> on 2023/10/9
 */
@FeignClient(name = "activityHallFameClient", url = "${feign.other.url}" +
        ActivityHallFameClientApi.API_PREFIX)
public interface ActivityHallFameClient extends ActivityHallFameClientApi {

}
