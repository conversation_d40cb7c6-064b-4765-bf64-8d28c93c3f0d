package com.red.circle.other.inner.endpoint.count.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.dto.count.StatisticsRoomActiveDTO;
import java.util.Date;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 统计房间活跃.
 *
 * <AUTHOR> on 2023/11/16
 */
public interface StatisticsRoomActiveClientApi {

  String API_PREFIX = "/statistics/room-active/client";

  /**
   * 获取房间获取情况.
   */
  @GetMapping("/listByTimeRange")
  ResultResponse<List<StatisticsRoomActiveDTO>> listByTimeRange(
      @RequestParam("roomId") Long roomId,
      @RequestParam("startTime") Date startTime,
      @RequestParam("endTime") Date endTime
  );
}
