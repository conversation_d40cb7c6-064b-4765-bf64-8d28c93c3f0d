package com.red.circle.other.inner.endpoint.approval.api;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.cmd.approval.ApprovalUserViolationHistoryQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalUserViolationHistoryDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 审批违规历史记录
 *
 * <AUTHOR> on 2024/1/22
 */
public interface ApprovalUserViolationHistoryClientApi {

  String API_PREFIX = "/approval-user-violation-history/client";

  @PostMapping("/pageHistory")
  ResultResponse<PageResult<ApprovalUserViolationHistoryDTO>> pageHistory(
      @RequestBody ApprovalUserViolationHistoryQryCmd query);
}
