package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.AppRankCountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * app 排行榜.
 *
 * <AUTHOR> on 2023/8/16
 */
@FeignClient(name = "appRankCountClient", url = "${feign.other.url}" +
        AppRankCountClientApi.API_PREFIX)
public interface AppRankCountClient extends AppRankCountClientApi {

}
