package com.red.circle.other.inner.endpoint.approval;

import com.red.circle.other.inner.endpoint.approval.api.ApprovalUserViolationHistoryClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 审批违规历史记录
 *
 * <AUTHOR> on 2024/1/22
 */
@FeignClient(name = "approvalUserViolationHistoryClient", url = "${feign.other.url}" +
    ApprovalUserViolationHistoryClientApi.API_PREFIX)
public interface ApprovalUserViolationHistoryClient extends ApprovalUserViolationHistoryClientApi {

}
