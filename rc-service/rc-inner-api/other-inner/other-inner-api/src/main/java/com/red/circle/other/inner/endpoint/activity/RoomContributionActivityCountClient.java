package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.RoomContributionActivityCountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 房间贡献.
 *
 * <AUTHOR> on 2023/11/2
 */
@FeignClient(name = "roomContributionActivityCountClient", url = "${feign.other.url}" +
    RoomContributionActivityCountClientApi.API_PREFIX)
public interface RoomContributionActivityCountClient extends
    RoomContributionActivityCountClientApi {

}
