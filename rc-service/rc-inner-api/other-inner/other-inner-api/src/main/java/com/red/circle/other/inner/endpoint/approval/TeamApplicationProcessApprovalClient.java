package com.red.circle.other.inner.endpoint.approval;

import com.red.circle.other.inner.endpoint.approval.api.TeamApplicationProcessApprovalClientApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "teamApplicationProcessApprovalClient", url = "${feign.other.url}" +
        TeamApplicationProcessApprovalClientApi.API_PREFIX)
public interface TeamApplicationProcessApprovalClient extends
        TeamApplicationProcessApprovalClientApi {


}
