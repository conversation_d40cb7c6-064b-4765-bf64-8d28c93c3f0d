package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.ActivityGlobalizationDescriptionClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 活动国际化语言描述 服务实现类.
 *
 * <AUTHOR> on 2023/10/8
 */
@FeignClient(name = "activityGlobalizationDescriptionClient", url = "${feign.other.url}" +
        ActivityGlobalizationDescriptionClientApi.API_PREFIX)
public interface ActivityGlobalizationDescriptionClient extends
    ActivityGlobalizationDescriptionClientApi {

}
