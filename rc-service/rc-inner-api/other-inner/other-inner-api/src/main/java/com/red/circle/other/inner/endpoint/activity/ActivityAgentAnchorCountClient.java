package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.ActivityAgentAnchorCountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on @date 2024/3/14
 */
@FeignClient(name = "activityAgentAnchorCountClient", url = "${feign.other.url}" +
        ActivityAgentAnchorCountClientApi.API_PREFIX)
public interface ActivityAgentAnchorCountClient extends ActivityAgentAnchorCountClientApi {


}
