package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 累积充值.
 *
 * <AUTHOR> on 2023/8/18
 */
public interface CumulativeRechargeClientApi {

  String API_PREFIX = "/cumulative-recharge/client";


  /**
   * 累计今天充值金额.
   *
   * @param userId        用户id
   * @param rechargeScore 充值积分
   */
  @GetMapping("/incrTodayRechargeScore")
  ResultResponse<Void> incrTodayRechargeScore(@RequestParam("userId") Long userId,
      @RequestParam("rechargeScore") Double rechargeScore);

  /**
   * 扣除今天充值金额.
   *
   * @param userId        用户id
   * @param rechargeScore 充值积分
   */
  @GetMapping("/decrTodayRechargeScore")
  ResultResponse<Void> decrTodayRechargeScore(@RequestParam("userId") Long userId,
      @RequestParam("rechargeScore") Double rechargeScore);

}
