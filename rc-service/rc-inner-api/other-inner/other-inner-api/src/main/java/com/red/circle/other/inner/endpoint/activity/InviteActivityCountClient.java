package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.InviteActivityCountClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on 2023/5/26
 */
@FeignClient(name = "inviteActivityCountClient", url = "${feign.other.url}" +
    InviteActivityCountClientApi.API_PREFIX)
public interface InviteActivityCountClient extends InviteActivityCountClientApi {

}
