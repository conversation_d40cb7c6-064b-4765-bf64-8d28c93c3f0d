package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.ResultResponse;
import com.red.circle.other.inner.model.dto.activity.InviteExtractRecordCountDTO;
import com.red.circle.other.inner.model.dto.activity.InviteRegisterRecordCountDTO;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 邀请注册 活动.
 *
 * <AUTHOR> on 2023/5/26
 */
public interface InviteActivityCountClientApi {

  String API_PREFIX = "/invite/activity/count";

  /**
   * 保存邀请记录.
   */
  @PostMapping("/add")
  ResultResponse<Void> add(@RequestBody @Validated InviteRegisterRecordCountDTO count);

  /**
   * 增加金额
   */
  @PostMapping("/inc")
  ResultResponse<Void> inc(
      @RequestParam("toUserId") Long toUserId,
      @RequestParam("quantity") Long quantity
  );

  /**
   * 获得邀请记录.
   */
  @GetMapping("/list/by-user")
  ResultResponse<List<InviteRegisterRecordCountDTO>> list(@RequestParam("userId") Long userId);

  /**
   * 获取记录信息.
   */
  @GetMapping("/list/more-condition")
  ResultResponse<List<InviteRegisterRecordCountDTO>> list(@RequestParam("userId") Long userId,
      @RequestParam("toUserId") Long toUserId,
      @RequestParam("startTime") LocalDateTime startTime,
      @RequestParam("endTime") LocalDateTime endTime);

  /**
   * 增加领取记录
   */
  @PostMapping("/add/extract")
  ResultResponse<Void> addExtract(
      @RequestParam("userId") Long userId,
      @RequestParam("quantity") Long quantity,
      @RequestParam("sysOrigin") String sysOrigin
  );

  /**
   * 获得领取记录.
   */
  @GetMapping("/list/extract")
  ResultResponse<List<InviteExtractRecordCountDTO>> listExtract(
      @RequestParam("sysOriginName") String sysOriginName);

}
