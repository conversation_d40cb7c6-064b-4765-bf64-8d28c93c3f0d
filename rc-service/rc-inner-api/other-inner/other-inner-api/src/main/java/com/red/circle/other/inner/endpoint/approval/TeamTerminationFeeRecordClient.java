package com.red.circle.other.inner.endpoint.approval;

import com.red.circle.other.inner.endpoint.approval.api.TeamTerminationFeeRecordClientApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "teamTerminationFeeRecordClient", url = "${feign.other.url}" +
        TeamTerminationFeeRecordClientApi.API_PREFIX)
public interface TeamTerminationFeeRecordClient extends TeamTerminationFeeRecordClientApi {

}
