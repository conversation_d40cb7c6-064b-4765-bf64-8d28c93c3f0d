package com.red.circle.other.inner.endpoint.activity.api;

import com.red.circle.framework.dto.ResultResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 排行榜统计.
 *
 * <AUTHOR> on 2023/8/16
 */
public interface AppRankCountClientApi {

  String API_PREFIX = "/app-rank-count/client";

  /**
   * 充值排行榜.
   */
  @GetMapping("/incrRechargeQuantity")
  ResultResponse<Void> incrRechargeQuantity(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("userId") Long userId,
      @RequestParam("quantity") Long quantity
  );

  /**
   * 扣除排行榜.
   */
  @GetMapping("/decrRechargeQuantity")
  ResultResponse<Void> decrRechargeQuantity(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("userId") Long userId,
      @RequestParam("quantity") Long quantity
  );

  /**
   * 魅力.
   */
  @GetMapping("/incrCharmQuantity")
  ResultResponse<Void> incrCharmQuantity(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("userId") Long userId,
      @RequestParam("quantity") Long quantity
  );

  /**
   * 财富.
   */
  @GetMapping("/incrWealthQuantity")
  ResultResponse<Void> incrWealthQuantity(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("userId") Long userId,
      @RequestParam("quantity") Long quantity
  );

  /**
   * 房间财富.
   */
  @GetMapping("/incrRoomWealthQuantity")
  ResultResponse<Void> incrRoomWealthQuantity(
      @RequestParam("sysOrigin") String sysOrigin,
      @RequestParam("userId") Long userId,
      @RequestParam("quantity") Long quantity
  );

}
