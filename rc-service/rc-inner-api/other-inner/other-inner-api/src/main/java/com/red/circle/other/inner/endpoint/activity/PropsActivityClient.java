package com.red.circle.other.inner.endpoint.activity;

import com.red.circle.other.inner.endpoint.activity.api.PropsActivityClientApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * 道具活动配置服务.
 *
 * <AUTHOR> on 2023/6/5
 */
@FeignClient(name = "propsActivityCnfClient", url = "${feign.other.url}" +
    PropsActivityClientApi.API_PREFIX)
public interface PropsActivityClient extends PropsActivityClientApi {


}
