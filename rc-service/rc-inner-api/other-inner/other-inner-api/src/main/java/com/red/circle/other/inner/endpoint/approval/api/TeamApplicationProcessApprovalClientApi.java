package com.red.circle.other.inner.endpoint.approval.api;

import com.red.circle.other.inner.model.dto.agency.agency.TeamApplicationProcessApprovalDTO;
import org.springframework.web.bind.annotation.PostMapping;

public interface TeamApplicationProcessApprovalClientApi {

  String API_PREFIX = "/team-approval-not-pass/client";

  @PostMapping("/add")
  TeamApplicationProcessApprovalDTO add(TeamApplicationProcessApprovalDTO param);
}
