package com.red.circle.other.inner.endpoint.approval;


import com.red.circle.other.inner.endpoint.approval.api.TeamApplicationProcessClientApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "teamApplicationProcessClient", url = "${feign.other.url}" +
        TeamApplicationProcessClientApi.API_PREFIX)
public interface TeamApplicationProcessClient extends TeamApplicationProcessClientApi {

}
