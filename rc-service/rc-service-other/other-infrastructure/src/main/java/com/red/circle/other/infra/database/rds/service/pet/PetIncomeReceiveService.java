package com.red.circle.other.infra.database.rds.service.pet;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.pet.PetIncomeReceive;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 宠物收益领取记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface PetIncomeReceiveService extends BaseService<PetIncomeReceive> {

  /**
   * 获取用户收益情况.
   *
   * @param userId 用户id
   * @param lastId 最后一条记录id
   * @return list
   */
  List<PetIncomeReceive> flowIncome(Long userId, Long lastId);

  /**
   * 获取用户被偷记录.
   *
   * @param userId 用户id
   * @param lastId 最后一条记录id
   * @return list
   */
  List<PetIncomeReceive> flowStolen(Long userId, Long lastId);

  /**
   * 验证用户是否没有偷过，可以偷的.
   *
   * @param userId           用户id
   * @param incomeDetailsIds 收益id
   * @return map
   */
  Map<Long, Boolean> mapNotStealableByIncomeDetailsIds(Long userId, Set<Long> incomeDetailsIds);

}
