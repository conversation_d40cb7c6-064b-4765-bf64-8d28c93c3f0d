package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLudoPlayers;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * ludo游戏玩家 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface GameLudoPlayersService extends BaseService<GameLudoPlayers> {

  List<GameLudoPlayers> listByGameIds(Set<Long> gameIds);
}
