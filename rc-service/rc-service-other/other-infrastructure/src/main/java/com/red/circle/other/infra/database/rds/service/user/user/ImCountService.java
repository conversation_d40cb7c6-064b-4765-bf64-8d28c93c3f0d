package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.ImCount;

/**
 * <p>
 * 用户im聊天统计 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
public interface ImCountService extends BaseService<ImCount> {

  /**
   * 查询用户im统计信息
   */
  ImCount getByUserIds(Long userId, Long acceptUserId, String originName);


  /**
   * 标记已点亮爱心
   */
  void updateByIds(Long cmdOriginUserId, Long acceptUserId, String originName);
}
