package com.red.circle.other.infra.gateway.props;

import com.red.circle.other.domain.gateway.props.PropsNobleVipGateway;
import com.red.circle.other.infra.convertor.material.PropsNobleVipInfraConvertor;
import com.red.circle.other.infra.database.rds.entity.props.PropsBackpack;
import com.red.circle.other.infra.database.rds.entity.props.PropsNobleVipAbility;
import com.red.circle.other.infra.database.rds.service.props.PropsBackpackService;
import com.red.circle.other.infra.database.rds.service.props.PropsNobleVipAbilityService;
import com.red.circle.other.infra.gateway.assembly.NobleVipAbilityAssembly;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import com.red.circle.other.inner.model.dto.material.props.NobleVipAbility;
import com.red.circle.other.inner.model.dto.material.props.PropsNobleVipAbilityDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 贵族vip.
 *
 * <AUTHOR> on 2023/11/7
 */
@Component
@RequiredArgsConstructor
public class PropsNobleVipGatewayImpl implements PropsNobleVipGateway {

  private final PropsBackpackService propsBackpackService;
  private final NobleVipAbilityAssembly nobleVipAbilityAssembly;
  private final PropsNobleVipInfraConvertor propsNobleVipInfraConvertor;
  private final PropsNobleVipAbilityService propsNobleVipAbilityService;

  @Override
  public void addOrUpdateAbility(PropsNobleVipAbilityDTO dto) {
    propsNobleVipAbilityService.saveOrUpdate(
        propsNobleVipInfraConvertor.toPropsNobleVipAbility(dto)
    );
  }

  @Override
  public boolean existsNotExpiredNobleVip(Long userId) {
    return propsBackpackService.existsNotExpiredProps(userId, PropsCommodityType.NOBLE_VIP);
  }

  @Override
  public PropsNobleVipAbilityDTO getUserMaxAbilityDTO(Long userId) {
    List<PropsBackpack> propsBackpacks = propsBackpackService
        .listNotExpiredByUserIdAndType(userId, PropsCommodityType.NOBLE_VIP);

    if (CollectionUtils.isEmpty(propsBackpacks)) {
      return null;
    }

    List<PropsNobleVipAbility> nobleVipAbilities = propsNobleVipAbilityService
        .listByIds(
            propsBackpacks.stream().map(PropsBackpack::getPropsId).toList());

    if (CollectionUtils.isEmpty(nobleVipAbilities)) {
      return null;
    }

    return propsNobleVipInfraConvertor.toPropsNobleVipAbilityDTO(nobleVipAbilities.stream()
        .filter(nobleVipAbility -> Objects.nonNull(nobleVipAbility.getVipLevel()))
        .max(Comparator.comparing(PropsNobleVipAbility::getVipLevel))
        .orElse(null));
  }

  @Override
  public PropsNobleVipAbilityDTO getAbilityDTO(Long sourceId) {
    return propsNobleVipInfraConvertor.toPropsNobleVipAbilityDTO(
        propsNobleVipAbilityService.getById(sourceId));
  }

  @Override
  public List<PropsNobleVipAbilityDTO> listAbilityDTOByIds(Set<Long> sourceIds) {
    return propsNobleVipInfraConvertor.toListPropsNobleVipAbilityDTO(
        propsNobleVipAbilityService.listByIds(sourceIds));
  }

  @Override
  public Map<Long, PropsNobleVipAbilityDTO> mapAbilityDTOByIds(Set<Long> sourceIds) {
    return propsNobleVipInfraConvertor.toMapPropsNobleVipAbilityDTO(
        propsNobleVipAbilityService.mapByIds(sourceIds));
  }

  @Override
  public NobleVipAbility getAbility(Long sourceId) {
    return nobleVipAbilityAssembly.getAbility(sourceId);
  }

  @Override
  public List<NobleVipAbility> listAbility(Set<Long> sourceIds) {
    return nobleVipAbilityAssembly.listAbility(sourceIds);
  }

  @Override
  public Map<Long, NobleVipAbility> mapAbility(Set<Long> sourceIds) {
    return nobleVipAbilityAssembly.mapAbility(sourceIds);
  }

}
