package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserCustomGiftRecord;
import com.red.circle.other.inner.model.cmd.user.UserCustomGiftQryCmd;
import java.util.Map;

/**
 * <p>
 * 用户领取专属礼物记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2024-01-08 18:11
 */
public interface UserCustomGiftRecordService extends BaseService<UserCustomGiftRecord> {

  PageResult<UserCustomGiftRecord> getCustomGift(UserCustomGiftQryCmd query);

  Map<Integer, UserCustomGiftRecord> mapByAmount(Long userId);

}
