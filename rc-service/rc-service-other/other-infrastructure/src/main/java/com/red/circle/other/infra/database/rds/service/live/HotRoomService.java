package com.red.circle.other.infra.database.rds.service.live;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.live.HotRoom;
import java.util.List;

/**
 * <p>
 * 系统热门房间 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
public interface HotRoomService extends BaseService<HotRoom> {

  List<HotRoom> listAvailable(String sysOrigin);
}
