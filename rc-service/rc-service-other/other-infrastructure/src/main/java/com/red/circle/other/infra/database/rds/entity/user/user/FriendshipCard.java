package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户友谊关系卡.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_friendship_card")
public class FriendshipCard extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 发送用户.
   */
  @TableField("send_user_id")
  private Long sendUserId;

  /**
   * 接收用户.
   */
  @TableField("accept_user_id")
  private Long acceptUserId;

  /**
   * 卡片类型.
   */
  @TableField("card_type")
  private String cardType;

  /**
   * 友谊值.
   */
  @TableField("friendship_value")
  private BigDecimal friendshipValue;

  /**
   * 0.发送人 1.接收人.
   */
  @TableField("is_initiate")
  private Boolean initiate;

}
