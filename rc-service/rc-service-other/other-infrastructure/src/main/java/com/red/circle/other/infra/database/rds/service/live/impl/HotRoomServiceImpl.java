package com.red.circle.other.infra.database.rds.service.live.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.live.HotRoomDAO;
import com.red.circle.other.infra.database.rds.entity.live.HotRoom;
import com.red.circle.other.infra.database.rds.service.live.HotRoomService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统热门房间 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Service
public class HotRoomServiceImpl extends BaseServiceImpl<HotRoomDAO, HotRoom> implements
    HotRoomService {

  @Override
  public List<HotRoom> listAvailable(String sysOrigin) {
    return Optional.ofNullable(query()
        .le(HotRoom::getStartTime, TimestampUtils.now())
        .ge(HotRoom::getExpiredTime, TimestampUtils.now())
        .eq(HotRoom::getSysOrigin, sysOrigin)
        .last(PageConstant.MAX_LIMIT)
        .orderByDesc(HotRoom::getWeights)
        .list()).orElse(CollectionUtils.newArrayList());
  }

}
