package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameRoomPkWinnerRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkWinnerRecord;
import com.red.circle.other.infra.database.rds.service.game.GameRoomPkWinnerRecordService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间pk游戏 胜利者表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
@RequiredArgsConstructor
public class GameRoomPkWinnerRecordServiceImpl extends
    BaseServiceImpl<GameRoomPkWinnerRecordDAO, GameRoomPkWinnerRecord> implements
    GameRoomPkWinnerRecordService {

  private final GameRoomPkWinnerRecordDAO gameRoomPkWinnerRecordDAO;

  @Override
  public List<GameRoomPkWinnerRecord> listTop10(String sysOrigin) {
    return query()
        .eq(GameRoomPkWinnerRecord::getSysOrigin, sysOrigin)
        .last(PageConstant.formatLimit(10))
        .orderByDesc(GameRoomPkWinnerRecord::getCreateTime)
        .list();
  }

  @Override
  public List<GameRoomPkWinnerRecord> weekTop(SysOriginPlatformEnum sysOrigin, Boolean isLastWeek) {

    return gameRoomPkWinnerRecordDAO.weekTop(sysOrigin.name(), isLastWeek);
  }

  @Override
  public List<GameRoomPkWinnerRecord> dayTop(SysOriginPlatformEnum sysOrigin, Boolean isYesterday) {

    return gameRoomPkWinnerRecordDAO.dayTop(sysOrigin.name(), isYesterday);
  }

  @Override
  public GameRoomPkWinnerRecord weekSinglesKing(SysOriginPlatformEnum sysOrigin,
      Boolean isLastWeek) {

    return gameRoomPkWinnerRecordDAO.weekSinglesKing(sysOrigin.name(), isLastWeek);
  }

  @Override
  public List<GameRoomPkWinnerRecord> listSpecifiedTimeWeekTop(SysOriginPlatformEnum sysOrigin,
      String startTime, String endTime) {
    return gameRoomPkWinnerRecordDAO.listSpecifiedTimeWeekTop(sysOrigin.name(), startTime, endTime);
  }

  @Override
  public GameRoomPkWinnerRecord getSpecifiedTimeWeekSinglesKing(SysOriginPlatformEnum origin,
      String startTime, String endTime) {
    return gameRoomPkWinnerRecordDAO
        .getSpecifiedTimeWeekSinglesKing(origin.name(), startTime, endTime);
  }
}
