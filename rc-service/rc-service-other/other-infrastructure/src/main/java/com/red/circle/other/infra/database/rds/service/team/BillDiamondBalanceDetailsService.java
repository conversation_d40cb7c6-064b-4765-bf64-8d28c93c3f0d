package com.red.circle.other.infra.database.rds.service.team;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.team.BillDiamondBalance;
import com.red.circle.other.infra.database.rds.entity.team.BillDiamondBalanceDetails;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户账单钻石余额明细 服务类.
 * </p>
 *
 * <AUTHOR> on 2024-05-28 18:08
 */
public interface BillDiamondBalanceDetailsService extends BaseService<BillDiamondBalanceDetails> {

    /**
     * 根据账单归属和用户id查询用户账单余额信息
     *
     * @param sysOrigin 系统
     * @param userId   用户id
     * @param billBelongDay   账单归属(YYYYMMdd)
     * @return 策略
     */
    BillDiamondBalanceDetails getEntityByBillBelongAndUserId(String sysOrigin, Long userId, Integer billBelongDay);

    /**
     * 根据账单归属和用户id查询用户账单余额信息
     *
     * @param sysOrigin 系统
     * @param userId   用户id
     * @param billBelongDay   账单归属(YYYYMMdd)
     * @return 策略
     */
    BillDiamondBalanceDetails getEntityByBillBelongAndUserIdAndBillBelongDay(String sysOrigin, Long userId, Integer billBelongDay);

    /**
     *
     * @param sysOrigin 系统
     * @param billBelong 账单归属(YYYYMM)
     * @param teamId 团队id
     * @return
     */
    List<BillDiamondBalanceDetails> getEntityByBillBelongAndTeamId(String sysOrigin, Integer billBelong, Long teamId);

    void add(BillDiamondBalanceDetails param);

    void addAgent(BillDiamondBalanceDetails param);

    boolean exists(Long userId, Integer billBelong);

    BillDiamondBalanceDetails getBalance(Long userId, Integer billBelong);

    BillDiamondBalanceDetails getBalanceByBillBelongDay(Long userId, Integer billBelongDay);

    BillDiamondBalanceDetails decr(Long userId, BigDecimal amount, Integer billBelong);

    BillDiamondBalanceDetails decrAgent(Long userId, BigDecimal amount, Integer billBelong);

    void removeBatchByTeamIds(List<Long> teamIds, Integer billBelong);

    void removeByUserId(Long userId, Integer billBelong);

    boolean existsBillBelongDay(Long userId, Integer billBelongDay);

    BillDiamondBalanceDetails decrBillBelongDay(Long userId, BigDecimal amount, Integer billBelongDay);

    BillDiamondBalanceDetails decrAgentBillBelongDay(Long userId, BigDecimal amount, Integer billBelongDay);

}
