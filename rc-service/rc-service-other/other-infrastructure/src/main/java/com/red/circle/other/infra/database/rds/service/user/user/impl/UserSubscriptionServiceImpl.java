package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.UserSubscriptionDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserSubscription;
import com.red.circle.other.infra.database.rds.service.user.user.UserSubscriptionService;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户关注列表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-20
 */
@Service
public class UserSubscriptionServiceImpl extends
    BaseServiceImpl<UserSubscriptionDAO, UserSubscription> implements UserSubscriptionService {

  @Override
  public boolean remove(Long userId, Long cancelSubscriptionUserId) {
    return delete()
        .eq(UserSubscription::getUserId, userId)
        .eq(UserSubscription::getSubscribeUserId, cancelSubscriptionUserId)
        .execute();
  }

  @Override
  public void updateMutualRelations(Long userId, Long subUserId, Boolean mutualRelations) {
    update().set(UserSubscription::getMutualRelations, mutualRelations)
        .in(UserSubscription::getUserId, Arrays.asList(userId, subUserId))
        .in(UserSubscription::getSubscribeUserId, Arrays.asList(userId, subUserId))
        .execute();
  }

  @Override
  public boolean notExistsAdd(Long userId, Long subscriptionUserId) {
    try {
      save(new UserSubscription()
          .setUserId(userId)
          .setSubscribeUserId(subscriptionUserId)
          .setMutualRelations(Boolean.FALSE)
      );
      return true;
    } catch (DuplicateKeyException ignore) {
      // ignore
    }
    return false;
  }

  @Override
  public List<UserSubscription> listByUserId(Long userId, Long lastId) {
    return query()
        .eq(UserSubscription::getUserId, userId)
        .lt(Objects.nonNull(lastId), UserSubscription::getId, lastId)
        .orderByDesc(UserSubscription::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }

  @Override
  public List<UserSubscription> getBySubscriptionUserId(Long userId, Long lastId) {
    return query()
        .eq(UserSubscription::getSubscribeUserId, userId)
        .lt(Objects.nonNull(lastId), UserSubscription::getId, lastId)
        .orderByDesc(UserSubscription::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }

  @Override
  public boolean checkSubscription(Long userId, Long subscriptionUserId) {
    return Optional.ofNullable(query()
            .select(UserSubscription::getId)
            .eq(UserSubscription::getUserId, userId)
            .eq(UserSubscription::getSubscribeUserId, subscriptionUserId)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(userSubscription -> Objects.nonNull(userSubscription.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public Map<Long, Boolean> checkMutualSubscription(Long userId, Long subscriptionUserId) {
    UserSubscription subscription = getBySubscription(userId, subscriptionUserId);
    Map<Long, Boolean> resultMap = CollectionUtils.newHashMap();
    if (Objects.nonNull(subscription) && Objects
        .equals(subscription.getMutualRelations(), Boolean.TRUE)) {
      resultMap.put(userId, Boolean.TRUE);
      resultMap.put(subscriptionUserId, Boolean.TRUE);
      return resultMap;
    }

    if (Objects.nonNull(subscription)) {
      resultMap.put(userId, Boolean.TRUE);
      resultMap.put(subscriptionUserId, Boolean.FALSE);
      return resultMap;
    }

    resultMap.put(userId, Boolean.FALSE);
    resultMap.put(subscriptionUserId, checkSubscription(subscriptionUserId, userId));
    return resultMap;
  }

  @Override
  public UserSubscription getBySubscription(Long userId, Long subscriptionUserId) {
    return query()
        .eq(UserSubscription::getUserId, userId)
        .eq(UserSubscription::getSubscribeUserId, subscriptionUserId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public Map<Long, Boolean> mapIsSubscription(Long userId, Set<Long> subscriptionUserIds) {
    List<UserSubscription> userSubscriptions = query()
        .select(UserSubscription::getSubscribeUserId)
        .eq(UserSubscription::getUserId, userId)
        .in(UserSubscription::getSubscribeUserId, subscriptionUserIds)
        .list();
    return Optional.ofNullable(userSubscriptions)
        .map(userSubscriptionList -> userSubscriptionList.stream().collect(
            Collectors.toMap(UserSubscription::getSubscribeUserId, v -> Boolean.TRUE))
        ).orElse(CollectionUtils.newHashMap());
  }

  @Override
  public List<UserSubscription> getAllBySubscriptionUserId(Long userId) {

    return query()
        .eq(UserSubscription::getSubscribeUserId, userId)
        .list();
  }
}
