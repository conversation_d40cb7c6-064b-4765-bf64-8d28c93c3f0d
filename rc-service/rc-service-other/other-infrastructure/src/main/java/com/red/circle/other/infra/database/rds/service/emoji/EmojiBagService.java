package com.red.circle.other.infra.database.rds.service.emoji;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.emoji.EmojiBag;
import java.util.List;

/**
 * <p>
 * 用户表情背包 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface EmojiBagService extends BaseService<EmojiBag> {

  /**
   * 获取用户表情背包.
   *
   * @param userId 用户id
   * @return 背包信息
   */
  List<EmojiBag> listByUserId(Long userId);

  /**
   * 添加用户表情包.
   *
   * @param userId  用户id
   * @param emojiId 表情包
   */
  void add(Long userId, Long emojiId);

  /**
   * 用户是否存在指定表情.
   *
   * @param userId  用户id
   * @param emojiId 表情id
   * @return true存在
   */
  boolean existEmoji(Long userId, Long emojiId);

}
