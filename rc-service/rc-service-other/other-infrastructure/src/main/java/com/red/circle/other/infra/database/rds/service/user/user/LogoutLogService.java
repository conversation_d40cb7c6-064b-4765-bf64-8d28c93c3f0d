package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.LogoutLog;
import com.red.circle.other.inner.model.cmd.user.UserLogoutLogQryCmd;

/**
 * <p>
 * 用户注销记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-09-12 11:01
 */
public interface LogoutLogService extends BaseService<LogoutLog> {

  Boolean getByUserId(Long userId, String logoutType);

  PageResult<LogoutLog> pageUserLogoutLog(UserLogoutLogQryCmd query);
}
