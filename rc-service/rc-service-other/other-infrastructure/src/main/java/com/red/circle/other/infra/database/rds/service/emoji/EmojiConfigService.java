package com.red.circle.other.infra.database.rds.service.emoji;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.emoji.EmojiConfig;
import com.red.circle.other.inner.model.cmd.sys.SysEmojiConfigQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 表情管理 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
public interface EmojiConfigService extends BaseService<EmojiConfig> {

  List<EmojiConfig> listEmojiHistoryVersion(String sysOrigin);

  /**
   * 获取分组下的表情包.
   *
   * @param groupId 分组id
   * @return list
   */
  List<EmojiConfig> listEmojiByGroupId(Long groupId);

  /**
   * 获取表情组信息.
   *
   * @param groupIds 分组id
   * @return map
   */
  Map<Long, List<EmojiConfig>> mapByGroupIds(Set<Long> groupIds);

  /**
   * 根据ids查询数据.
   */
  List<EmojiConfig> listByIds(Set<Long> ids);

  /**
   * 表情列表
   */
  PageResult<EmojiConfig> pageEmojiConfig(SysEmojiConfigQryCmd query);

  List<EmojiConfig> list(String sysOrigin);
}
