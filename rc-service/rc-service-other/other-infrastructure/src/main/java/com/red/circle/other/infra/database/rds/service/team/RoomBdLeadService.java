package com.red.circle.other.infra.database.rds.service.team;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.team.RoomBdLead;

/**
 * <p>
 * BD Lead.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-4-7
 */
public interface RoomBdLeadService extends BaseService<RoomBdLead> {

  boolean checkBdLeader(Long userId);

  RoomBdLead getByUserId(Long userId);

  void deleteById(Long id);

}
