package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityFriendshipCardHistory;
import java.util.List;

/**
 * <p>
 * 用户友谊关系卡 - 活动往日周历史记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
public interface ActivityFriendshipCardHistoryService extends
    BaseService<ActivityFriendshipCardHistory> {

  List<ActivityFriendshipCardHistory> listBySysOrigin(String sysOrigin);

}
