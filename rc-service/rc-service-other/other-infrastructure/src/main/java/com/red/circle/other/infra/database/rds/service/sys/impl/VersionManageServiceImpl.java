package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.VersionManageDAO;
import com.red.circle.other.infra.database.rds.entity.sys.VersionManage;
import com.red.circle.other.infra.database.rds.service.sys.VersionManageService;
import com.red.circle.other.inner.asserts.ImErrorCode;
import com.red.circle.other.inner.model.cmd.sys.VersionManageQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * app版本管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-12
 */
@Service
public class VersionManageServiceImpl extends
    BaseServiceImpl<VersionManageDAO, VersionManage> implements VersionManageService {

  @Override
  public VersionManage getVersion(String sysOrigin, String platform,
      String channel, Boolean review) {
    return query()
        .eq(VersionManage::getSysOrigin, sysOrigin)
        .eq(VersionManage::getPlatform, platform)
        .eq(StringUtils.isNotBlank(channel), VersionManage::getChannel, channel)
        .eq(VersionManage::getReview, review)
        .orderByDesc(VersionManage::getCreateTime)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }


  @Override
  public PageResult<VersionManage> pageVersionManage(VersionManageQryCmd cmd) {
    return query()
        .eq(VersionManage::getSysOrigin, cmd.getSysOrigin())
        .eq(org.apache.commons.lang3.StringUtils.isNotBlank(cmd.getPlatform()),
            VersionManage::getPlatform, cmd.getPlatform())
        .eq(org.apache.commons.lang3.StringUtils.isNotBlank(cmd.getChannel()),
            VersionManage::getChannel, cmd.getChannel())
        .eq(VersionManage::getDel, Boolean.FALSE)
        .orderByDesc(VersionManage::getCreateTime)
        .page(cmd.getPageQuery());
  }

  @Override
  public Boolean saveVersionManage(VersionManage param) {
    if (Objects.equals(param.getReview(), Boolean.TRUE)) {
      VersionManage reviewVersion = query()
          .eq(VersionManage::getSysOrigin, param.getSysOrigin())
          .eq(VersionManage::getPlatform, param.getPlatform())
          .eq(VersionManage::getChannel, param.getChannel())
          .eq(VersionManage::getReview, Boolean.TRUE)
          .eq(VersionManage::getDel, Boolean.FALSE)
          .last(PageConstant.LIMIT_ONE)
          .getOne();
      ResponseAssert.isNull(ImErrorCode.EXISTS_REVIEW_VERSION, reviewVersion);
    }
    return save(param);
  }

  @Override
  public void deleteVersionManage(Long id) {
    boolean isUpdateSuccess = update()
        .set(VersionManage::getDel, Boolean.TRUE)
        .eq(VersionManage::getId, id)
        .eq(VersionManage::getDel, Boolean.FALSE)
        .execute();
    ResponseAssert.equals(CommonErrorCode.DELETE_FAILURE, isUpdateSuccess, Boolean.TRUE);
  }

  @Override
  public void updateVersionManage(VersionManage param) {
    ResponseAssert.notNull(CommonErrorCode.UPDATE_FAILURE, param.getId());
    if (Objects.equals(param.getReview(), Boolean.TRUE)) {
      VersionManage reviewVersion = query()
          .eq(VersionManage::getSysOrigin, param.getSysOrigin())
          .eq(VersionManage::getPlatform, param.getPlatform())
          .eq(VersionManage::getChannel, param.getChannel())
          .eq(VersionManage::getReview, Boolean.TRUE)
          .eq(VersionManage::getDel, Boolean.FALSE)
          .ne(VersionManage::getId, param.getId())
          .last(PageConstant.LIMIT_ONE).getOne();

      ResponseAssert.isNull(ImErrorCode.EXISTS_REVIEW_VERSION, reviewVersion);
    }

    boolean isUpdateSuccess = update()
        .set(VersionManage::getPlatform, param.getPlatform())
        .set(VersionManage::getChannel, param.getChannel())
        .set(VersionManage::getVersion, param.getVersion())
        .set(VersionManage::getForceUpdate, param.getForceUpdate())
        .set(VersionManage::getPatch, param.getPatch())
        .set(VersionManage::getReview, param.getReview())
        .set(VersionManage::getUpdateDescribe, param.getUpdateDescribe())
        .set(VersionManage::getAppType, param.getAppType())
        .set(VersionManage::getApkSize, param.getApkSize())
        .set(VersionManage::getDownloadUrl, param.getDownloadUrl())
        .set(VersionManage::getBuildVersion, param.getBuildVersion())
        .set(VersionManage::getUpdateUser, param.getCreateUser())
        .eq(VersionManage::getId, param.getId())
        .execute();
    ResponseAssert.equals(CommonErrorCode.UPDATE_FAILURE, isUpdateSuccess, Boolean.TRUE);
  }
}
