package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameUserShardsBackpack;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户游戏抽奖碎片背包 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface GameUserShardsBackpackService extends BaseService<GameUserShardsBackpack> {

  /**
   * 获得具体奖品
   */
  GameUserShardsBackpack getOneByConfigIdByUserId(Long rewardConfigId, Long userId);

  /**
   * 增加碎片数量
   */
  void saveOrUpdateByConfigIdByUserId(Long rewardConfigId, Long userId, Integer quantity);

  /**
   * 减少碎片数量
   */
  boolean reduceShardsQuantity(Long id, Long quantity);

  /**
   * map
   */
  Map<Long, GameUserShardsBackpack> mapByUserId(Long userId, Set<Long> configIds);

  /**
   * 根据用户id获得用户碎片总数
   */
  Integer getSumShardsQuantityByUserId(Long userId);

}
