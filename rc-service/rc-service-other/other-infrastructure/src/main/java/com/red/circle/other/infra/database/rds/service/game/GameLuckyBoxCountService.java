package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxCount;
import com.red.circle.other.inner.model.cmd.game.LuckBoxGameQryCmd;
import com.red.circle.other.inner.model.dto.game.GameLuckyBoxCountInfoDTO;
import java.util.List;

/**
 * <p>
 * lucky-box抽奖记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxCountService extends BaseService<GameLuckyBoxCount> {

  List<GameLuckyBoxCount> listByUserId(Long userId, Long lastId);

  List<GameLuckyBoxCount> getAwardRecord();

  PageResult<GameLuckyBoxCount> pageByCondition(LuckBoxGameQryCmd query);

  GameLuckyBoxCountInfoDTO countLuckyBoxGame(LuckBoxGameQryCmd query);
}
