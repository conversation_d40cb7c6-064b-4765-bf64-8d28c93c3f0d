package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityHallFame;
import java.util.List;

/**
 * <p>
 * 活动名人堂 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface ActivityHallFameService extends BaseService<ActivityHallFame> {

  List<ActivityHallFame> listBySysOrigin(String sysOrigin);

}
