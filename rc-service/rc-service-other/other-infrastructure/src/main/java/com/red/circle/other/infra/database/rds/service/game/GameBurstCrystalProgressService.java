package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameBurstCrystalProgress;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 爆水晶游戏进度 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
public interface GameBurstCrystalProgressService extends BaseService<GameBurstCrystalProgress> {

  /**
   * 累计当天房间贡献进度.
   *
   * @param id       记录id
   * @param quantity 数量
   */
  void incrThatDayProgressRate(Long id, BigDecimal quantity);

  /**
   * 创建新的进度.
   *
   * @param progress ignore
   * @return ignore
   */
  GameBurstCrystalProgress saveThatDayCloseProcessing(GameBurstCrystalProgress progress);

  /**
   * 完成游戏.
   *
   * @param roomId 房间id
   */
  void completedGame(Long roomId);

  /**
   * 获取今日房间宝箱进度映射表.
   *
   * @param roomId 房间id
   * @return map
   */
  Map<Integer, GameBurstCrystalProgress> mapThatDayBoxLevel(Long roomId);

  /**
   * 获取水晶游戏最新进度.
   *
   * @param roomId 房间id
   * @return obj
   */
  GameBurstCrystalProgress getThatDayLatestProgress(Long roomId);
}
