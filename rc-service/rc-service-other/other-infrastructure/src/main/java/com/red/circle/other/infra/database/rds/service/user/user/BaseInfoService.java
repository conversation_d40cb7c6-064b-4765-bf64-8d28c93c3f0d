package com.red.circle.other.infra.database.rds.service.user.user;


import com.google.common.collect.Maps;
import com.red.circle.common.business.enums.AccountStatusEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.inner.model.cmd.count.SysOriginUserQryCmd;
import com.red.circle.other.inner.model.dto.pet.UserBaseInfoDTO;
import com.red.circle.tool.core.tuple.ImmutableKeyValuePair;
import com.red.circle.other.infra.database.rds.entity.user.user.BaseInfo;
import com.red.circle.other.inner.model.cmd.user.UserBaseInfoPageQryCmd;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户基础信息表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-26
 */
public interface BaseInfoService extends BaseService<BaseInfo> {

  /**
   * 获取马甲信息.
   */
  List<BaseInfo> listVest(int size);

  /**
   * 获取用户信息.
   */
  List<BaseInfo> listUser(SysOriginUserQryCmd qryCmd);

  /**
   * 归属系统.
   */
  String getSysOrigin(Long userId);

  /**
   * 获取最大的account.
   *
   * @return ignore
   */
  Long maxUserAccount();

  /**
   * 获取最大的account.
   *
   * @param start 开始
   * @param end   结束
   * @return ignore
   */
  Long maxUserAccount(Long start, Long end);

  List<BaseInfo> pageQuery(UserBaseInfoPageQryCmd query);

  /**
   * 修改用户头像
   */
  boolean updateAvatar(Long userId, String avatar);

  /**
   * 修改用户头像,批量.
   */
  boolean updateAvatar(Collection<Long> userIds, String avatar);

  /**
   * 修改昵称.
   */
  boolean updateNickname(Long userId, String nickname);


  /**
   * 修改昵称, 批量.
   */
  boolean updateNicknameBatch(
      Collection<ImmutableKeyValuePair<Long, String>> keyValuePairs);

  /**
   * 解除账号状态
   *
   * @param userId 用户id
   * @return ignore
   */
  boolean removeAccountArchive(Long userId);

  /**
   * 账号解读
   *
   * @param userId 用户id
   * @return ignore
   */
  boolean removeAccountFreeze(Long userId);

  /**
   * 获取用户昵称.
   *
   * @param account 用户id
   * @return 昵称
   */
  String getNicknameByAccount(Long account);

  /**
   * 获取用户信息.
   *
   * @param userIds 用户id
   * @return ignore
   */
  Map<Long, BaseInfo> mapBaseInfo(Collection<Long> userIds);

  /**
   * 修改账户状态.
   *
   * @param userId        用户id
   * @param accountStatus 账号状态
   * @return true/false
   */
  boolean updateAccountStatus(Long userId, AccountStatusEnum accountStatus);

  /**
   * 删除用户.
   *
   * @param userId 用户id
   * @return ignore
   */
  boolean deleteUser(Long userId);

  /**
   * 获取一组用户信息.
   *
   * @param userIds 用户id
   * @return ignore
   */
  List<BaseInfo> listByUserId(Collection<Long> userIds);

  /**
   * 获取一组用户信息.
   *
   * @param accounts 用户账号
   */
  List<BaseInfo> listByAccount(Collection<String> accounts);

  /**
   * 根据账户获取用户信息.
   *
   * @param account 账号
   * @return ignore
   */
  BaseInfo getByAccount(String account);

  /**
   * 根据账户获取用户信息.
   *
   * @param account 账号
   * @return ignore
   */
  BaseInfo getByAccount(String account,String sysOrigin);

  /**
   * 获取cacoo来源账号.
   *
   * @param account 账号
   * @return 用户基本信息
   */
  BaseInfo getByAccountAndLongCacooTrueAndFemale(String account);

  /**
   * 临时矫正数据使用.
   */
  List<BaseInfo> listUserByLastId(Long lastId);

  /**
   * 获取一组用户详细信息,映射
   *
   * @param userIds 用户id
   * @return map
   */
//  @Deprecated
  Map<Long, UserBaseInfoDTO> mapUserBaseInfoDTO(Collection<Long> userIds);

  /**
   * 账号冻结.
   */
  boolean updateAccountFreeze(Long userId, Integer days);

  /**
   * 还原删除用户.
   */
  boolean restoreDel(Long userId);
}
