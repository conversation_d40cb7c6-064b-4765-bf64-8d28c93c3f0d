package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomSudRecord;

/**
 * <p>
 * 房间游戏sud记录 服务类.
 * </p>
 *
 */
public interface GameRoomSudRecordService extends BaseService<GameRoomSudRecord> {

    GameRoomSudRecord getRoomIdSud(Long roomId);

    void dalRoomSudRecord(Long roomId);

}
