package com.red.circle.other.infra.database.rds.enums.pet;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 宠物阶段.
 *
 * <AUTHOR> on 2021/10/14
 */
public enum PetStageLevelEnum {

  /**
   * 蛋蛋.
   */
  EGGING(1),

  /**
   * 童年.
   */
  CHILDHOOD(2),

  /**
   * 成年.
   */
  ALDULT(3),

  /**
   * 养成.
   */
  FINISH(4);

  private final int level;

  PetStageLevelEnum(int level) {
    this.level = level;
  }

  public int getLevel() {
    return level;
  }

  public static int getLevel(String name) {
    try {
      return PetStageLevelEnum.valueOf(name).getLevel();
    } catch (Exception ex) {
      // ignore
    }
    return 0;
  }

  public static List<PetStageLevelEnum> getCharges() {
    return Arrays.stream(PetStageLevelEnum.values())
        .filter(petStageLevelEnum -> !Objects.equals(petStageLevelEnum, PetStageLevelEnum.EGGING))
        .collect(Collectors.toList());
  }

  public static String getNameByLevel(Integer level) {
    return Optional.ofNullable(getByLevel(level))
        .map(PetStageLevelEnum::name)
        .orElse("");
  }

  public static PetStageLevelEnum getByLevel(Integer level) {
    return Arrays.stream(PetStageLevelEnum.values())
        .filter(petStageLevelEnum -> Objects.equals(petStageLevelEnum.getLevel(), level))
        .findFirst()
        .orElse(null);
  }
}
