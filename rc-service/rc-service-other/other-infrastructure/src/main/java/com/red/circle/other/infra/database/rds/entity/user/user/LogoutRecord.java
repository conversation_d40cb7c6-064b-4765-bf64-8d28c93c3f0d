package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统注销记录.
 * </p>
 *
 * <AUTHOR> on 2019-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_logout_record")
public class LogoutRecord implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 被注销的用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * ip.
   */
  @TableField("ip")
  private String ip;

  /**
   * 操作注销平台 'Android','iOS'.
   */
  @TableField("platform")
  private String platform;

  /**
   * 注销时间.
   */
  @TableField("logout_time")
  private Timestamp logoutTime;

}
