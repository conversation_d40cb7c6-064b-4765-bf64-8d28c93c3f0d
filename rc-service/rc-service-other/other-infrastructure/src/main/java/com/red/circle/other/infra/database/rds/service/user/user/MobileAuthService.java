package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.MobileAuth;
import com.red.circle.other.inner.model.dto.user.account.UserMobileUpdateCmd;

/**
 * <p>
 * 用户手机号注册 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-25
 */
public interface MobileAuthService extends BaseService<MobileAuth> {

  /**
   * 删除用户认证-逻辑删除.
   *
   * @param userId 用户id
   */
  void deleteLogicByUserId(Long userId);

  /**
   * 恢复用户认证-逻辑删除.
   *
   * @param userId 用户id
   */
  void restoreLogicByUserId(Long userId);

  /**
   * 删除用户认证-物理删除.
   */
  void deleteByUserId(Long userId);

  /**
   * 是否存在手机号码.
   *
   * @param phonePrefix 手机号码前缀
   * @param phoneNumber 手机号码
   * @return ignore
   */
  MobileAuth getByPhone(Integer phonePrefix, String phoneNumber);

  /**
   * 验证密码是否正确.
   *
   * @param phonePrefix 手机号码前缀
   * @param phoneNumber 手机号码
   * @param password    密码
   * @return 用户id
   */
  Long checkPasswordReturnUserId(Integer phonePrefix, String phoneNumber, String password);

  /**
   * 保存认证信息.
   *
   * @param mobileAuth ignore
   * @return ignore
   */
  boolean saveMobileAuth(MobileAuth mobileAuth);

  /**
   * 重置密码.
   *
   * @param id       记录id
   * @param password 密码
   * @return ignore
   */
  boolean resetPassword(Long id, String password);

  /**
   * 修改用户手机绑定信息,密码.
   */
  boolean updateUserMobileAuthPassword(UserMobileUpdateCmd param);

  /**
   * 添加用户手机绑定信息.
   */
  Boolean addOrUpdateUserMobileAuth(UserMobileUpdateCmd param);

  /**
   * 查询认证信息
   */
  MobileAuth getByUserId(Long userId);
}
