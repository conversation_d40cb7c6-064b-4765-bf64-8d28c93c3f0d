package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.ConsumptionLevel;
import java.math.BigDecimal;

/**
 * <p>
 * 用户消费等级 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-26
 */
public interface ConsumptionLevelService extends BaseService<ConsumptionLevel> {

  /**
   * 获取消费等级信息.
   *
   * @param userId 用户id
   * @return ignore
   */
  ConsumptionLevel getByUserId(Long userId);

  /**
   * 初始化用户等级贡献记录.
   */
  void createConsumption(Long userId);

  /**
   * 累计消费.
   *
   * @param userId   用户id
   * @param quantity 数量
   */
  void incrConsumptionGolds(Long userId, BigDecimal quantity);

  /**
   * 获取用户消费金币.
   *
   * @param userId 用户id
   * @return 金币
   */
  BigDecimal getConsumptionGolds(Long userId);

  /**
   * 消费钻石.
   *
   * @param userId   用户id
   * @param quantity 数量
   */
  void incrConsumptionDiamond(Long userId, BigDecimal quantity);

  /**
   * 获取用户消费钻石.
   *
   * @param userId 用户id
   * @return 金币
   */
  BigDecimal getConsumptionDiamond(Long userId);

}
