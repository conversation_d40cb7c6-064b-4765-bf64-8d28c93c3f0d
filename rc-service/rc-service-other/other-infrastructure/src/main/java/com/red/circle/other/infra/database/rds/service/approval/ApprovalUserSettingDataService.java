package com.red.circle.other.infra.database.rds.service.approval;


import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.common.business.core.enums.DataApprovalTypeEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.approval.ApprovalUserSettingData;
import com.red.circle.other.inner.model.cmd.approval.ApprovalDataCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalProfileDescQryCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalUserProfileQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalUserProfileDTO;
import com.red.circle.other.inner.model.dto.user.approve.UserProfileApproveDTO;

import java.util.List;

/**
 * <p>
 * 审批用户设置资料信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-09
 */
public interface ApprovalUserSettingDataService extends BaseService<ApprovalUserSettingData> {

  /**
   * 修改审批状态.
   *
   * @param userId       用户id
   * @param approvalType 审批类型
   * @param status       状态
   * @param approvalUser 审批人
   */
  void updateApprovalStatus(Long userId, DataApprovalTypeEnum approvalType,
      ApprovalStatusEnum status, Long approvalUser);


  /**
   * 修改审批状态.
   *
   */
  void updateStatus(ApprovalDataCmd cmd);

  /**
   * 审批类型.
   *
   * @param userId      用户id
   * @param approveType 审批类型
   */
  void saveOrUpdateApproval(Long userId, String sysOrigin, DataApprovalTypeEnum approveType);

  /**
   * 设置审批状态.
   *
   * @param userId             用户id
   * @param sysOrigin          系统给来源
   * @param approveType        审批类型
   * @param approvalStatusEnum 状态
   */
  void saveApproval(Long userId, String sysOrigin, DataApprovalTypeEnum approveType,
      ApprovalStatusEnum approvalStatusEnum);

  /**
   * 批量添加.
   *
   * @param userId            用户id
   * @param sysOriginPlatform 来源平台
   * @param approveTypes      审批类型
   */
  void saveBatchApproval(Long userId, String sysOriginPlatform,
      List<DataApprovalTypeEnum> approveTypes);

  /**
   * 累计违规次数.
   *
   * @param userId      用户id
   * @param approveType 审批类型
   */
  void incrNotPassSize(Long userId, DataApprovalTypeEnum approveType);

  /**
   * 修改机器编码.
   *
   * @param userId         用户id
   * @param approveType    审批类型
   * @param approvalStatus 审批状态
   * @param label          标签
   * @return 是否成功
   */
  boolean updateApprovalStatusMachineLabel(Long userId,
      ApprovalStatusEnum approvalStatus,
      DataApprovalTypeEnum approveType,
      String label)
  ;

  /**
   * 验证是否审批通过.
   *
   * @param userId       用户id
   * @param approvalType 审批状态
   * @return 是否成功
   */
  boolean isPass(Long userId, DataApprovalTypeEnum approvalType);

  /**
   * 验证是否审不批通过或者没有上传.
   *
   * @param userId       用户id
   * @param approvalType 审批状态
   * @return 是否成功
   */
  boolean isNoneOrNotPass(Long userId, DataApprovalTypeEnum approvalType);

  void deleteFamilyApproval(Long userId);


  PageResult<ApprovalUserSettingData> pageFamilyApproval(ApprovalProfileDescQryCmd query);

  PageResult<ApprovalUserProfileDTO> pageUserProfileApproval(ApprovalUserProfileQryCmd query);

  List<ApprovalUserSettingData> getListByUserId(Long userId);
}
