package com.red.circle.other.infra.database.rds.service.pet.impl;


import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.convertor.material.PropsActivityInfraConvertor;
import com.red.circle.other.infra.convertor.pet.PetFeedingRecordConvertor;
import com.red.circle.other.infra.database.rds.dao.pet.PetFeedingRecordDAO;
import com.red.circle.other.infra.database.rds.entity.pet.PetFeedingRecord;
import com.red.circle.other.infra.database.rds.service.pet.PetFeedingRecordService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.red.circle.other.infra.database.rds.service.user.user.BaseInfoService;
import com.red.circle.other.inner.model.cmd.pet.PetFeedingRecordCmd;
import com.red.circle.other.inner.model.dto.pet.PetFeedingRecordDTO;
import com.red.circle.other.inner.model.dto.pet.UserBaseInfoDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 宠物喂食记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PetFeedingRecordServiceImpl extends
    BaseServiceImpl<PetFeedingRecordDAO, PetFeedingRecord> implements PetFeedingRecordService {

  private final PetFeedingRecordConvertor petFeedingRecordConvertor;

  private final BaseInfoService baseInfoService;

  public PetFeedingRecordServiceImpl(PetFeedingRecordConvertor petFeedingRecordConvertor, BaseInfoService baseInfoService) {
      this.petFeedingRecordConvertor = petFeedingRecordConvertor;
      this.baseInfoService = baseInfoService;
  }

  @Override
  public List<PetFeedingRecord> flow(Long userId, Long lastId) {
    return query()
        .eq(PetFeedingRecord::getUserId, userId)
        .lt(Objects.nonNull(lastId), PetFeedingRecord::getId, lastId)
        .orderByDesc(PetFeedingRecord::getId)
        .list();
  }

  @Override
  public PageResult<PetFeedingRecordDTO> feedingPage(PetFeedingRecordCmd cmd) {
    PageResult<PetFeedingRecordDTO> recordPage = query()
            .eq(StringUtils.isNotBlank(cmd.getSysOrigin()), PetFeedingRecord::getSysOrigin,
                    cmd.getSysOrigin())
            .eq(Objects.nonNull(cmd.getPetId()), PetFeedingRecord::getPetId, cmd.getPetId())
            .eq(Objects.nonNull(cmd.getUserId()), PetFeedingRecord::getUserId, cmd.getUserId())
            .orderByDesc(PetFeedingRecord::getId)
            .page(cmd.getPageQuery())
            .convert(petFeedingRecordConvertor::toPetFeedingRecord);


    Map<Long, UserBaseInfoDTO> userBaseInfoMap = baseInfoService.mapUserBaseInfoDTO(
            recordPage.getRecords().stream().map(PetFeedingRecordDTO::getUserId)
                    .collect(Collectors.toSet()));

    return recordPage.convert(record -> {
      record.setUserBaseInfo(userBaseInfoMap.get(record.getUserId()));
      return record;
    });
  }
}
