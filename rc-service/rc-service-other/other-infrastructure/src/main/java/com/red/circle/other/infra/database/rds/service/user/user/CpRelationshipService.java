package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.CpRelationship;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户cp关系 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-13
 */
public interface CpRelationshipService extends BaseService<CpRelationship> {

  /**
   * 获取最近cp.
   */
  List<CpRelationship> listLatestCp(String sysOrigin, Integer size);

  /**
   * 获取最近cp, 排除指定用户id.
   */
  List<CpRelationship> listLatestCp(String sysOrigin, Set<Long> excludeUserId, Integer size);

  /**
   * 是否存在cp.
   *
   * @param userIds 用户id
   * @return true 存在，false 不存在
   */
  boolean existsCp(Long userIds);

  /**
   * 是否存在cp.
   *
   * @param userIds 用户id
   * @return true 存在，false 不存在
   */
  boolean existsCp(List<Long> userIds);

  /**
   * 获取cp用户id.
   *
   * @param userId 用户id
   * @return cp id
   */
  Long getCpUserId(Long userId);

  /**
   * 获取cp信息.
   *
   * @param userId 用户id
   * @return cp id
   */
  CpRelationship getByUserId(Long userId);

  /**
   * 获取cp信息.
   *
   * @param cpUserId 用户id
   * @return cp id
   */
  CpRelationship getByCpUserId(Long cpUserId);

  /**
   * 获取cpValId
   *
   * @param userId 用户id
   * @return ignore
   */
  Long getCpValByUserId(Long userId);

  /**
   * 删除cp关系.
   *
   * @param userId 用户id
   */
  void deleteCpRelationship(Long userId);

  /**
   * 组建cp.
   *
   * @param sendApplyUserId   用户id
   * @param acceptApplyUserId cp用户id
   * @param bindCpValId       cp值id
   * @param sysOrigin         系统
   */
  void addCp(Long sendApplyUserId, Long acceptApplyUserId, Long bindCpValId, String sysOrigin);

  /**
   * 获取cp信息.
   *
   * @param userId 用户id param account 用户赠送的礼物价值
   */
  void updateAccountByUserId(Long userId, BigDecimal account);

  /**
   * 获取cp信息.
   *
   * @param userId 用户id
   * @return
   */
  CpRelationship getByUserIdOrCpUserId(Long userId);

}
