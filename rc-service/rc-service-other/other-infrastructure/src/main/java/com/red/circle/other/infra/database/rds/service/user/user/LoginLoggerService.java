package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.LoginLogger;
import com.red.circle.other.inner.model.cmd.user.LoginLogQryCmd;

/**
 * <p>
 * 用户登陆日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
public interface LoginLoggerService extends BaseService<LoginLogger> {

  PageResult<LoginLogger> pageLoginLogger(LoginLogQryCmd cmd);

  /**
   * 根据设备ID查询登录日志
   */
  LoginLogger getByDeviceId(String deviceId);
}
