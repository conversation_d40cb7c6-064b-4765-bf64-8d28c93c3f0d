package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxBountyConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxBountyConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxBountyConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckBoxBountyConfigQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 赏金任务配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxBountyConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxBountyConfigDAO, GameLuckyBoxBountyConfig> implements
    GameLuckyBoxBountyConfigService {

  @Override
  public List<GameLuckyBoxBountyConfig> listBySysOrigin(String sysOriginName) {
    return query().eq(GameLuckyBoxBountyConfig::getSysOrigin, sysOriginName).list();
  }

  @Override
  public Map<Long, GameLuckyBoxBountyConfig> mapByGiftIds(Set<Long> giftIds) {
    return CollectionUtils.isEmpty(giftIds)
        ? Maps.newHashMap()
        : Optional.ofNullable(
                query().in(GameLuckyBoxBountyConfig::getGiftId, giftIds).list()
            ).map(gameLuckyBoxBountyConfigs -> gameLuckyBoxBountyConfigs.stream()
                .collect(Collectors.toMap(GameLuckyBoxBountyConfig::getGiftId, v -> v)))
            .orElse(Maps.newHashMap());
  }

  @Override
  public Map<Long, GameLuckyBoxBountyConfig> getMap(String sysOriginName) {
    return Optional.ofNullable(
            query().eq(GameLuckyBoxBountyConfig::getSysOrigin, sysOriginName).list()
        ).map(gameLuckyBoxBountyConfigs -> gameLuckyBoxBountyConfigs.stream()
            .collect(Collectors.toMap(GameLuckyBoxBountyConfig::getId, v -> v)))
        .orElse(Maps.newHashMap());
  }

  @Override
  public PageResult<GameLuckyBoxBountyConfig> getBountyConfig(GameLuckBoxBountyConfigQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameLuckyBoxBountyConfig::getSysOrigin,
            query.getSysOrigin())
        .like(StringUtils.isNotBlank(query.getType()), GameLuckyBoxBountyConfig::getType,
            query.getType())
        .orderByDesc(GameLuckyBoxBountyConfig::getCreateTime)
        .page(query.getPageQuery());
  }
}
