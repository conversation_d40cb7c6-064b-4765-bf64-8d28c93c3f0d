package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.CpValue;
import java.math.BigDecimal;

/**
 * <p>
 * cp值 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
public interface CpValueService extends BaseService<CpValue> {

  /**
   * 累计CP值.
   *
   * @param id  id记录值
   * @param val 值
   */
  void incrValue(Long id, BigDecimal val);

  void incrBlessValue(Long id, BigDecimal val);

  /**
   * 获取cp值.
   *
   * @param id id记录值
   * @return ignore
   */
  BigDecimal getCpVal(Long id);

  /**
   * 删除指定记录.
   */
  void deleteById(Long id);

}
