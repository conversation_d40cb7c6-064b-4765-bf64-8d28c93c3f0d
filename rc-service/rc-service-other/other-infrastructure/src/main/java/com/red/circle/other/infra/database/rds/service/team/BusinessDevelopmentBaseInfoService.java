package com.red.circle.other.infra.database.rds.service.team;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.team.BusinessDevelopmentBaseInfo;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 商务拓展基本信息表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
public interface BusinessDevelopmentBaseInfoService extends
    BaseService<BusinessDevelopmentBaseInfo> {

  boolean checkBD(Long userId);

  BusinessDevelopmentBaseInfo getByUserId(Long userId);

  Long countBdByLeaderUserId(Long leadUserId);

  List<BusinessDevelopmentBaseInfo> listBdByLeadUserId(Long leadUserId, Long lastId);
  List<BusinessDevelopmentBaseInfo> listBdByLeadUserId(Long leadUserId);

  void add(BusinessDevelopmentBaseInfo param);

  void incrMemberCount(Set<Long> userIds);

  void decrMemberCount(Set<Long> userIds);

  Map<Long, Long> mapBdLeaderUserId(Set<Long> userIds);

  Map<Long, Boolean> mapCheckBd(Set<Long> userIds);

  /**
   * 移除bd load.
   */
  void removeBdLeader(Long bdLeadUserId);

  /**
   * 根据lead查询名下bd成员数量.
   */
  Map<Long, Long> mapCountByLeaderUserIds(Set<Long> bdLeadUserIds);

}
