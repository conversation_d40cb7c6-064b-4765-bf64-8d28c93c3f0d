package com.red.circle.other.infra.database.rds.service.team.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.team.BusinessDevelopmentBaseInfoDAO;
import com.red.circle.other.infra.database.rds.entity.team.BusinessDevelopmentBaseInfo;
import com.red.circle.other.infra.database.rds.service.team.BusinessDevelopmentBaseInfoService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 商务拓展基本信息表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Service
public class BusinessDevelopmentBaseInfoServiceImpl extends
    BaseServiceImpl<BusinessDevelopmentBaseInfoDAO, BusinessDevelopmentBaseInfo> implements
    BusinessDevelopmentBaseInfoService {

  @Override
  public boolean checkBD(Long userId) {
    return Objects.nonNull(query()
        .eq(BusinessDevelopmentBaseInfo::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }

  @Override
  public BusinessDevelopmentBaseInfo getByUserId(Long userId) {
    return query()
        .eq(BusinessDevelopmentBaseInfo::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public Long countBdByLeaderUserId(Long leadUserId) {
    return query()
        .eq(BusinessDevelopmentBaseInfo::getBdLeadUserId, leadUserId)
        .count();
  }

  @Override
  public List<BusinessDevelopmentBaseInfo> listBdByLeadUserId(Long leadUserId,
      Long lastId) {

    return query()
        .eq(BusinessDevelopmentBaseInfo::getBdLeadUserId, leadUserId)
        .lt(Objects.nonNull(lastId), BusinessDevelopmentBaseInfo::getId, lastId)
        .orderByDesc(BusinessDevelopmentBaseInfo::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }
  @Override
  public List<BusinessDevelopmentBaseInfo> listBdByLeadUserId(Long leadUserId) {

    return query()
        .eq(BusinessDevelopmentBaseInfo::getBdLeadUserId, leadUserId)
        .orderByDesc(BusinessDevelopmentBaseInfo::getId)
        .list();
  }

  @Override
  public void add(BusinessDevelopmentBaseInfo param) {
    param.setId(IdWorkerUtils.getId());
    param.setCreateTime(TimestampUtils.now());
    param.setCreateUser(param.getUserId());
    save(param);
  }


  @Override
  public void incrMemberCount(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return;
    }

    update()
        .setSql("member_quantity=member_quantity+" + 1)
        .in(BusinessDevelopmentBaseInfo::getUserId, userIds)
        .last(PageConstant.LIMIT_ONE)
        .execute();

  }

  @Override
  public void decrMemberCount(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return;
    }

    update()
        .setSql("member_quantity=member_quantity-" + 1)
        .in(BusinessDevelopmentBaseInfo::getUserId, userIds)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }


  @Override
  public Map<Long, Long> mapBdLeaderUserId(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Maps.newHashMap();
    }

    List<BusinessDevelopmentBaseInfo> baseInfos = query()
        .select(BusinessDevelopmentBaseInfo::getUserId,
            BusinessDevelopmentBaseInfo::getBdLeadUserId)
        .in(BusinessDevelopmentBaseInfo::getUserId, userIds)
        .list();

    if (CollectionUtils.isEmpty(baseInfos)) {
      return Maps.newHashMap();
    }
    return baseInfos.stream()
        .filter(Objects::nonNull)
        .filter(base -> Objects.nonNull(base.getBdLeadUserId()))
        .collect(Collectors.toMap(BusinessDevelopmentBaseInfo::getUserId,
            BusinessDevelopmentBaseInfo::getBdLeadUserId));
  }

  @Override
  public Map<Long, Boolean> mapCheckBd(Set<Long> userIds) {

    if (CollectionUtils.isEmpty(userIds)) {
      return Maps.newHashMap();
    }

    List<BusinessDevelopmentBaseInfo> baseInfos = query()
        .select(BusinessDevelopmentBaseInfo::getId, BusinessDevelopmentBaseInfo::getUserId)
        .in(BusinessDevelopmentBaseInfo::getUserId, userIds)
        .list();

    if (CollectionUtils.isEmpty(baseInfos)) {
      return Maps.newHashMap();
    }

    return baseInfos.stream().collect(Collectors.toMap(BusinessDevelopmentBaseInfo::getUserId,
        v -> Objects.nonNull(v.getUserId())));
  }

  @Override
  public void removeBdLeader(Long bdLeadUserId) {
    update()
        .set(BusinessDevelopmentBaseInfo::getBdLeadUserId, null)
        .eq(BusinessDevelopmentBaseInfo::getBdLeadUserId, bdLeadUserId)
        .execute();
  }

  @Override
  public Map<Long, Long> mapCountByLeaderUserIds(Set<Long> bdLeadUserIds) {

    if (CollectionUtils.isEmpty(bdLeadUserIds)) {
      return Maps.newHashMap();
    }

    List<BusinessDevelopmentBaseInfo> bdList = query()
        .in(BusinessDevelopmentBaseInfo::getBdLeadUserId, bdLeadUserIds)
        .list();

    if (CollectionUtils.isEmpty(bdList)) {
      return Maps.newHashMap();
    }

    return bdList.stream().collect(
        Collectors
            .groupingBy(BusinessDevelopmentBaseInfo::getBdLeadUserId, Collectors.counting()));
  }

}
