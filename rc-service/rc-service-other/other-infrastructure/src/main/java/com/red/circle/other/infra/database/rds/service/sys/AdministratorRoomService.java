package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.AdministratorRoom;
import com.red.circle.other.inner.model.cmd.sys.SysAdministratorRoomCmd;

/**
 * <p>
 * APP管理员房间关系表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface AdministratorRoomService extends BaseService<AdministratorRoom> {

  /**
   * 获取用户负责的房间信息.
   *
   * @param userId 用户id.
   * @param roomId 房间id.
   * @return true 存在 .
   */
  Boolean isRoomExist(Long userId, Long roomId);

  void addSysAdministratorRoom(SysAdministratorRoomCmd sysAdministratorRoomDTO);

  void deleteSysAdministratorRoom(Long id);
}
