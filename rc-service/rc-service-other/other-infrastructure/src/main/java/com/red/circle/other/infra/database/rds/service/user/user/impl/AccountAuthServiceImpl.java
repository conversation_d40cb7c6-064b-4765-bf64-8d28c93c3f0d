package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.AccountAuthDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.AccountAuth;
import com.red.circle.other.infra.database.rds.service.user.user.AccountAuthService;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 用户账号认证 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-09-13 11:43
 */
@Service
@RequiredArgsConstructor
public class AccountAuthServiceImpl extends BaseServiceImpl<AccountAuthDAO, AccountAuth> implements
    AccountAuthService {

  private final PasswordEncoder passwordEncoder;

  @Override
  public void saveAccountAuth(AccountAuth accountAuth) {
    accountAuth.setPwd(passwordEncoder.encode(accountAuth.getPwd()));
    super.save(accountAuth);

  }

  @Override
  public AccountAuth getByPassword(String pwd, Long userId) {
    return Optional.ofNullable(
        query().eq(AccountAuth::getDel, Boolean.FALSE)
            .eq(AccountAuth::getUserId, userId)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).filter(accountAuth -> passwordEncoder.matches(pwd, accountAuth.getPwd())).orElse(null);
  }

  @Override
  public boolean deleteById(Long id) {
    return delete().eq(AccountAuth::getId, id).execute();
  }

  @Override
  public AccountAuth getByUserId(Long userId) {
    return query().eq(AccountAuth::getUserId, userId)
        .last(PageConstant.LIMIT_ONE).getOne();
  }


  @Override
  public void deleteByUserId(Long userId) {
    delete().eq(AccountAuth::getUserId, userId).execute();
  }
}
