package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameTrumpetBlacklistDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameTrumpetBlacklist;
import com.red.circle.other.infra.database.rds.service.game.GameTrumpetBlacklistService;
import com.red.circle.other.inner.model.cmd.game.GameTrumpetBlacklistQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 喇叭-黑名单 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-06 16:18
 */
@Service
@AllArgsConstructor
public class GameTrumpetBlacklistServiceImpl extends
    BaseServiceImpl<GameTrumpetBlacklistDAO, GameTrumpetBlacklist> implements
    GameTrumpetBlacklistService {


  @Override
  public boolean exist(Long userId) {

    return Optional.ofNullable(query()
            .eq(GameTrumpetBlacklist::getUserId, userId)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(blacklist -> Objects.nonNull(blacklist.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public void deleteByUserId(Long userId) {

    delete().eq(GameTrumpetBlacklist::getUserId, userId).last(PageConstant.LIMIT_ONE).execute();
  }

  @Override
  public PageResult<GameTrumpetBlacklist> page(GameTrumpetBlacklistQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getUserId()), GameTrumpetBlacklist::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameTrumpetBlacklist::getSysOrigin,
            query.getSysOrigin())
        .orderByDesc(GameTrumpetBlacklist::getCreateTime)
        .page(query.getPageQuery());
  }


}
