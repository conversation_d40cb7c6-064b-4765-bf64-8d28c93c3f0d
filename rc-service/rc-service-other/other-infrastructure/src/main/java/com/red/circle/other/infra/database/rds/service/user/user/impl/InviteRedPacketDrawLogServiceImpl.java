package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.InviteRedPacketDrawLogDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteRedPacketDrawLog;
import com.red.circle.other.infra.database.rds.service.user.user.InviteRedPacketDrawLogService;
import com.red.circle.other.inner.model.cmd.user.invite.InviteRedPacketDrawLogPageQryCmd;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 邀请新用户-红包抽奖记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@AllArgsConstructor
@Service
public class InviteRedPacketDrawLogServiceImpl extends
    BaseServiceImpl<InviteRedPacketDrawLogDAO, InviteRedPacketDrawLog> implements
    InviteRedPacketDrawLogService {

  @Override
  public List<InviteRedPacketDrawLog> pageLotteryDetails(Long lastId, Long userId) {

    return query()
        .eq(InviteRedPacketDrawLog::getUserId, userId)
        .lt(Objects.nonNull(lastId), InviteRedPacketDrawLog::getId, lastId)
        .orderByDesc(InviteRedPacketDrawLog::getId)
        .last(PageConstant.formatLimit(20))
        .list();
  }

  @Override
  public PageResult<InviteRedPacketDrawLog> pageLog(InviteRedPacketDrawLogPageQryCmd cmd) {

    return query()
        .eq(Objects.nonNull(cmd.getUserId()), InviteRedPacketDrawLog::getUserId,
            cmd.getUserId())
        .eq(StringUtils.isNotBlank(cmd.getSysOrigin()),
            InviteRedPacketDrawLog::getSysOrigin, cmd.getSysOrigin())
        .eq(Objects.nonNull(cmd.getRedPacketId()), InviteRedPacketDrawLog::getRedPacketId,
            cmd.getRedPacketId())
        .eq(StringUtils.isNotBlank(cmd.getAwardType()), InviteRedPacketDrawLog::getAwardType,
            cmd.getAwardType())
        .ge(Objects.nonNull(cmd.getStartTime()), InviteRedPacketDrawLog::getCreateTime,
            cmd.getStartTime())
        .le(Objects.nonNull(cmd.getEndTime()), InviteRedPacketDrawLog::getCreateTime,
            cmd.getEndTime())
        .orderByDesc(InviteRedPacketDrawLog::getCreateTime)
        .page(cmd.getPageQuery());

  }

}
