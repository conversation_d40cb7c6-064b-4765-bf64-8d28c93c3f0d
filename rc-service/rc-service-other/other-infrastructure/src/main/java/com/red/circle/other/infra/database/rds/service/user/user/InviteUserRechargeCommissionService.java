package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteUserRechargeCommission;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 邀请用户充值佣金 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
public interface InviteUserRechargeCommissionService extends
    BaseService<InviteUserRechargeCommission> {

  void add(String sysOrigin, Long userId, Long inviteUserId, BigDecimal commission);


  /**
   * 获得分页数据
   *
   * @param pageNumber   第几页
   * @param inviteUserId 被邀请人用户id
   */
  List<InviteUserRechargeCommission> pageByCommissionDesc(Integer pageNumber, Long inviteUserId);

}
