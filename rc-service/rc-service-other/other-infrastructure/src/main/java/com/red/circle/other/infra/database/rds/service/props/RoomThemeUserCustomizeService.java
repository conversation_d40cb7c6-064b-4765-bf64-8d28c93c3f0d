package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.RoomThemeUserCustomize;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户房间主题 服务类.
 * </p>
 *
 * <AUTHOR> on 2021-01-22
 */
public interface RoomThemeUserCustomizeService extends BaseService<RoomThemeUserCustomize> {

  /**
   * 获取自定义主题
   *
   * @param ids 自定义主题id集合
   * @return ignore
   */
  Map<Long, RoomThemeUserCustomize> mapByIds(Set<Long> ids);

  /**
   * 获取最新的等待审批自定义主题
   *
   * @param userId 用户id
   * @return ignore
   */
  RoomThemeUserCustomize getLatestCustomize(Long userId);
}
