package com.red.circle.other.infra.database.rds.service.emoji.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.emoji.EmojiGroupDAO;
import com.red.circle.other.infra.database.rds.entity.emoji.EmojiGroup;
import com.red.circle.other.infra.database.rds.service.emoji.EmojiGroupService;
import com.red.circle.other.inner.model.cmd.sys.SysEmojiGroupQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统表情包分组 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
@Service
public class EmojiGroupServiceImpl extends BaseServiceImpl<EmojiGroupDAO, EmojiGroup> implements
    EmojiGroupService {

  @Override
  public Map<Long, EmojiGroup> mapByIds(Set<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newHashMap();
    }
    return Optional.ofNullable(
            query()
                .in(EmojiGroup::getId, ids)
                .list())
        .map(emojiGroups -> emojiGroups.stream()
            .collect(Collectors.toMap(EmojiGroup::getId, v -> v)))
        .orElseGet(CollectionUtils::newHashMap);
  }

  @Override
  public Long getSysOriginGroupIdByCode(String sysOrigin, String groupCode) {
    return Optional.ofNullable(
            query()
                .select(EmojiGroup::getId)
                .eq(EmojiGroup::getSysOrigin, sysOrigin)
                .eq(EmojiGroup::getGroupCode, groupCode)
                .eq(EmojiGroup::getShelfStatus, Boolean.TRUE)
                .last(PageConstant.LIMIT_ONE)
                .getOne())
        .map(EmojiGroup::getId)
        .orElse(null);
  }

  @Override
  public EmojiGroup getSysOriginByCode(String sysOrigin, String groupCode) {
    return query()
        .eq(EmojiGroup::getSysOrigin, sysOrigin)
        .eq(EmojiGroup::getGroupCode, groupCode)
        .eq(EmojiGroup::getShelfStatus, Boolean.TRUE)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public List<EmojiGroup> listAll(String sysOrigin) {
    return query()
        .eq(EmojiGroup::getSysOrigin, sysOrigin)
        .eq(EmojiGroup::getShelfStatus, Boolean.TRUE)
        .last(PageConstant.MAX_LIMIT)
        .list();
  }

  @Override
  public PageResult<EmojiGroup> pageEmojiGroup(SysEmojiGroupQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getId()), EmojiGroup::getId, query.getId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), EmojiGroup::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getGroupCode()), EmojiGroup::getGroupCode,
            query.getGroupCode())
        .eq(StringUtils.isNotBlank(query.getGroupName()), EmojiGroup::getGroupName,
            query.getGroupName())
        .eq(Objects.nonNull(query.getShelfStatus()), EmojiGroup::getShelfStatus,
            query.getShelfStatus())
        .eq(Objects.nonNull(query.getKtvStatus()), EmojiGroup::getKtvStatus,
            query.getKtvStatus())
        .page(query.getPageQuery());
  }

  @Override
  public List<EmojiGroup> groupListBySysOrigin(String sysOrigin) {
    return Optional.ofNullable(query()
        .eq(EmojiGroup::getSysOrigin, sysOrigin)
        .orderByDesc(EmojiGroup::getId)
        .list()).orElse(Lists.newArrayList());
  }

  @Override
  public void switchGroupShelfStatus(Long id, Boolean status) {
    update()
        .set(EmojiGroup::getShelfStatus, status)
        .eq(EmojiGroup::getId, id)
        .execute();
  }
}
