package com.red.circle.other.infra.database.rds.service.family.impl;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyBaseInfoDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.infra.database.rds.service.family.FamilyBaseInfoService;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 工会基础信息表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Service
@RequiredArgsConstructor
public class FamilyBaseInfoServiceImpl extends
    BaseServiceImpl<FamilyBaseInfoDAO, FamilyBaseInfo> implements FamilyBaseInfoService {

  private final FamilyBaseInfoDAO familyBaseInfoDAO;

  @Override
  public FamilyBaseInfo getBaseInfoById(Long id) {
    return getById(id);
  }

  @Override
  public Long maxAccount() {
    return familyBaseInfoDAO.maxAccount();
  }

  @Override
  public void update(FamilyBaseInfo baseInfo) {
    update()
        .set(StringUtils.isNotBlank(baseInfo.getFamilyName()), FamilyBaseInfo::getFamilyName,
            baseInfo.getFamilyName())
        .set(StringUtils.isNotBlank(baseInfo.getFamilyAvatar()), FamilyBaseInfo::getFamilyAvatar,
            baseInfo.getFamilyAvatar())
        .set(StringUtils.isNotBlank(baseInfo.getFamilyNotice()), FamilyBaseInfo::getFamilyNotice,
            baseInfo.getFamilyNotice())
        .eq(FamilyBaseInfo::getId, baseInfo.getId())
        .execute();
  }

  @Override
  public Map<Long, FamilyBaseInfo> mapBaseInfo(Collection<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newHashMap();
    }
    return Optional.ofNullable(query().in(FamilyBaseInfo::getId, ids).list())
        .map(baseInfo -> baseInfo.stream().collect(Collectors.toMap(FamilyBaseInfo::getId, v -> v)))
        .orElse(CollectionUtils.newHashMap());
  }

  @Override
  public Map<Long, FamilyBaseInfo> mapByUserIds(Collection<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Maps.newHashMap();
    }

    return Optional.ofNullable(query().in(FamilyBaseInfo::getCreateUser, userIds).last(
            PageConstant.formatLimit(userIds.size())).list())
        .map(bases -> bases.stream()
            .collect(Collectors.toMap(FamilyBaseInfo::getCreateUser, v -> v)))
        .orElse(Maps.newHashMap());
  }

  @Override
  public List<FamilyBaseInfo> listBySysOrigin(String sysOrigin) {
    if (StringUtils.isBlank(sysOrigin)) {
      return CollectionUtils.newArrayList();
    }
    return Optional.ofNullable(query().eq(FamilyBaseInfo::getSysOrigin, sysOrigin).list())
        .orElse(CollectionUtils.newArrayList());
  }


  @Override
  public PageResult<FamilyBaseInfo> pageData(FamilyBaseInfoQryCmd queryWhere) {
    return getInfoPage(queryWhere);
  }

  @Override
  public void delFamily(Long familyId) {

    delete().eq(FamilyBaseInfo::getId, familyId).execute();
  }

  @Override
  public List<FamilyBaseInfo> listByIds(List<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Lists.newArrayList();
    }
    return Optional.ofNullable(query().in(FamilyBaseInfo::getId, ids).last(
        PageConstant.formatLimit(ids.size())).list()).orElse(Lists.newArrayList());
  }


  private PageResult<FamilyBaseInfo> getInfoPage(FamilyBaseInfoQryCmd queryWhere) {
    return query()
        .eq(Objects.nonNull(queryWhere.getId()), FamilyBaseInfo::getId, queryWhere.getId())
        .eq(Objects.nonNull(queryWhere.getFamilyAccount()), FamilyBaseInfo::getFamilyAccount,
            queryWhere.getFamilyAccount())
        .eq(Objects.nonNull(queryWhere.getUserId()), FamilyBaseInfo::getCreateUser,
            queryWhere.getUserId())
        .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(queryWhere.getSysOrigin()),
            FamilyBaseInfo::getSysOrigin,
            queryWhere.getSysOrigin())
        .ge(Objects.nonNull(queryWhere.startTimeToLocalDateTime()), FamilyBaseInfo::getCreateTime,
            queryWhere.startTimeToLocalDateTime())
        .le(Objects.nonNull(queryWhere.endTimeToLocalDateTime()), FamilyBaseInfo::getCreateTime,
            queryWhere.endTimeToLocalDateTime())
        .orderByDesc(FamilyBaseInfo::getCreateTime)
        .page(queryWhere.getPageQuery());
  }

}
