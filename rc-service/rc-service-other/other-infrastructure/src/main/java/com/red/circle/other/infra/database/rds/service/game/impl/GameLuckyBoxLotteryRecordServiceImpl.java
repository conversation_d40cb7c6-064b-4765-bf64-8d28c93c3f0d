package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxLotteryRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxLotteryRecord;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxLotteryRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * lucky box 抽奖记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
@Service
public class GameLuckyBoxLotteryRecordServiceImpl extends
    BaseServiceImpl<GameLuckyBoxLotteryRecordDAO, GameLuckyBoxLotteryRecord> implements
    GameLuckyBoxLotteryRecordService {

}
