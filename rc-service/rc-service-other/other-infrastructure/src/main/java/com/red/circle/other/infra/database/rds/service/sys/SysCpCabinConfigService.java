package com.red.circle.other.infra.database.rds.service.sys;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.SysCpCabinConfig;
import com.red.circle.other.inner.model.cmd.sys.SysCpCabinConfigQryCmd;

/**
 * <p>
 * CP小屋配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-11-13 16:24
 */
public interface SysCpCabinConfigService extends BaseService<SysCpCabinConfig> {

  PageResult<SysCpCabinConfig> getCpCabin(SysCpCabinConfigQryCmd query);

  void removeCpCabin(Long id);

}
