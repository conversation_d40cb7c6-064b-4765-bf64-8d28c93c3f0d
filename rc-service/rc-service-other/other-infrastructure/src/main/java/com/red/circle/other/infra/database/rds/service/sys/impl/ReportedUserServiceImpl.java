package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.ReportedUserDAO;
import com.red.circle.other.infra.database.rds.entity.sys.ReportedUser;
import com.red.circle.other.infra.database.rds.service.sys.ReportedUserService;
import com.red.circle.other.inner.model.cmd.approval.ApprovalReportedCmd;
import com.red.circle.other.inner.model.cmd.sys.SysReportedQryCmd;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 被举报用户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-31
 */
@Service
public class ReportedUserServiceImpl extends
    BaseServiceImpl<ReportedUserDAO, ReportedUser> implements ReportedUserService {


  @Override
  public PageResult<ReportedUser> pageSysReportedUser(SysReportedQryCmd sysReportedQuery) {
    return query()
        .eq(Objects.nonNull(sysReportedQuery.getUpdateUserId())
            , ReportedUser::getUpdateUser, sysReportedQuery.getUpdateUserId())
        .eq(Objects.nonNull(sysReportedQuery.getReportUserId())
            , ReportedUser::getReportUserId, sysReportedQuery.getReportUserId())
        .eq(Objects.nonNull(sysReportedQuery.getReportedUserId())
            , ReportedUser::getReportedUserId, sysReportedQuery.getReportedUserId())
        .eq(StringUtils.isNotBlank(sysReportedQuery.getSysOrigin())
            , ReportedUser::getSysOrigin, sysReportedQuery.getSysOrigin())
        .eq(Objects.nonNull(sysReportedQuery.getReportType())
            , ReportedUser::getReportType, sysReportedQuery.getReportType())
        .eq(Objects.nonNull(sysReportedQuery.getApprovalStatus())
            , ReportedUser::getApprovalStatus, sysReportedQuery.getApprovalStatus())
        .ge(Objects.nonNull(sysReportedQuery.getStartCreateDate()), ReportedUser::getCreateTime,
            sysReportedQuery.getStartCreateDate())
        .le(Objects.nonNull(sysReportedQuery.getEndCreateDate()), ReportedUser::getCreateTime,
            sysReportedQuery.getEndCreateDate())
        .orderByDesc(ReportedUser::getCreateTime)
        .page(sysReportedQuery.getPageQuery());
  }

  @Override
  public void approvalReported(ApprovalReportedCmd cmd) {
    update()
        .set(ReportedUser::getApprovalStatus,
            Objects.equals(ApprovalStatusEnum.NOT_PASS.name(), cmd.getApprovalStatus()) ? 2 : 1)
        .set(ReportedUser::getUpdateUser, cmd.getApprovalUserId())
        .set(ReportedUser::getUpdateTime, TimestampUtils.now())
        .in(ReportedUser::getId, cmd.getContentIds())
        .execute();
  }
}
