package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxTimeConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxStandardDetailsConfigQryCmd;
import java.util.List;

/**
 * <p>
 * 抽奖频数配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxTimeConfigService extends BaseService<GameLuckyBoxTimeConfig> {

  List<GameLuckyBoxTimeConfig> listByStandardId(Long standardId);

  List<GameLuckyBoxTimeConfig> getByStandardId(GameLuckyBoxStandardDetailsConfigQryCmd build);

  void deleteByStandardId(Long id);
}
