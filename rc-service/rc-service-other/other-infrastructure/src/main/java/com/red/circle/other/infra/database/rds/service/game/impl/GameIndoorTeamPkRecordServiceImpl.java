package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.common.business.core.enums.GameStateEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameIndoorTeamPkRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameIndoorTeamPkRecord;
import com.red.circle.other.infra.database.rds.service.game.GameIndoorTeamPkRecordService;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间内团队PK 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Service
@RequiredArgsConstructor
public class GameIndoorTeamPkRecordServiceImpl extends
    BaseServiceImpl<GameIndoorTeamPkRecordDAO, GameIndoorTeamPkRecord> implements
    GameIndoorTeamPkRecordService {

  private final GameIndoorTeamPkRecordDAO gameIndoorTeamPkRecordDAO;

  @Override
  public GameIndoorTeamPkRecord getByIdByStatus(Long id, GameStateEnum gameStateEnum) {
    return Optional.ofNullable(query()
        .eq(GameIndoorTeamPkRecord::getId, id)
        .eq(GameIndoorTeamPkRecord::getGameStatus, gameStateEnum.name())
        .last(PageConstant.LIMIT_ONE)
        .getOne()).orElse(new GameIndoorTeamPkRecord());
  }

  @Override
  public GameIndoorTeamPkRecord getByRoomIdByStatus(Long roomId, GameStateEnum gameStateEnum) {
    return Optional.ofNullable(query()
        .eq(GameIndoorTeamPkRecord::getRoomId, roomId)
        .eq(GameIndoorTeamPkRecord::getGameStatus, gameStateEnum.name())
        .orderByDesc(GameIndoorTeamPkRecord::getCreateTime)
        .last(PageConstant.LIMIT_ONE)
        .getOne()).orElse(new GameIndoorTeamPkRecord());
  }

  @Override
  public GameIndoorTeamPkRecord getEfficientPkByRoomId(Long roomId) {
    return gameIndoorTeamPkRecordDAO.getEfficientPkByRoomId(roomId);
  }

  @Override
  public Boolean checkCreateGame(Long userId) {
    return Objects.isNull(gameIndoorTeamPkRecordDAO.checkCreateGame(userId));
  }

  @Override
  public void updateGameOverById(Long id) {
    update()
        .set(GameIndoorTeamPkRecord::getGameStatus, GameStateEnum.VOLUNTARILY_END.name())
        .eq(GameIndoorTeamPkRecord::getId, id).execute();
  }


  @Override
  public List<GameIndoorTeamPkRecord> listExpiredStartedRecordByNow(Timestamp nowTime) {
    return gameIndoorTeamPkRecordDAO.listExpiredStartedRecordByNow(nowTime);
  }

  @Override
  public List<GameIndoorTeamPkRecord> listExpiredWaitingRecordByNow(Timestamp nowTime) {
    return gameIndoorTeamPkRecordDAO.listExpiredWaitingRecordByNow(nowTime);
  }

  @Override
  public Boolean checkStartGame(Long userId, Long pkId) {
    return Objects.nonNull(query()
        .eq(GameIndoorTeamPkRecord::getCreateUser, userId)
        .eq(GameIndoorTeamPkRecord::getGameStatus, GameStateEnum.WAITING.name())
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }


  @Override
  public Boolean isCanGame(Long userId, Long roomId) {

    if (Objects.isNull(userId) || Objects.isNull(roomId)) {
      return Boolean.FALSE;
    }

    return Objects.isNull(gameIndoorTeamPkRecordDAO.isCanGame(userId, roomId));
  }


}
