package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.PasswordLogDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.PasswordLog;
import com.red.circle.other.infra.database.rds.service.user.user.PasswordLogService;
import com.red.circle.other.inner.model.cmd.user.UserPasswordLogQryCmd;
import java.util.Objects;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 用户密码日志 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-07 12:10
 */
@Service
public class PasswordLogServiceImpl extends BaseServiceImpl<PasswordLogDAO, PasswordLog> implements
    PasswordLogService {

  @Override
  public PageResult<PasswordLog> pageListQuery(UserPasswordLogQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), PasswordLog::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getUserId()), PasswordLog::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotBlank(query.getBusinessType()), PasswordLog::getBusinessType,
            query.getBusinessType())
        .eq(StringUtils.isNotBlank(query.getOperateType()), PasswordLog::getOperateType,
            query.getOperateType())
        .orderByDesc(PasswordLog::getCreateTime)
        .page(query.getPageQuery());
  }
}
