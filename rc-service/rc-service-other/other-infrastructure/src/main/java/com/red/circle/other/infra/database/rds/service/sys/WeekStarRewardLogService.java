package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.WeekStarRewardLog;

/**
 * <p>
 * 周星奖励日志 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
public interface WeekStarRewardLogService extends BaseService<WeekStarRewardLog> {

  /**
   * 本周是否存在领取记录.
   *
   * @param userId 用户id
   * @return true 存在，false 不存在
   */
  boolean existsThisWeek(Long userId);
}
