package com.red.circle.other.infra.utils;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.Locale;
import java.util.ResourceBundle;
import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;

@UtilityClass
public class I18nUtils {

    private static final String BUNDLE_NAME = "i18n.messages";

    private static final Map<String, String> REGION_LANGUAGE_MAP = new HashMap<>();

    static {
        // 英语地区
        Arrays.asList("非洲", "德国", "意大利", "马来西亚", "尼泊尔", "其他", "加拿大")
            .forEach(region -> REGION_LANGUAGE_MAP.put(region, "en"));

        // 其他语言地区
        REGION_LANGUAGE_MAP.put("阿拉伯", "ar");
        REGION_LANGUAGE_MAP.put("孟加拉", "bn");
        REGION_LANGUAGE_MAP.put("印度", "hi");
        REGION_LANGUAGE_MAP.put("印尼", "id");
        REGION_LANGUAGE_MAP.put("土耳其", "tr");
        REGION_LANGUAGE_MAP.put("越南", "vi");
        REGION_LANGUAGE_MAP.put("伊朗阿富汗", "fa");
        REGION_LANGUAGE_MAP.put("菲律宾", "fil");
        REGION_LANGUAGE_MAP.put("巴基斯坦", "ur");
        REGION_LANGUAGE_MAP.put("巴西", "pt");
        REGION_LANGUAGE_MAP.put("泰国", "th");
    }

    /**
     * 根据地区获取对应的语言代码
     *
     * @param region 地区名称
     * @return 语言代码，如果未找到对应地区则返回英语(en)
     */
    public String getLanguageByRegion(String region) {
        if (StringUtils.isBlank(region)) {
            return "en";
        }
        return REGION_LANGUAGE_MAP.getOrDefault(region, "en");
    }

    /**
     * 获取道具奖励描述
     *
     * @param propsType 道具类型 (AVATAR_FRAME, RIDE等)
     * @param days 天数
     * @param language 语言代码
     * @return 奖励描述
     */
    public String getPropsRewardContent(String propsType, Integer days, String language) {
        if (StringUtils.isBlank(language)) {
            language = "en";
        }

        try {
            ResourceBundle bundle = ResourceBundle.getBundle(BUNDLE_NAME, new Locale(language));

            // 获取道具类型的多语言描述
            String propsTypeDesc = bundle.getString("props.type." + propsType);

            // 获取内容模板
            String contentTemplate = bundle.getString("props.reward.content");

            // 使用 MessageFormat 替换占位符
            return MessageFormat.format(contentTemplate, propsTypeDesc, days);

        } catch (Exception e) {
            // 如果获取失败,返回英文默认值
            ResourceBundle defaultBundle = ResourceBundle.getBundle(BUNDLE_NAME, new Locale("en"));
            String propsTypeDesc = defaultBundle.getString("props.type." + propsType);
            String contentTemplate = defaultBundle.getString("props.reward.content");
            return MessageFormat.format(contentTemplate, propsTypeDesc, days);
        }
    }

    /**
     * 测试方法
     */
    public static void main(String[] args) {
        // 测试不同语言的道具奖励描述
        System.out.println("===== 测试道具奖励描述 =====");

        // 测试英语
        String enContent = getPropsRewardContent("AVATAR_FRAME", 7, "en");
        System.out.println("英语: " + enContent);

        // 测试阿拉伯语
        String arContent = getPropsRewardContent("RIDE", 14, "ar");
        System.out.println("阿拉伯语: " + arContent);

        // 测试印尼语
        String idContent = getPropsRewardContent("CHAT_BUBBLE", 30, "id");
        System.out.println("印尼语: " + idContent);

        System.out.println("\n===== 测试地区语言映射 =====");

        // 测试地区语言映射
        System.out.println("印度地区语言: " + getLanguageByRegion("印度"));
        System.out.println("德国地区语言: " + getLanguageByRegion("德国"));
        System.out.println("德国地区语言: " + getPropsRewardContent("CHAT_BUBBLE", 30, getLanguageByRegion("印度")));
        System.out.println("未知地区语言: " + getLanguageByRegion("未知地区"));
    }
}
