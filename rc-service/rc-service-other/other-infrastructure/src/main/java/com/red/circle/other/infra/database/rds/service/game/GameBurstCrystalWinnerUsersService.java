package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameBurstCrystalWinnerUsers;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 爆水晶游戏获奖名单 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
public interface GameBurstCrystalWinnerUsersService extends
    BaseService<GameBurstCrystalWinnerUsers> {

  /**
   * 获取游戏开奖用户信息.
   *
   * @param gameIds 游戏id
   * @return map
   */
  Map<Long, List<GameBurstCrystalWinnerUsers>> mapGroupByGameId(Set<Long> gameIds);
}
