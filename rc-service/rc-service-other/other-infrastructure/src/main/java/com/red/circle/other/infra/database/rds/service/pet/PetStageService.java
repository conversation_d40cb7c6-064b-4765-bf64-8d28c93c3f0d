package com.red.circle.other.infra.database.rds.service.pet;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.pet.PetStage;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 宠物阶段 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface PetStageService extends BaseService<PetStage> {

  /**
   * 获取宠物阶段映射.
   *
   * @param petIds 宠物id
   * @return map
   */
  Map<Long, List<PetStage>> mapByPetIds(Set<Long> petIds);

  /**
   * 宠物阶段信息.
   *
   * @param petId 宠物id
   * @return list
   */
  List<PetStage> listByPetIds(Long petId);

  /**
   * 获取指定阶段信息.
   *
   * @param petId 宠物id
   * @param state 阶段
   * @return info
   */
  PetStage getByState(Long petId, String state);

  /**
   * 获取指定宠物的第一个阶段.
   *
   * @param petId 宠物id
   * @return first stage
   */
  PetStage getFirst(Long petId);

  /**
   * 获取下一个等级阶段.
   *
   * @param petId 宠物id
   * @param level 等级
   * @return 阶段
   */
  PetStage getNextPetStage(Long petId, Integer level);
}
