package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户在线时长.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_online_time")
public class OnlineTime implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统来源
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 在线时长，分钟
   */
  @TableField("time")
  private Integer time;

  /**
   * 日期时间戳
   */
  @TableField("date_time")
  private Long dateTime;

  /**
   * 创建时间
   */
  @TableField("create_time")
  private Timestamp createTime;

  /**
   * 修改时间
   */
  @TableField("update_time")
  private Timestamp updateTime;


}
