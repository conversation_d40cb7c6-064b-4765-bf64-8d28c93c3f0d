package com.red.circle.other.infra.database.rds.service.live.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.live.RoomMikeTypeDAO;
import com.red.circle.other.infra.database.rds.entity.live.RoomMikeType;
import com.red.circle.other.infra.database.rds.enums.live.MikeTypeEnum;
import com.red.circle.other.infra.database.rds.enums.live.MikeTypeExpireTypeEnum;
import com.red.circle.other.infra.database.rds.service.live.RoomMikeTypeService;
import com.red.circle.other.inner.model.cmd.live.MikeTypePropsQryCmd;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * 设置活动房间麦类型服务实现 mike_type.
 *
 * <AUTHOR> on 2023/3/24 17:40
 */
@Service
public class RoomMikeTypeServiceImpl extends
    BaseServiceImpl<RoomMikeTypeDAO, RoomMikeType> implements
    RoomMikeTypeService {


  @Override
  public boolean save(Long userId, String sysOrigin, String mikeType,
      BigDecimal roomMikeAmount) {

    RoomMikeType roomMikeType = new RoomMikeType()
        .setUserId(userId)
        .setSysOrigin(sysOrigin)
        .setMikeType(mikeType)
        .setPropsCandy(roomMikeAmount);
    roomMikeType.setExpireType(MikeTypeExpireTypeEnum.PERMANENT.name());
    roomMikeType.setCreateUser(userId);
    roomMikeType.setCreateTime(TimestampUtils.now());
    return save(roomMikeType);
  }

  @Override
  public boolean save(Long userId, String sysOrigin, String mikeType, String duration,
      BigDecimal roomMikeAmount) {

    RoomMikeType roomMikeType = new RoomMikeType()
        .setUserId(userId)
        .setSysOrigin(sysOrigin)
        .setMikeType(mikeType)
        .setPropsCandy(roomMikeAmount);
    roomMikeType.setCreateUser(userId);
    roomMikeType.setCreateTime(TimestampUtils.now());
    if (Objects.equals(duration, MikeTypeEnum.FIFTEEN_DAYS.name())) {
      roomMikeType.setExpireType(MikeTypeExpireTypeEnum.TEMPORARY.name());
      roomMikeType.setExpireTime(TimestampUtils.nowPlusDays(15));
    } else if (Objects.equals(duration, MikeTypeEnum.FOREVER.name())) {
      roomMikeType.setExpireType(MikeTypeExpireTypeEnum.PERMANENT.name());
      roomMikeType.setExpireTime(TimestampUtils.now());
    }
    return save(roomMikeType);
  }

  @Override
  public List<RoomMikeType> listByUserIdRoomMikeTypes(Long userId) {
    return query()
        .and(and -> and.eq(RoomMikeType::getExpireType, "TEMPORARY").gt(RoomMikeType::getExpireTime,
            TimestampUtils.now()).or(or -> or.eq(RoomMikeType::getExpireType, "PERMANENT")))
        .and(and -> and.eq(RoomMikeType::getUserId, userId))
        .list();
  }

  @Override
  public boolean isPurchaseMikeType(Long userId, String mikeType) {

    return Objects.nonNull(query().select(RoomMikeType::getId)
        .eq(RoomMikeType::getUserId, userId)
        .eq(RoomMikeType::getMikeType, mikeType)
        .last(PageConstant.LIMIT_ONE)
        .getOne());

  }

  @Override
  public boolean isPurchaseMikeType(Long userId, String mikeType, String expireType) {

    return Objects.nonNull(query().select(RoomMikeType::getId)
        .eq(RoomMikeType::getUserId, userId)
        .eq(RoomMikeType::getMikeType, mikeType)
        .eq(RoomMikeType::getExpireType, expireType)
        .gt(Objects.equals(expireType, "TEMPORARY"), RoomMikeType::getExpireTime,
            TimestampUtils.now())
        .last(PageConstant.LIMIT_ONE)
        .getOne());

  }

  @Override
  public PageResult<RoomMikeType> pageRoomMikeTypeList(MikeTypePropsQryCmd query) {

    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), RoomMikeType::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getExpireType()), RoomMikeType::getExpireType,
            query.getExpireType())
        .eq(Objects.nonNull(query.getUserId()), RoomMikeType::getUserId, query.getUserId())
        .eq(StringUtils.isNotBlank(query.getMikeType()), RoomMikeType::getMikeType,
            query.getMikeType())
        .orderByDesc(RoomMikeType::getCreateTime)
        .page(query.getPageQuery());
  }
}
