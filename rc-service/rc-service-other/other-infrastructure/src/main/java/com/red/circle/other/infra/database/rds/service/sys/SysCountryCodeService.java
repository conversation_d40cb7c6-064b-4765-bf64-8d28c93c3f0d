package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.SysCountryCode;
import com.red.circle.other.inner.model.cmd.sys.SysCountryCodePageQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
public interface SysCountryCodeService extends BaseService<SysCountryCode> {

  /**
   * 获取指定code国家编码映射信息.
   *
   * @param code 国家编码
   * @return 映射信息
   */
  SysCountryCode getByCode(String code);

  /**
   * 获取开放国家列表.
   *
   * @return 国家信息
   */
  List<SysCountryCode> listOpenCountry();

  /**
   * 获取开放国家列表-top.
   *
   * @return 国家信息
   */
  List<SysCountryCode> listOpenCountryTop(Integer size);

  /**
   * 该区域是否开放-手机号前缀.
   *
   * @param phonePrefix ignore
   * @return true 开发，false 未开放
   */
  boolean checkOpenPhonePrefix(Integer phonePrefix);

  /**
   * 获取国家映射信息.
   */
  Map<Long, SysCountryCode> mapByIdes(Set<Long> countryIds);

  /**
   * 查询国家列表
   */
  List<SysCountryCode> listContent();

  /**
   * 分页查询国家列表
   */
  PageResult<SysCountryCode> sysCountryCodePage(SysCountryCodePageQryCmd query);

  /**
   * 编辑国家资料
   */
  void updateCountryCode(SysCountryCode sysCountryCode);
}
