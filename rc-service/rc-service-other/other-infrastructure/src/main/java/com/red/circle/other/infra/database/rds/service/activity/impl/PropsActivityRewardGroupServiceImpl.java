package com.red.circle.other.infra.database.rds.service.activity.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.PropsActivityRewardGroupDAO;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRewardGroup;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRewardGroupService;
import com.red.circle.other.inner.asserts.GiftErrorCode;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRewardGroupQryCmd;
import com.red.circle.other.inner.model.dto.material.ActivityGroupKeyValueDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动道具奖励配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Service
@RequiredArgsConstructor
public class PropsActivityRewardGroupServiceImpl extends
    BaseServiceImpl<PropsActivityRewardGroupDAO, PropsActivityRewardGroup> implements
    PropsActivityRewardGroupService {


  @Override
  public List<PropsActivityRewardGroup> listByIds(Collection<Long> groupIds) {
    return CollectionUtils.isEmpty(groupIds)
        ? CollectionUtils.newArrayList()
        : Optional.ofNullable(query()
                .in(PropsActivityRewardGroup::getId, groupIds)
                .list())
            .orElseGet(CollectionUtils::newArrayList);
  }

  @Override
  public PropsActivityRewardGroup getDataById(Long groupId) {
    return Objects.isNull(groupId)
        ? new PropsActivityRewardGroup()
        : Optional.ofNullable(query()
                .eq(PropsActivityRewardGroup::getId, groupId).getOne())
            .orElseGet(PropsActivityRewardGroup::new);
  }

  @Override
  public PageResult<PropsActivityRewardGroup> pagePropsActivityRewardGroup(PropsActivityRewardGroupQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()),
            PropsActivityRewardGroup::getSysOrigin, query.getSysOrigin())
        .eq(Objects.nonNull(query.getId()),
            PropsActivityRewardGroup::getId, query.getId())
        .like(StringUtils.isNotBlank(query.getName()), PropsActivityRewardGroup::getName,
            query.getName())
        .eq(Objects.nonNull(query.getShelfStatus()),
            PropsActivityRewardGroup::getShelfStatus, query.getShelfStatus())
        .orderByDesc(PropsActivityRewardGroup::getCreateTime).page(query.getPageQuery());
  }

  @Override
  public void offShelf(Long id, Boolean offShelf) {
    ResponseAssert.notNull(GiftErrorCode.BAD_REQUEST, id);
    update().set(PropsActivityRewardGroup::getShelfStatus, offShelf)
        .eq(PropsActivityRewardGroup::getId, id).execute();
  }

  @Override
  public List<ActivityGroupKeyValueDTO> getActivityGroupKeyValueList() {
    List<PropsActivityRewardGroup> propsActivityRewardGroupList = query()
        .eq(PropsActivityRewardGroup::getShelfStatus, 1).list();

    if (CollectionUtils.isEmpty(propsActivityRewardGroupList)) {
      return Lists.newArrayList();
    }

    return propsActivityRewardGroupList.stream().map(propsActivityRewardGroup -> {
      ActivityGroupKeyValueDTO result = new ActivityGroupKeyValueDTO();
      result.setValue(propsActivityRewardGroup.getId());
      result.setName(propsActivityRewardGroup.getName());
      return result;
    }).collect(Collectors.toList());
  }
}
