package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.OperationExcludeRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.OperationExcludeRecord;
import com.red.circle.other.infra.database.rds.service.user.user.OperationExcludeRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@Service
public class OperationExcludeRecordServiceImpl extends
    BaseServiceImpl<OperationExcludeRecordDAO, OperationExcludeRecord> implements
    OperationExcludeRecordService {

}
