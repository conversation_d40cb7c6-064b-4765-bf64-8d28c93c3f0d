package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsSourceRecord;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 道具资源信息 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-29
 */
public interface PropsSourceRecordService extends BaseService<PropsSourceRecord> {

  /**
   * 获取最便宜的头像框.
   *
   * @return ignore
   */
  PropsSourceRecord getCheapAvatarFrame();

  /**
   * 获取指定一组资源信息映射.
   *
   * @param ids id集合
   * @return map
   */
  Map<Long, PropsSourceRecord> mapByIds(Set<Long> ids);

  /**
   * 获取一组指定道具.
   *
   * @param ids id集合
   * @return list
   */
  List<PropsSourceRecord> listByIds(List<Long> ids);

  /**
   * 获得资源数据
   */
  PropsSourceRecord getPropsById(Long id);

  /**
   * 获取平台道具资源
   */
  List<PropsSourceRecord> listSysOrigin(String sysOrigin, String type);

  /**
   * 获取平台道具资源
   */
  List<PropsSourceRecord> listSysOrigin(String sysOrigin, String type, Set<Long> excludePropsIds);

  /**
   * 资源上下架.
   */
  void offShelf(Long id, Boolean offShelf);

  List<PropsSourceRecord> adminFreePropsList();

}
