package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 邀请新用户-配置.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_config")
public class InviteConfig extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 现金奖项人数-最少.
   */
  @TableField("cash_number_min")
  private Long cashNumberMin;

  /**
   * 现金奖项人数-最多.
   */
  @TableField("cash_number_max")
  private Long cashNumberMax;

  /**
   * 其他奖项人数-最少.
   */
  @TableField("other_number_min")
  private Long otherNumberMin;

  /**
   * 其他奖项人数-最多.
   */
  @TableField("other_number_max")
  private Long otherNumberMax;

  /**
   * 红包总金额.
   */
  @TableField("total_amount")
  private Long totalAmount;

  /**
   * 开红包金额-最少.
   */
  @TableField("open_red_packet_min")
  private BigDecimal openRedPacketMin;

  /**
   * 开红包金额-最多.
   */
  @TableField("open_red_packet_max")
  private BigDecimal openRedPacketMax;

  /**
   * 转盘‘直接提现’奖项最少邀请多少人才能有机会触发.
   */
  @TableField("withdraw_threshold_count")
  private Integer withdrawThresholdCount;

  /**
   * 转盘‘直接提现’奖项触发人次比例.
   */
  @TableField("withdraw_trigger_proportion")
  private Integer withdrawTriggerProportion;

  /**
   * 是否已关闭活动(0.未关闭 1.已关闭).
   */
  @TableField("check_close")
  private Boolean checkClose;

  /**
   * 一个ip最多可集满红包次数.
   */
  @TableField("ip_frequency")
  private Integer ipFrequency;

  /**
   * 一个设备最多可集满红包次数.
   */
  @TableField("device_frequency")
  private Integer deviceFrequency;

  /**
   * 用户账号一天最多可集满红包次数.
   */
  @TableField("day_frequency")
  private Integer dayFrequency;

  /**
   * 用户账号总可集满次数.
   */
  @TableField("total_frequency")
  private Integer totalFrequency;

  /**
   * 区域code, 多个用,分割.
   */
  @TableField("region_code_array")
  private String regionCodeArray;

  /**
   * 新用户可助力天,多个用,分割.
   */
  @TableField("new_user_help_days")
  private String newUserHelpDays;

  /**
   * 金币余额可参加活动
   */
  @TableField("gold_coin_balance")
  private BigDecimal goldCoinBalance;

  /**
   * 注册时间大于多少天可参加活动
   */
  @TableField("reg_date_max")
  private Integer regDateMax;

  /**
   * 红包飘窗是否开启
   */
  @TableField("bay_window")
  private Boolean bayWindow;
}
