package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameBurstCrystalProgressDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameBurstCrystalProgress;
import com.red.circle.other.infra.database.rds.service.game.GameBurstCrystalProgressService;
import com.red.circle.other.inner.enums.game.GameBurstCrystalProgressStatus;
import com.red.circle.tool.core.date.ZonedDateTimeAsiaRiyadhUtils;
import java.math.BigDecimal;
import java.util.Map;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 爆水晶游戏进度 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Service
public class GameBurstCrystalProgressServiceImpl extends
    BaseServiceImpl<GameBurstCrystalProgressDAO, GameBurstCrystalProgress> implements
    GameBurstCrystalProgressService {

  @Override
  public void incrThatDayProgressRate(Long id, BigDecimal quantity) {
    update()
        .setSql("progress_rate=progress_rate+" + quantity)
        .eq(GameBurstCrystalProgress::getNumberDate,
            ZonedDateTimeAsiaRiyadhUtils.nowDateToInt())
        .eq(GameBurstCrystalProgress::getId, id)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public GameBurstCrystalProgress saveThatDayCloseProcessing(
      GameBurstCrystalProgress progress) {
    completedGame(progress.getRoomId());
    GameBurstCrystalProgress newProgress = new GameBurstCrystalProgress()
        .setRuleId(progress.getRuleId())
        .setNumberDate(ZonedDateTimeAsiaRiyadhUtils.nowDateToInt())
        .setRoomId(progress.getRoomId())
        .setBoxLevel(progress.getBoxLevel())
        .setProgressRate(progress.getProgressRate())
        .setStatus(GameBurstCrystalProgressStatus.PROCESSING.name());
    newProgress.setCreateTime(progress.getCreateTime());
    save(newProgress);
    return newProgress;
  }

  @Override
  public void completedGame(Long roomId) {
    update()
        .set(GameBurstCrystalProgress::getStatus, GameBurstCrystalProgressStatus.COMPLETED.name())
        .eq(GameBurstCrystalProgress::getRoomId, roomId)
        .eq(GameBurstCrystalProgress::getStatus, GameBurstCrystalProgressStatus.PROCESSING.name())
        .eq(GameBurstCrystalProgress::getNumberDate, ZonedDateTimeAsiaRiyadhUtils.nowDateToInt())
        .execute();
  }

  private GameBurstCrystalProgress getRoomRuleProgress(Long roomId, Long ruleId) {
    return query()
        .eq(GameBurstCrystalProgress::getRoomId, roomId)
        .eq(GameBurstCrystalProgress::getRuleId, ruleId)
        .eq(GameBurstCrystalProgress::getNumberDate, ZonedDateTimeAsiaRiyadhUtils.nowDateToInt())
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public Map<Integer, GameBurstCrystalProgress> mapThatDayBoxLevel(Long roomId) {
    return query()
        .eq(GameBurstCrystalProgress::getRoomId, roomId)
        .eq(GameBurstCrystalProgress::getNumberDate, ZonedDateTimeAsiaRiyadhUtils.nowDateToInt())
        .last(PageConstant.DEFAULT_LIMIT)
        .list2Map(GameBurstCrystalProgress::getBoxLevel);
  }

  @Override
  public GameBurstCrystalProgress getThatDayLatestProgress(Long roomId) {
    return query()
        .eq(GameBurstCrystalProgress::getRoomId, roomId)
        .eq(GameBurstCrystalProgress::getNumberDate, ZonedDateTimeAsiaRiyadhUtils.nowDateToInt())
        .orderByDesc(GameBurstCrystalProgress::getBoxLevel)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

}
