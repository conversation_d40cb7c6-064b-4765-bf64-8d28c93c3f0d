package com.red.circle.other.infra.database.rds.service.live;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.live.RoomContributionBalance;
import java.math.BigDecimal;

/**
 * <p>
 * 房间贡献总额 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-01
 */
public interface RoomContributionBalanceService extends BaseService<RoomContributionBalance> {

  /**
   * 获取总额数量.
   *
   * @param userId 用户id
   * @return 总额
   */
  BigDecimal getTotalQuantityByUserId(Long userId);

  /**
   * 获取总额数量.
   *
   * @param roomId 房间id
   * @return 总额
   */
  BigDecimal getTotalQuantityRoomId(Long roomId);

  /**
   * 获取余额数量.
   *
   * @param roomId 房间id
   * @return 余额
   */
  BigDecimal getBalanceQuantity(Long roomId);

  /**
   * 获取房间贡献额信息.
   *
   * @param roomId 房间id
   * @return 贡献
   */
  RoomContributionBalance getByRoomId(Long roomId);

  /**
   * 累计总榜.
   *
   * @param roomId   房间id
   * @param userId   用户id
   * @param quantity 数量
   */
  void incrTotalQuantity(Long roomId, Long userId, BigDecimal quantity);

  /**
   * 累计消费总榜.
   *
   * @param roomId   房间id
   * @param quantity 数量
   * @return true 成功 false 失败
   */
  boolean incrWithdrawQuantity(Long roomId, BigDecimal quantity);

  /**
   * 矫正数据.
   */
  void correctionData(Long roomId, BigDecimal quantity);

  /**
   * 矫正反了的数据.
   */
  void correctData(Long oldRoomId, Long newRoomId, BigDecimal val);

}
