package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.common.business.enums.PhotoApprovalStatusEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicPicture;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 动态图片
 * </p>
 *
 * <AUTHOR>
 * @since 2022-4-19
 */
public interface DynamicPictureService extends BaseService<DynamicPicture> {

  /**
   * 根据动态id 获取资源图片地址
   *
   * @param dynamicId 动态id
   * @return ignore
   */
  String displayableFirstCover(Long dynamicId);

  /**
   * 查询照片墙映射
   */
  Map<Long, List<DynamicPicture>> mapByDynamicIds(Set<Long> dynamicIds);

  /**
   * 批量修改照片审核状态信息
   *
   * @param dynamicPictures 照片墙信息
   */
  void updateApprovalBySelective(List<DynamicPicture> dynamicPictures);

  /**
   * 移除图片
   *
   * @param dynamicId 动态id
   */
  void deleteByUserId(Long dynamicId);

  /**
   * 修改违规状态
   *
   * @param ids           资源id集合
   * @param violationEnum 违规状态
   * @return 是否修改成功
   */
  boolean updateViolationStatus(List<Long> ids, PhotoApprovalStatusEnum violationEnum);

  /**
   * 是否存在照片墙
   *
   * @param dynamicId 动态id
   * @return true 存在 false 不存在
   */
  boolean existsPicture(Long dynamicId);

  /**
   * 获取违规图片
   *
   * @param dynamicId 动态id
   * @return ignore
   */
  List<DynamicPicture> listViolationPicture(Long dynamicId);

  /**
   * 获取可预览图片
   *
   * @param dynamicId 动态id
   * @return ignore
   */
  List<DynamicPicture> listPreviewPicture(Long dynamicId);


  /**
   * 获取动态所有正在使用图片,走缓存
   *
   * @param dynamicId 动态id
   * @return ignore
   */
  List<DynamicPicture> listPicture(Long dynamicId);

  /**
   * 获取照片墙疑似违规图片并且是今日上传的
   *
   * @param dynamicId 动态
   * @return ignore
   */
  List<DynamicPicture> listNotPassAndGtToday(Long dynamicId);

  /**
   * 获取一组指定图片，可预览的
   *
   * @param dynamicIds 动态id
   * @return list
   */
  Map<Long, List<DynamicPicture>> mapPreviewGroup(Set<Long> dynamicIds);

  /**
   * 是否存在可预览图片
   *
   * @param dynamicId ignore
   * @return ignore
   */
  boolean existsPreview(Long dynamicId);

  /**
   * 图片不存在或者存在违规
   *
   * @param dynamicId 动态id
   * @return ignore
   */
  boolean existsEmptyOrViolation(Long dynamicId);
}
