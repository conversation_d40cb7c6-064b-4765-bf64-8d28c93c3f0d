package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.AdministratorAuthResource;
import java.util.List;

/**
 * <p>
 * APP权限菜单表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface AdministratorAuthResourceService extends BaseService<AdministratorAuthResource> {

  /**
   * 获取用户授权.
   *
   * @param resourceIds 资源信息.
   * @return 用户列表
   */
  List<AdministratorAuthResource> listUserAuthResource(List<Long> resourceIds);

  /**
   * 获取用户授权资源
   */
  List<AdministratorAuthResource> getSysAdministratorAuthResource();

  /**
   * 修改用户授权资源
   */
  boolean updateSysAdministratorAuthResource(
      AdministratorAuthResource authResource);
}
