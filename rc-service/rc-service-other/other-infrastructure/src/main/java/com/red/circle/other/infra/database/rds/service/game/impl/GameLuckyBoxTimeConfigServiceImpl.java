package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxTimeConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxTimeConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxTimeConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxStandardDetailsConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖频数配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxTimeConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxTimeConfigDAO, GameLuckyBoxTimeConfig> implements
    GameLuckyBoxTimeConfigService {

  @Override
  public List<GameLuckyBoxTimeConfig> listByStandardId(Long standardId) {
    return query().eq(GameLuckyBoxTimeConfig::getStandardId, standardId).list();
  }

  @Override
  public List<GameLuckyBoxTimeConfig> getByStandardId(
      GameLuckyBoxStandardDetailsConfigQryCmd query) {
    return query().eq(
            GameLuckyBoxTimeConfig::getStandardId, query.getStandardId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()),
            GameLuckyBoxTimeConfig::getSysOrigin, query.getSysOrigin()).list();
  }

  @Override
  public void deleteByStandardId(Long standardId) {
    delete().eq(GameLuckyBoxTimeConfig::getStandardId, standardId).execute();
  }
}
