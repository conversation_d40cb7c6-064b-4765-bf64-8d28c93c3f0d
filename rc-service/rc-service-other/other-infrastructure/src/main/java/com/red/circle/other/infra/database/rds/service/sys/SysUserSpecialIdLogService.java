package com.red.circle.other.infra.database.rds.service.sys;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.SysUserSpecialIdLog;
import com.red.circle.other.inner.model.cmd.sys.PageUserSpecialIdCmd;
import java.util.List;

/**
 * <p>
 * 靓号使用日志记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
public interface SysUserSpecialIdLogService extends BaseService<SysUserSpecialIdLog> {

  /**
   * 获取指定数量，最新的账号日志记录.
   */
  List<SysUserSpecialIdLog> listLatest(Long userId, Integer size);

  /**
   * 靓号日志-分页.
   */
  PageResult<SysUserSpecialIdLog> pageSpecialIdLog(PageUserSpecialIdCmd param);
}
