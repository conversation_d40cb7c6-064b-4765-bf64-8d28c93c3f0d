package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.UserCustomGiftRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserCustomGiftRecord;
import com.red.circle.other.infra.database.rds.service.user.user.UserCustomGiftRecordService;
import com.red.circle.other.inner.model.cmd.user.UserCustomGiftQryCmd;
import com.red.circle.tool.core.date.LocalDateUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户领取专属礼物记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2024-01-08 18:11
 */
@Service
@AllArgsConstructor
public class UserCustomGiftRecordServiceImpl extends
    BaseServiceImpl<UserCustomGiftRecordDAO, UserCustomGiftRecord> implements
    UserCustomGiftRecordService {


  @Override
  public PageResult<UserCustomGiftRecord> getCustomGift(UserCustomGiftQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), UserCustomGiftRecord::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getApproveStatus()), UserCustomGiftRecord::getApproveStatus,
            query.getApproveStatus())
        .eq(Objects.nonNull(query.getUserId()), UserCustomGiftRecord::getUserId,
            query.getUserId())
        .eq(UserCustomGiftRecord::getUploadResource, Boolean.TRUE)
        .eq(StringUtils.isNotBlank(query.getRegion()), UserCustomGiftRecord::getRegions,
            query.getRegion())
        .eq(StringUtils.isNotBlank(query.getCustomType()), UserCustomGiftRecord::getCustomType,
            query.getCustomType())
        .page(query.getPageQuery());

  }

  @Override
  public Map<Integer, UserCustomGiftRecord> mapByAmount(Long userId) {
    return Optional.ofNullable(
            query().eq(UserCustomGiftRecord::getUserId, userId)
                .eq(UserCustomGiftRecord::getRechargeDate,
                    LocalDateUtils.nowThisMonthToInteger()).list())
        .map(customGiftRecords -> customGiftRecords.stream()
            .collect(Collectors.toMap(UserCustomGiftRecord::getAmount,
                Function.identity(), (v0, v1) -> v1))).orElse(Maps.newHashMap());
  }


}
