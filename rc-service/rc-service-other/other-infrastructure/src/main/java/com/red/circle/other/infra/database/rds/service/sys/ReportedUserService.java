package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.ReportedUser;
import com.red.circle.other.inner.model.cmd.approval.ApprovalReportedCmd;
import com.red.circle.other.inner.model.cmd.sys.SysReportedQryCmd;

/**
 * <p>
 * 被举报用户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-31
 */
public interface ReportedUserService extends BaseService<ReportedUser> {

  PageResult<ReportedUser> pageSysReportedUser(SysReportedQryCmd sysReportedQuery);

  void approvalReported(ApprovalReportedCmd cmd);
}
