package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxFortuneValueConfig;

/**
 * <p>
 * 幸运值概率配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxFortuneValueConfigService extends
    BaseService<GameLuckyBoxFortuneValueConfig> {

  GameLuckyBoxFortuneValueConfig getBySysOrigin(String sysOriginName);

  GameLuckyBoxFortuneValueConfig getLuckyBoxFortuneValueConfig(String sysOrigin);
}
