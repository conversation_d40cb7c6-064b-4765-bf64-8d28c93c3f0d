package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.RunningWaterUserProps;
import com.red.circle.other.inner.model.cmd.material.RunningWaterUserPropsQryCmd;

/**
 * <p>
 * 道具购买流水 服务类.
 * </p>
 *
 * <AUTHOR> on 2021-01-22
 */
public interface RunningWaterUserPropsService extends BaseService<RunningWaterUserProps> {

  PageResult<RunningWaterUserProps> pageRunningWaterUserProps(RunningWaterUserPropsQryCmd query);
}
