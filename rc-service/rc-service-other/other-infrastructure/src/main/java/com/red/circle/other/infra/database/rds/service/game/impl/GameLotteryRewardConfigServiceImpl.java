package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLotteryRewardConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLotteryRewardConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLotteryRewardConfigService;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖游戏奖励配置 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Service
public class GameLotteryRewardConfigServiceImpl extends
    BaseServiceImpl<GameLotteryRewardConfigDAO, GameLotteryRewardConfig> implements
    GameLotteryRewardConfigService {


  @Override
  public List<GameLotteryRewardConfig> listGamePrize(Long groupId) {
    return query()
        .eq(GameLotteryRewardConfig::getGroupId, groupId)
        .orderByAsc(GameLotteryRewardConfig::getSort)
        .list();
  }
}
