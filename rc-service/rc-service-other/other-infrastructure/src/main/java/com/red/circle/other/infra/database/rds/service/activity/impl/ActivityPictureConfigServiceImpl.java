package com.red.circle.other.infra.database.rds.service.activity.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.ActivityPictureConfigDAO;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityPictureConfig;
import com.red.circle.other.infra.database.rds.service.activity.ActivityPictureConfigService;
import com.red.circle.other.inner.model.cmd.activity.ActivityPictureQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动图片配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-20 16:37
 */
@Service
public class ActivityPictureConfigServiceImpl extends
    BaseServiceImpl<ActivityPictureConfigDAO, ActivityPictureConfig> implements
    ActivityPictureConfigService {

  @Override
  public PageResult<ActivityPictureConfig> getActivityPicture(ActivityPictureQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), ActivityPictureConfig::getSysOrigin,
            query.getSysOrigin())
        .page(query.getPageQuery());
  }

  @Override
  public void removeActivityPicture(Long id) {
    delete().eq(ActivityPictureConfig::getId, id).execute();
  }
}
