package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.UserThumbsUp;

/**
 * <p>
 * 用户点赞列表 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-04-06 11:41
 */
public interface UserThumbsUpService extends BaseService<UserThumbsUp> {

  boolean checkExist(Long cmdOriginUserId, Long thumbsUpId);

  boolean notExistsAdd(Long cmdOriginUserId, Long thumbsUpId);

  long countThumbsUp(Long thumbsUpId);
}
