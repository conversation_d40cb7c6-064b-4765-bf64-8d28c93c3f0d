package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxUserAward;

/**
 * <p>
 * 用户抽奖次数 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxUserAwardService extends BaseService<GameLuckyBoxUserAward> {

  GameLuckyBoxUserAward getByUserId(Long userId);

  void updateByUserId(GameLuckyBoxUserAward build);
}
