package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户登陆日志
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_login_logger")
public class LoginLogger implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户登陆日志
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 设备号
   */
  @TableField("device_id")
  private String deviceId;

  /**
   * 用户id
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 登陆IP
   */
  @TableField("ip")
  private String ip;

  /**
   * 登陆方式.
   */
  @TableField("login_type")
  private String loginType;

  /**
   * 版本号
   */
  @TableField("app_version")
  private String appVersion;

  /**
   * 平台 IOS，Android
   */
  @TableField("app_platform")
  private String appPlatform;

  /**
   * 操作系统版本
   */
  @TableField("sys_version")
  private String sysVersion;

  /**
   * 手机型号
   */
  @TableField("sys_model")
  private String sysModel;

  /**
   * 登陆时间
   */
  @TableField("create_time")
  private Timestamp createTime;


}
