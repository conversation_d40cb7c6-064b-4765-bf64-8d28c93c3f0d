package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.RelationshipFriend;
import com.red.circle.other.inner.model.dto.user.PetRelationshipFriendDTO;
import java.util.List;

/**
 * <p>
 * 用户好友关系 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
public interface RelationshipFriendService extends BaseService<RelationshipFriend> {

  /**
   * 获取宠物余额最高的top.
   *
   * @param userId  用户id
   * @param topSize 数量
   * @return ignore
   */
  List<PetRelationshipFriendDTO> listPetBeanTop(Long userId, Integer topSize);

  /**
   * 获取用户好友信息.
   *
   * @param userId 用户id
   * @param lastId 最后一条记录id
   * @return ignore
   */
  List<RelationshipFriend> flowSize1000(Long userId, Long lastId,Long friendIds);

  Long todayFlowSize(Long userId);

  /**
   * 检测是否是好友关系.
   *
   * @param userId       用户id
   * @param friendUserId 好友用户id
   * @return true 是好友，false 不是好友
   */
  Boolean checkFriend(Long userId, Long friendUserId);

  /**
   * 移除好友.
   *
   * @param userId       用户id
   * @param friendUserId 好友用户id
   */
  void removeFriend(Long userId, Long friendUserId);

  /**
   * 获取用户朋友记录id.
   *
   * @param userId       用户id
   * @param friendUserId 朋友id
   * @return 记录id
   */
  Long getIdByFriendUserId(Long userId, Long friendUserId);

}
