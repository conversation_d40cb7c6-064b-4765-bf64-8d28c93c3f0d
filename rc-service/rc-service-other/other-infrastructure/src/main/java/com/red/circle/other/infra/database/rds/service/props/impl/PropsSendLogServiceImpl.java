package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsSendLogDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsSendLog;
import com.red.circle.other.infra.database.rds.service.props.PropsSendLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 道具发送日志 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-02
 */
@Service
public class PropsSendLogServiceImpl extends
    BaseServiceImpl<PropsSendLogDAO, PropsSendLog> implements PropsSendLogService {

}
