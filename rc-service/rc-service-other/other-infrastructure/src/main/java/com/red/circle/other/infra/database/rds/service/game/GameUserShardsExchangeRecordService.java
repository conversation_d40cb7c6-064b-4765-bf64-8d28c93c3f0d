package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameUserShardsExchangeRecord;
import com.red.circle.other.inner.model.cmd.game.GameEggQryCmd;

/**
 * <p>
 * 用户游戏抽奖碎片兑换记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface GameUserShardsExchangeRecordService extends
    BaseService<GameUserShardsExchangeRecord> {


  PageResult<GameUserShardsExchangeRecord> pageRecord(GameEggQryCmd query);
}
