package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.FriendshipCard;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 用户友谊关系卡 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
public interface FriendshipCardService extends BaseService<FriendshipCard> {

  /**
   * 根据类型与用户id查询出该用户送出的卡片中最强的那个用户
   *
   * @param type   卡片
   * @param userId 发送人
   * @return 最强接收人
   */
  FriendshipCard getMaxUserByTypeByUserId(String type, Long userId);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   */
  List<FriendshipCard> pageByCondition(Integer pageNumber, String type, Long userId);

  /**
   * 是否存在卡片关系
   *
   * @param type         卡片类型
   * @param sendUserId   发送人
   * @param acceptUserId 接受人
   * @return true 存在。 false 不存在。
   */
  Boolean exist(String type, Long sendUserId, Long acceptUserId);

  /**
   * 根据条件获得卡片
   *
   * @param type         卡片类型
   * @param sendUserId   用户
   * @param acceptUserId 用户
   * @return 卡片
   */
  FriendshipCard getByCondition(String type, Long sendUserId, Long acceptUserId);

  /**
   * 删除卡片关系
   */
  Boolean deleteByCondition(String type, Long sendUserId, Long acceptUserId);

  /**
   * 根据条件获得卡片
   *
   * @param sendUserId   用户
   * @param acceptUserId 用户
   * @return 卡片
   */
  List<FriendshipCard> listByCondition(Long sendUserId, Long acceptUserId);

  /**
   * 根据条件获得卡片
   *
   * @param sendUserId    用户
   * @param acceptUserIds 用户
   * @return 卡片
   */
  List<FriendshipCard> listByCondition(Long sendUserId, Collection<Long> acceptUserIds);

}
