package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.GiveFriendshipCardApply;
import java.util.List;

/**
 * <p>
 * 用户赠送友谊关系卡记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
public interface GiveFriendshipCardApplyService extends BaseService<GiveFriendshipCardApply> {

  Boolean exist(String type, Long sendUserId, Long acceptUserId);

  Boolean changeStatusById(Long id, String status);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   */
  List<GiveFriendshipCardApply> pageByCondition(Integer pageNumber, String type, Long userId);

  /**
   * 统计未同意友谊关系卡申请
   *
   * @param userId 用户id
   */
  Long countFriendshipCardApply(Long userId);

}
