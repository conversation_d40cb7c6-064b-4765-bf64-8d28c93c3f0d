package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyGiftStandardConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckGiftStandardConfigQryCmd;
import java.util.List;

/**
 * <p>
 * 礼物规格配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-06-16 17:14
 */
public interface GameLuckyGiftStandardConfigService extends
    BaseService<GameLuckyGiftStandardConfig> {

  List<GameLuckyGiftStandardConfig> getLuckyGiftStandard(String sysOrigin);

  PageResult<GameLuckyGiftStandardConfig> getStandardConfig(GameLuckGiftStandardConfigQryCmd query);

  void deleteStandardConfig(Long id, Long userId);
}
