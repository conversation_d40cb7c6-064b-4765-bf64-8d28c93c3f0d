package com.red.circle.other.infra.database.rds.service.sys.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.WeekStarGiftDAO;
import com.red.circle.other.infra.database.rds.entity.sys.WeekStarGift;
import com.red.circle.other.infra.database.rds.service.sys.WeekStarGiftService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 周星礼物 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Service
public class WeekStarGiftServiceImpl extends
    BaseServiceImpl<WeekStarGiftDAO, WeekStarGift> implements WeekStarGiftService {

  @Override
  public List<Long> listGiftIdsByGroupId(Long groupId) {

    if (Objects.isNull(groupId)) {
      return CollectionUtils.newArrayList();
    }

    return Optional.ofNullable(
            query()
                .select(WeekStarGift::getGiftId)
                .eq(WeekStarGift::getGroupId, groupId)
                .list()
        ).map(weekStarGifts -> weekStarGifts.stream().map(WeekStarGift::getGiftId).toList())
        .orElse(null);
  }

  @Override
  public List<WeekStarGift> listByGroupId(Long groupId) {
    return query().eq(WeekStarGift::getGroupId, groupId).list();
  }

  @Override
  public Map<Long, List<WeekStarGift>> mapGroupByGroupId(List<Long> groupIds) {
    return Optional.of(query().in(WeekStarGift::getGroupId, groupIds).list())
        .map(weekStarGifts -> weekStarGifts.stream()
            .collect(Collectors.groupingBy(WeekStarGift::getGroupId)))
        .orElse(CollectionUtils.newHashMap());
  }

  @Override
  public void deleteByGroupId(Long id) {
    delete().eq(WeekStarGift::getGiftId, id).execute();
  }
}
