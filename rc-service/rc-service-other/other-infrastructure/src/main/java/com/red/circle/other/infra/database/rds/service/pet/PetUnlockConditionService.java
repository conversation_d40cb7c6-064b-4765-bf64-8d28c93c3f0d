package com.red.circle.other.infra.database.rds.service.pet;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.pet.PetUnlockCondition;
import java.util.List;

/**
 * <p>
 * 解锁条件 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface PetUnlockConditionService extends BaseService<PetUnlockCondition> {


  /**
   * 获取解锁条件.
   *
   * @param petId 宠物id
   * @return list
   */
  List<PetUnlockCondition> listByPetId(Long petId);
}
