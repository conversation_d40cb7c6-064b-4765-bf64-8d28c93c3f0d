package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkIntegralRecord;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 房间PK用户贡献记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
public interface GameRoomPkIntegralRecordService extends BaseService<GameRoomPkIntegralRecord> {

  /**
   * 根据pk id获得总积分
   */
  Long getTotalIntegralByPkId(Long pkId);

  /**
   * 根据pk id获得对战人员总积分
   */
  List<GameRoomPkIntegralRecord> getTotalIntegralAndUserIdByPkId(Long pkId);

  /**
   * 根据pkId查询粉丝贡献数据并保存值粉丝贡献表
   */
  void saveIntegralToFansByPkId(@Param("pkId") Long pkId);

}
