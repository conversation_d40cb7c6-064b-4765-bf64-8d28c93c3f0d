package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxBountyDetailsConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxBountyDetailsConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxBountyDetailsConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxBountyDetailsConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 赏金任务配置详情 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxBountyDetailsConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxBountyDetailsConfigDAO, GameLuckyBoxBountyDetailsConfig> implements
    GameLuckyBoxBountyDetailsConfigService {

  @Override
  public List<GameLuckyBoxBountyDetailsConfig> getBoxBountyDetailsConfig(String sysOriginName) {
    return query().eq(GameLuckyBoxBountyDetailsConfig::getSysOrigin, sysOriginName).list();
  }

  @Override
  public void deleteByBountyId(Long bountyId) {
    delete().eq(GameLuckyBoxBountyDetailsConfig::getBountyId, bountyId).execute();
  }

  @Override
  public List<GameLuckyBoxBountyDetailsConfig> getGameLuckyBoxBountyDetailsConfig(
      GameLuckyBoxBountyDetailsConfigQryCmd query) {
    return query().eq(
            GameLuckyBoxBountyDetailsConfig::getBountyId, query.getBountyId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()),
            GameLuckyBoxBountyDetailsConfig::getSysOrigin, query.getSysOrigin()).list();
  }
}
