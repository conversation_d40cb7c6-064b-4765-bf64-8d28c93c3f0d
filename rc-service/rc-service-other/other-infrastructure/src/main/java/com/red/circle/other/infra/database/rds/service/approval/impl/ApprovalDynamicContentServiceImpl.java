package com.red.circle.other.infra.database.rds.service.approval.impl;


import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.approval.ApprovalDynamicContentDAO;
import com.red.circle.other.infra.database.rds.entity.approval.ApprovalDynamicContent;
import com.red.circle.other.infra.database.rds.service.approval.ApprovalDynamicContentService;
import com.red.circle.other.inner.model.cmd.approval.ApprovalDynamicQryCmd;
import com.red.circle.tool.core.date.LocalDateTimeUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审核动态内容 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
@Service
public class ApprovalDynamicContentServiceImpl extends
    BaseServiceImpl<ApprovalDynamicContentDAO, ApprovalDynamicContent> implements
    ApprovalDynamicContentService {

  @Override
  public PageResult<ApprovalDynamicContent> pageApprovalDynamic(ApprovalDynamicQryCmd query) {
    if (query.getStartTime() != null && query.getEndTime() != null){
      query.setStartCreateDate(LocalDateTimeUtils.convert(query.getStartTime()));
      query.setEndCreateDate(LocalDateTimeUtils.convert(query.getEndTime()));
    }
    return query()
        .eq(Objects.nonNull(query.getUserId()), ApprovalDynamicContent::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), ApprovalDynamicContent::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getApproveStatus()),
            ApprovalDynamicContent::getApproveStatus, query.getApproveStatus())
        .gt(Objects.nonNull(query.getStartCreateDate()), ApprovalDynamicContent::getCreateTime,
            query.getStartCreateDate())
        .lt(Objects.nonNull(query.getEndCreateDate()), ApprovalDynamicContent::getCreateTime,
            query.getEndCreateDate())
        .orderByDesc(ApprovalDynamicContent::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public void approvalPass(Set<Long> ids) {
    update()
        .set(ApprovalDynamicContent::getApproveStatus, ApprovalStatusEnum.PASS.name())
        .set(ApprovalDynamicContent::getUpdateTime, TimestampUtils.now())
        .in(ApprovalDynamicContent::getDynamicId, ids)
        .execute();
  }

  @Override
  public void approvalNotPass(Set<Long> ids) {
    update()
        .set(ApprovalDynamicContent::getApproveStatus, ApprovalStatusEnum.NOT_PASS.name())
        .set(ApprovalDynamicContent::getUpdateTime, TimestampUtils.now())
        .in(ApprovalDynamicContent::getDynamicId, ids)
        .execute();
  }

  @Override
  public void deleteByDynamicId(Long dynamicId) {
    if (Objects.isNull(dynamicId)) {
      return;
    }
    update().set(ApprovalDynamicContent::getApproveStatus, ApprovalStatusEnum.NOT_PASS.name())
        .eq(ApprovalDynamicContent::getDynamicId, dynamicId)
        .last(PageConstant.LIMIT_ONE).execute();
  }
}
