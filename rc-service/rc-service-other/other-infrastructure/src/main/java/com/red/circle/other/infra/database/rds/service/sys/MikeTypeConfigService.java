package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.MikeTypeConfig;
import com.red.circle.other.inner.model.cmd.sys.SysMikeTypeConfigQryCmd;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 麦位类型配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-04-19 15:24
 */
public interface MikeTypeConfigService extends BaseService<MikeTypeConfig> {

  /**
   * 获取麦位类型配置
   */
  List<MikeTypeConfig> getMikeTypeList(String sysOrigin);

  /**
   * 根据麦位类型获取麦位信息
   */
  BigDecimal getMikeTypeAmount(String mikeType, String duration, SysOriginPlatformEnum sysOrigin);

  Map<String, String> getMikeTypeByType(String sysOrigin);

  PageResult<MikeTypeConfig> getMikeType(SysMikeTypeConfigQryCmd query);

  void removeMikeType(Long id);

  Long countByMikeType(String mikeType, String sysOrigin);

  Long countByMikeTypeAndId(Long id, String mikeType, String sysOrigin);

  Boolean updateInfo(MikeTypeConfig toSysMikeTypeConfig);
}
