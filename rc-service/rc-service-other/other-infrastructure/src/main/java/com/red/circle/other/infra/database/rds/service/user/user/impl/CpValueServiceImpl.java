package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.CpValueDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.CpValue;
import com.red.circle.other.infra.database.rds.service.user.user.CpValueService;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * cp值 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-16
 */
@Service
public class CpValueServiceImpl extends BaseServiceImpl<CpValueDAO, CpValue> implements
    CpValueService {

  @Override
  public void incrValue(Long id, BigDecimal val) {
    update()
        .setSql("cp_val=cp_val+" + val)
        .eq(CpValue::getId, id)
        .execute();
  }

  @Override
  public void incrBlessValue(Long id, BigDecimal val) {
    update()
        .setSql("bless_val=bless_val+" + val)
        .eq(CpValue::getId, id)
        .execute();
  }

  @Override
  public BigDecimal getCpVal(Long id) {
    return Objects.isNull(id)
        ? BigDecimal.ZERO
        : Optional.ofNullable(query()
                .select(CpValue::getCpVal)
                .eq(CpValue::getId, id)
                .getOne())
            .map(CpValue::getCpVal)
            .orElse(BigDecimal.ZERO);
  }

  @Override
  public void deleteById(Long id) {
    delete()
        .eq(CpValue::getId, id)
        .execute();
  }

}
