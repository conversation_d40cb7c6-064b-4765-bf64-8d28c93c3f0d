package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysPushRecordErrorLogDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysPushRecordErrorLog;
import com.red.circle.other.infra.database.rds.service.sys.SysPushRecordErrorLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 推送失败记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-09
 */
@Service
public class SysPushRecordErrorLogServiceImpl extends
    BaseServiceImpl<SysPushRecordErrorLogDAO, SysPushRecordErrorLog> implements
    SysPushRecordErrorLogService {

}
