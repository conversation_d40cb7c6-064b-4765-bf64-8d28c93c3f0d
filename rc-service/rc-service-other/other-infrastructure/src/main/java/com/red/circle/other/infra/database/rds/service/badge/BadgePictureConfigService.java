package com.red.circle.other.infra.database.rds.service.badge;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.badge.BadgePictureConfig;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 徽章图片配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
public interface BadgePictureConfigService extends BaseService<BadgePictureConfig> {

  List<BadgePictureConfig> listByIds(Collection<Long> badgeIds, String sysOrigin);

  Map<Long, BadgePictureConfig> mapBadge(String sysOrigin, Set<Long> badgeIds);

  BadgePictureConfig getByBadgeId(String sysOrigin, Long badgeId);

  List<BadgePictureConfig> listBySysOrigin(String sysOrigin);

  Map<Long, List<BadgePictureConfig>> mapGroupSysBadgePicture(Set<Long> collect);

  /**
   * 获得徽章图片.
   */
  BadgePictureConfig getByConfigIdBySysOrigin(Long configId, String sysOrigin);

}
