package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLotteryRewardGroupDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLotteryRewardGroup;
import com.red.circle.other.infra.database.rds.service.game.GameLotteryRewardGroupService;
import com.red.circle.other.infra.enums.game.LotteryGameTypeEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖游戏奖励配置分组 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Service
public class GameLotteryRewardGroupServiceImpl extends
    BaseServiceImpl<GameLotteryRewardGroupDAO, GameLotteryRewardGroup> implements
    GameLotteryRewardGroupService {

  @Override
  public GameLotteryRewardGroup getGameGroupByGameType(SysOriginPlatformEnum sysOrigin,
      LotteryGameTypeEnum gameType) {
    return query()
        .eq(GameLotteryRewardGroup::getGameType, gameType)
        .eq(GameLotteryRewardGroup::getSysOrigin, sysOrigin)
        .eq(GameLotteryRewardGroup::getShelfStatus, Boolean.TRUE)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }
}
