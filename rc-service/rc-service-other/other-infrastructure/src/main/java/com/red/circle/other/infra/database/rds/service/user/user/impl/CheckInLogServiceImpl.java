package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.CheckInLogDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.CheckInLog;
import com.red.circle.other.infra.database.rds.service.user.user.CheckInLogService;
import com.red.circle.other.inner.model.cmd.user.UserCheckInLogQryCmd;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户签到日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-08
 */
@Service
public class CheckInLogServiceImpl extends BaseServiceImpl<CheckInLogDAO, CheckInLog> implements
    CheckInLogService {

  @Override
  public PageResult<CheckInLog> getCheckInLog(UserCheckInLogQryCmd query) {
    return query().eq(Objects.nonNull(query.getUserId()), CheckInLog::getUserId, query.getUserId())
        .ge(Objects.nonNull(query.getStartTime()), CheckInLog::getCreateTime,
            query.startTimeToLocalDateTime())
        .le(Objects.nonNull(query.getEndTime()), CheckInLog::getCreateTime,
            query.endTimeToLocalDateTime())
        .page(query.getPageQuery());
  }
}
