package com.red.circle.other.infra.enums.user.user;

/**
 * <AUTHOR> on 2020/8/17
 */
public enum VipTypeEnum {
  /**
   * 不是vip
   */
  NONE(0),

  /**
   * 废材VIP1.99
   */
  WASTE_MATERIAL_VIP(10),

  /**
   * 废铁VIP3.99
   */
  SCRAP_IRON_VIP(10),

  /**
   * 青铁VIP9.99
   */
  GREEN_IRON_VIP(20),

  /**
   * 3天免费19.99
   */
  THREE_DAYS_FREE(30),

  /**
   * 青铜19.99
   */
  VIP(30),

  /**
   * 白银59.99
   */
  SILVER_VIP(40),

  /**
   * 黄金89.99
   */
  GOLD_VIP(50);

  private final Integer typeValue;

  VipTypeEnum(Integer typeValue) {
    this.typeValue = typeValue;
  }

  public Integer getTypeValue() {
    return typeValue;
  }
}
