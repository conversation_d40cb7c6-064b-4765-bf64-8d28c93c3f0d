package com.red.circle.other.infra.database.rds.service.game.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameUserLotteryRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameUserLotteryRecord;
import com.red.circle.other.infra.database.rds.service.game.GameUserLotteryRecordService;
import com.red.circle.other.inner.model.cmd.game.GameEggQryCmd;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户游戏抽奖记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Service
@RequiredArgsConstructor
public class GameUserLotteryRecordServiceImpl extends
    BaseServiceImpl<GameUserLotteryRecordDAO, GameUserLotteryRecord> implements
    GameUserLotteryRecordService {

  private final GameUserLotteryRecordDAO gameUserLotteryRecordDAO;

  @Override
  public List<GameUserLotteryRecord> listByUserId(Long userId, Boolean isSingle, String gameType) {
    return gameUserLotteryRecordDAO.listByUserId(userId, isSingle, gameType);
  }

  @Override
  public List<GameUserLotteryRecord> listTop20(String sysOrigin, String gameType) {
    return gameUserLotteryRecordDAO.listTop20(sysOrigin, gameType);
  }


  @Override
  public PageResult<GameUserLotteryRecord> pageRecord(GameEggQryCmd query) {

    return query()
        .eq(Objects.nonNull(query.getUserId()),
            GameUserLotteryRecord::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotEmpty(query.getSysOrigin()),
            GameUserLotteryRecord::getSysOrigin,
            query.getSysOrigin())
        .orderByDesc(GameUserLotteryRecord::getCreateTime)
        .page(query.getPageQuery());
  }
}
