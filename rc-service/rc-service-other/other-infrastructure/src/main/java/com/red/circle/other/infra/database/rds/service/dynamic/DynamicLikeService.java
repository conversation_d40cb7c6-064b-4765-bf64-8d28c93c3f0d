package com.red.circle.other.infra.database.rds.service.dynamic;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicLike;
import java.util.Collection;
import java.util.Map;

/**
 * <p>
 * 动态-朋友圈点赞 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface DynamicLikeService extends BaseService<DynamicLike> {

  /**
   * 根据动态ID与用户ID获得点赞情况
   *
   * @param contentIds 动态内容ID
   * @param userId     用户id
   * @return true 已点赞
   */
  Map<Long, Boolean> mapByContentIdsByUserId(Collection<Long> contentIds, Long userId);

  /**
   * 是否存在数据
   *
   * @param contentId  内容ID
   * @param createUser 创建人
   * @return true存在
   */
  Boolean exist(Long contentId, Long createUser);


  /**
   * 删除点赞记录
   *
   * @param contentId  内容ID
   * @param createUser 创建人
   */
  void delete(Long contentId, Long createUser);

  /**
   * 根据动态ID获得动态点赞数量
   *
   * @param contentIds 动态内容ID
   */
  Map<Long, Long> mapLikeQuantity(Collection<Long> contentIds);
}
