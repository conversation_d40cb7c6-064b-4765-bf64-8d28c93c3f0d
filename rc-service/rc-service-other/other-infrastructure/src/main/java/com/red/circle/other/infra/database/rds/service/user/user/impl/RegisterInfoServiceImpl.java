package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.RegisterInfoDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.RegisterInfo;
import com.red.circle.other.infra.database.rds.service.user.user.RegisterInfoService;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户基本信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@Service
public class RegisterInfoServiceImpl extends
    BaseServiceImpl<RegisterInfoDAO, RegisterInfo> implements RegisterInfoService {

  @Override
  public Map<Long, RegisterInfo> mapRegisterInfo(Set<Long> userIds) {
    List<RegisterInfo> registerInfos = query().in(RegisterInfo::getUserId, userIds).list();

    if (CollectionUtils.isEmpty(registerInfos)) {
      return CollectionUtils.newHashMap();
    }
    return registerInfos.stream()
        .collect(Collectors.toMap(RegisterInfo::getUserId, registerInfo -> registerInfo));
  }


  @Override
  public RegisterInfo getRegisterInfo(Long userId) {
    return getById(userId);
  }
}
