package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameUserShardsBackpackDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameUserShardsBackpack;
import com.red.circle.other.infra.database.rds.service.game.GameUserShardsBackpackService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户游戏抽奖碎片背包 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Service
public class GameUserShardsBackpackServiceImpl extends
    BaseServiceImpl<GameUserShardsBackpackDAO, GameUserShardsBackpack> implements
    GameUserShardsBackpackService {

  @Override
  public GameUserShardsBackpack getOneByConfigIdByUserId(Long rewardConfigId, Long userId) {

    return query()
        .eq(GameUserShardsBackpack::getUserId, userId)
        .eq(GameUserShardsBackpack::getRewardConfigId, rewardConfigId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public void saveOrUpdateByConfigIdByUserId(Long rewardConfigId, Long userId, Integer quantity) {

    GameUserShardsBackpack userShardsBackpack = query()
        .eq(GameUserShardsBackpack::getUserId, userId)
        .eq(GameUserShardsBackpack::getRewardConfigId, rewardConfigId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(userShardsBackpack)) {
      save(new GameUserShardsBackpack()
          .setUserId(userId)
          .setRewardConfigId(rewardConfigId)
          .setQuantity(quantity)
      );
      return;
    }

    update()
        .set(GameUserShardsBackpack::getQuantity, userShardsBackpack.getQuantity() + quantity)
        .eq(GameUserShardsBackpack::getId, userShardsBackpack.getId())
        .execute();
  }

  @Override
  public boolean reduceShardsQuantity(Long id, Long quantity) {
    String expression = "quantity-" + quantity;
    return update()
        .setSql(String.format("quantity=IF(%s>0,%s,%s)", expression, expression, "0"))
        .eq(GameUserShardsBackpack::getId, id)
        .execute();
  }

  @Override
  public Map<Long, GameUserShardsBackpack> mapByUserId(Long userId, Set<Long> configIds) {
    if (Objects.isNull(userId) || CollectionUtils.isEmpty(configIds)) {
      return Maps.newHashMap();
    }

    return Optional.ofNullable(query()
            .eq(GameUserShardsBackpack::getUserId, userId)
            .in(GameUserShardsBackpack::getRewardConfigId, configIds)
            .list())
        .map(shards -> shards.stream()
            .collect(Collectors.toMap(GameUserShardsBackpack::getRewardConfigId, v -> v)))
        .orElse(Maps.newHashMap());
  }

  @Override
  public Integer getSumShardsQuantityByUserId(Long userId) {

    return Optional.ofNullable(query().eq(GameUserShardsBackpack::getUserId, userId).list())
        .map(shards -> shards.stream().mapToInt(GameUserShardsBackpack::getQuantity).sum())
        .orElse(0);
  }
}
