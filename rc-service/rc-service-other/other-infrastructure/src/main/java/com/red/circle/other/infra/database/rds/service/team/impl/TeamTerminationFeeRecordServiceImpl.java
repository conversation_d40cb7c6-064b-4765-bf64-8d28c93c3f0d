package com.red.circle.other.infra.database.rds.service.team.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.team.TeamTerminationFeeRecordDAO;
import com.red.circle.other.infra.database.rds.entity.team.TeamTerminationFeeRecord;
import com.red.circle.other.infra.database.rds.service.team.TeamTerminationFeeRecordService;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 团队主播解约费记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-09-20 15:10
 */
@Service
public class TeamTerminationFeeRecordServiceImpl extends
    BaseServiceImpl<TeamTerminationFeeRecordDAO, TeamTerminationFeeRecord> implements
    TeamTerminationFeeRecordService {


  @Override
  public List<TeamTerminationFeeRecord> listByTimeGt24AndPayeeUserNull() {

    return Optional.ofNullable(query()
            .lt(TeamTerminationFeeRecord::getCreateTime, TimestampUtils.nowMinusDays(1))
            .isNull(TeamTerminationFeeRecord::getPayeeUserId)
            .last(PageConstant.formatLimit(5000))
            .list())
        .orElse(Lists.newArrayList());
  }


}
