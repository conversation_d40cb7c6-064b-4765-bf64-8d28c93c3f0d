package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameTurntableParticipateUser;
import java.util.List;

/**
 * <p>
 * 游戏转盘参与用户 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-20
 */
public interface GameTurntableParticipateUserService extends
    BaseService<GameTurntableParticipateUser> {

  /**
   * 获取游戏参与者.
   *
   * @param gameId 游戏id
   * @return ignore
   */
  List<GameTurntableParticipateUser> listParticipateUser(Long gameId);

  /**
   * 游戏top.
   *
   * @param sysOrigin 系统
   * @return ignore
   */
  List<GameTurntableParticipateUser> listTop20BySysOrigin(String sysOrigin);


}
