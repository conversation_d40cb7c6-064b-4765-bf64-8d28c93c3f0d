package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 邀请用户充值佣金与数量累计.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_user_summary")
public class InviteUserSummary extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 标识.
   */
  @TableId(value = "id", type = IdType.INPUT)
  private Long id;

  /**
   * 来源平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 获得糖果(佣金)总数量.
   */
  @TableField("commission")
  private BigDecimal commission;

  /**
   * 邀请总人数.
   */
  @TableField("quantity")
  private Long quantity;


}
