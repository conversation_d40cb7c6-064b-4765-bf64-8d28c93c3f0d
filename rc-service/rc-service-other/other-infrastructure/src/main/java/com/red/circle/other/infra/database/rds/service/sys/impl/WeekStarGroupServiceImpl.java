package com.red.circle.other.infra.database.rds.service.sys.impl;


import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.WeekStarGroupDAO;
import com.red.circle.other.infra.database.rds.entity.sys.WeekStarGroup;
import com.red.circle.other.infra.database.rds.service.sys.WeekStarGroupService;
import com.red.circle.other.inner.enums.activity.WeekStarGiftTypeEnum;
import com.red.circle.other.inner.model.cmd.sys.SysWeekStarGroupQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 当前周星礼物 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Service
public class WeekStarGroupServiceImpl extends
    BaseServiceImpl<WeekStarGroupDAO, WeekStarGroup> implements WeekStarGroupService {


  @Override
  public void updateTypeById(Long id, WeekStarGiftTypeEnum type) {
    update()
        .set(WeekStarGroup::getType, type)
        .eq(WeekStarGroup::getId, id)
        .execute();
  }

  @Override
  public Optional<WeekStarGroup> getByTypeOne(SysOriginPlatformEnum sysOrigin,
      WeekStarGiftTypeEnum type) {
    return Optional.ofNullable(query()
        .eq(WeekStarGroup::getSysOrigin, sysOrigin)
        .eq(WeekStarGroup::getType, type)
        .last(PageConstant.formatLimit(1))
        .getOne());
  }

  @Override
  public Optional<WeekStarGroup> getStatusNoneMinDisplayNumber(SysOriginPlatformEnum sysOrigin) {
    return Optional.ofNullable(query()
        .eq(WeekStarGroup::getSysOrigin, sysOrigin)
        .eq(WeekStarGroup::getType, WeekStarGiftTypeEnum.NONE)
        .orderByAsc(WeekStarGroup::getDisplayNumber)
        .last(PageConstant.formatLimit(1))
        .getOne());
  }

  @Override
  public void updateLastWeekToNone(SysOriginPlatformEnum sysOrigin) {
    update()
        .set(WeekStarGroup::getType, WeekStarGiftTypeEnum.NONE)
        .eq(WeekStarGroup::getType, WeekStarGiftTypeEnum.LAST_WEEK)
        .eq(WeekStarGroup::getSysOrigin, sysOrigin)
        .execute();
  }

  @Override
  public void updateLastWeekToNone(SysOriginPlatformEnum sysOrigin,
      List<WeekStarGiftTypeEnum> types) {
    update()
        .set(WeekStarGroup::getType, WeekStarGiftTypeEnum.NONE)
        .in(WeekStarGroup::getType, types)
        .eq(WeekStarGroup::getSysOrigin, sysOrigin)
        .execute();
  }

  @Override
  public PageResult<WeekStarGroup> getPage(SysWeekStarGroupQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), WeekStarGroup::getSysOrigin,
            query.getSysOrigin())
        .orderByDesc(WeekStarGroup::getId)
        .page(query.getPageQuery());
  }

  @Override
  public void deleteById(Long id) {
    delete().eq(WeekStarGroup::getId, id).execute();
  }

}
