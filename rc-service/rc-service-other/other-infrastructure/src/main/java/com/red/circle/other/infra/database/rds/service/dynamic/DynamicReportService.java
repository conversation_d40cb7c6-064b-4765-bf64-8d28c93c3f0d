package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicReport;
import com.red.circle.other.inner.model.cmd.approval.ApprovalDynamicReportCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalDynamicReportQryCmd;
import java.util.List;

/**
 * <p>
 * 投诉动态 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-18
 */
public interface DynamicReportService extends BaseService<DynamicReport> {

  Boolean exist(Long dynamicContentId, Long userId);

  PageResult<DynamicReport> pageData(ApprovalDynamicReportQryCmd query);

  List<DynamicReport> getByContentId(Long contentId);

  void updateDynamicReport(ApprovalDynamicReportCmd cmd, List<DynamicReport> complaints);
}
