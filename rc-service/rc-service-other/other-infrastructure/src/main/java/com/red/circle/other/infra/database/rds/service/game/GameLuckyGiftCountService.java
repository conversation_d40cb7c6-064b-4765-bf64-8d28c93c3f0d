package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyGiftCount;
import com.red.circle.other.inner.model.cmd.game.LuckGiftGameCountQryCmd;
import com.red.circle.other.inner.model.cmd.game.LuckGiftGameQryCmd;
import com.red.circle.other.inner.model.dto.game.GameLuckyGiftCountDTO;
import java.util.List;

/**
 * 幸运礼物送礼明细记录.
 *
 * <AUTHOR> on 2023/6/19
 */
public interface GameLuckyGiftCountService extends BaseService<GameLuckyGiftCount> {

  /**
   * 获得最新前30名且倍数大于等于100倍.
   */
  List<GameLuckyGiftCount> lastTop30AndGetHundredfold();

  /**
   * 获得用户中奖数据.
   */
  List<GameLuckyGiftCount> pageByUserIdAndLastId(Long userId, Long lastId);

  PageResult<GameLuckyGiftCount> pageByCondition(LuckGiftGameQryCmd query);

  GameLuckyGiftCountDTO countLuckyGiftGame(LuckGiftGameCountQryCmd query);
}
