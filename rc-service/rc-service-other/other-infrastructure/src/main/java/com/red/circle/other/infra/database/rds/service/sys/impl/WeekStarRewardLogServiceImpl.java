package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.WeekStarRewardLogDAO;
import com.red.circle.other.infra.database.rds.entity.sys.WeekStarRewardLog;
import com.red.circle.other.infra.database.rds.service.sys.WeekStarRewardLogService;
import com.red.circle.tool.core.date.LocalDateUtils;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 周星奖励日志 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
@Service
public class WeekStarRewardLogServiceImpl extends
    BaseServiceImpl<WeekStarRewardLogDAO, WeekStarRewardLog> implements WeekStarRewardLogService {

  @Override
  public boolean existsThisWeek(Long userId) {
    return Optional.ofNullable(
            query()
                .select(WeekStarRewardLog::getId)
                .eq(WeekStarRewardLog::getUserId, userId)
                .eq(WeekStarRewardLog::getReceiveDateNum, LocalDateUtils.nowWeekMondayToInteger())
                .last(PageConstant.LIMIT_ONE)
                .getOne())
        .map(weekStarRewardLog -> Objects.nonNull(weekStarRewardLog.getId()))
        .orElse(Boolean.FALSE);
  }
}
