package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.RecentlyActive;
import java.sql.Timestamp;

/**
 * <p>
 * 用户最近活跃时间.
 * </p>
 *
 * <AUTHOR> on 2023-12-29 18:10
 */
public interface RecentlyActiveService extends BaseService<RecentlyActive> {

  void add(String sysOrigin, Long userId);

  Timestamp getLastActiveTime(Long userId);

}
