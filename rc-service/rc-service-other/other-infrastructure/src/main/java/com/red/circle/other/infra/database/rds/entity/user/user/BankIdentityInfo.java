package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户身份信息.
 * </p>
 *
 * <AUTHOR> on 2023-06-15 17:13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("user_bank_identity_info")
public class BankIdentityInfo extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 身份图片.
   */
  @TableField("identity_cover")
  private String identityCover;

  /**
   * 手机号.
   */
  @TableField("contact")
  private String contact;

  /**
   * 状态.
   */
  @TableField("status")
  private String status;

  /**
   * 类型.
   */
  @TableField("type")
  private String type;

  /**
   * 描述.
   */
  @TableField("description")
  private String description;

  /**
   * 审核描述.
   */
  @TableField("audit_description")
  private String auditDescription;


}
