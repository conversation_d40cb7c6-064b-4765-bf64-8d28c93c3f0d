package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameKingActivityRecord;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 游戏王临时统计表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface GameKingActivityRecordService extends BaseService<GameKingActivityRecord> {

  /**
   * 获得五个周数据
   */
  Map<Long, List<GameKingActivityRecord>> listFiveWeeks(SysOriginPlatformEnum sysOrigin,
      List<Long> rounds);

  /**
   * 获得最新的五组回合数
   */
  List<Long> listFiveWeeksRounds(SysOriginPlatformEnum sysOrigin);

  /**
   * 获得用户id集合
   *
   * @param rounds    回合数
   * @param sysOrigin 系统
   * @return 用户id
   */
  Set<Long> userIdsByRounds(List<Long> rounds, SysOriginPlatformEnum sysOrigin);

}
