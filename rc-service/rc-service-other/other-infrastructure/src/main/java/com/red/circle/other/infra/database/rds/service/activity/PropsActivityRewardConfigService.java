package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRewardConfig;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 活动道具奖励配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
public interface PropsActivityRewardConfigService extends BaseService<PropsActivityRewardConfig> {

  /**
   * 获取分组资源信息.
   *
   * @param groupIds 分组id
   * @return map
   */
  Map<Long, List<PropsActivityRewardConfig>> mapGroupByIds(Set<Long> groupIds);


  /**
   * 根据类型分组.
   *
   * @param groupId 分组id
   * @return map
   */
  Map<String, List<PropsActivityRewardConfig>> mapGroupTypeById(Long groupId);

  /**
   * 获取奖品.
   *
   * @param groupId 分组
   * @return list
   */
  List<PropsActivityRewardConfig> listByGroupId(Long groupId);

  /**
   * 获取奖品.
   *
   * @param groupId 分组
   * @return list
   */
  List<PropsActivityRewardConfig> listByGroupIdType(Long groupId, String type);

  /**
   * 获取奖品.
   *
   * @param groupIds 分组
   * @return list
   */
  List<PropsActivityRewardConfig> listByGroupIds(List<Long> groupIds);

  void deleteByGroupId(Long groupId);
}
