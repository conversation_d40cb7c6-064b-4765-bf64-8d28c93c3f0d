package com.red.circle.other.infra.database.rds.service.gift;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.gift.GiftConfig;
import com.red.circle.other.inner.enums.material.GiftCurrencyType;
import com.red.circle.other.inner.model.cmd.sys.GiftQryCmd;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 礼物信息 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-01
 */
public interface GiftConfigService extends BaseService<GiftConfig> {

  /**
   * 随机获取cp礼物.
   *
   * @param sysOrigin 平台
   */
  GiftConfig getRandomCp(SysOriginPlatformEnum sysOrigin);

  /**
   * 修改新的周星.
   *
   * @param sysOrigin 系统
   * @param gifts     礼物id
   */
  void updateWeekStarSetting(SysOriginPlatformEnum sysOrigin, List<Long> gifts);

  /**
   * 获取一个最便宜的礼物.
   *
   * @param sysOrigin 系统
   * @param type      类型
   * @return ignore
   */
  GiftConfig getCheapest(SysOriginPlatformEnum sysOrigin, GiftCurrencyType type);

  /**
   * 获取有效礼物.
   *
   * @param ids 集合
   * @return ignore
   */
  List<GiftConfig> listByIds(Collection<Long> ids);

  /**
   * 获得集合根据金额排序
   *
   * @param ids 集合
   * @return ignore
   */
  List<GiftConfig> listSortAscCandyByIds(Collection<Long> ids);

  /**
   * 根据id获取有效礼物.
   *
   * @param id 礼物id
   * @return ignore
   */
  GiftConfig getEffectiveById(Long id);

  /**
   * 获取一批指定礼物信息.
   *
   * @param ids 编号id
   * @return ignore
   */
  Map<Long, GiftConfig> mapByIds(Set<Long> ids);

  /**
   * 获取平台下有效的礼物.
   *
   * @param sysOrigin 平台
   * @return list
   */
  List<GiftConfig> listEffectiveSysOriginGift(SysOriginPlatformEnum sysOrigin);

  /**
   * 修改专属礼物不等于的下架.
   */
  void updateExclusiveNotInDown(List<Long> ids);


  /**
   * 根据专属礼物ID修改状态为上架.
   */
  void updateExclusiveRecovery(List<Long> ids);

  /**
   * 根据code查询.
   */
  GiftConfig getByCode(String code);

  /**
   * 获取指定平台礼物信息.
   */
  List<GiftConfig> listBySysOrigin(String sysOrigin);

  /**
   * 获取礼物分页信息.
   */
  PageResult<GiftConfig> pageGiftInfo(GiftQryCmd giftQryCmd);

  /**
   * 修改礼物信息.
   */
  Boolean updateGift(GiftConfig giftConfigDTO);

  /**
   * 上下架礼物.
   */
  Boolean switchDelStatus(Long id, Boolean status);

  /**
   * 礼物列表.
   */
  List<GiftConfig> listByTab(String sysOrigin, String giftTab);

  /**
   * 礼物列表.
   */
  List<GiftConfig> listByTabV2(GiftQryCmd giftQryCmd);

  List<GiftConfig> list(String sysOrigin);

  List<GiftConfig> getLuckyGiftByStandardId(Long id, String name);

  /**
   * 获取平台表白礼物.
   *
   * @param sysOrigin 平台
   * @return list
   */
  List<GiftConfig> listConfessionGifts(String sysOrigin);

  List<GiftConfig> listByExpiredTime(Timestamp now);
}
