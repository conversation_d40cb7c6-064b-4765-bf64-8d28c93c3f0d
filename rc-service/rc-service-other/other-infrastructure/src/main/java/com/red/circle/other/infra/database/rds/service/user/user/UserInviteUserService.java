package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserInviteUser;
import com.red.circle.other.inner.model.cmd.team.bd.BdTeamWorkStatisticsQryCmd;
import com.red.circle.other.inner.model.dto.user.UserInviteUserCountDTO;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> on 2024/3/22
 */
public interface UserInviteUserService extends BaseService<UserInviteUser> {

  List<UserInviteUserCountDTO> listInviteStatisticsByUserIds(BdTeamWorkStatisticsQryCmd qryCmd, Set<Long> userIds);
}
