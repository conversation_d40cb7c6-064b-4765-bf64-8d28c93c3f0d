package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 用户im聊天统计.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("user_im_count")
public class ImCount extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键id.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 发起用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 接受用户id.
   */
  @TableField("accept_user_id")
  private Long acceptUserId;

  /**
   * 发送消息次数.
   */
  @TableField("message_count")
  private Long messageCount;


  /**
   * 是否已点亮爱心，'false.未点亮,true.已点亮'.
   */
  @TableField("light_love")
  private Boolean lightLove;




}
