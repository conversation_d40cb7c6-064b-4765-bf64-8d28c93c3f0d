package com.red.circle.other.infra.enums.user.user;

/**
 * <p>
 * 红包邀请活动-助力用户类型.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 17:21
 */
public enum InviteRedPacketStatusEnum {

  /**
   * 未完成.
   */
  UNDONE("未完成"),

  /**
   * 正常完成.
   */
  COMPLETE("正常完成"),

  /**
   * 提前完成.
   */
  BEFORE_COMPLETE("提前完成");
  private final String desc;

  InviteRedPacketStatusEnum(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }
}
