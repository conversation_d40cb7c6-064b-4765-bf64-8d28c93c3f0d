package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameRoomVoteRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomVoteRecord;
import com.red.circle.other.infra.database.rds.service.game.GameRoomVoteRecordService;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Service
public class GameRoomVoteRecordServiceImpl extends
    BaseServiceImpl<GameRoomVoteRecordDAO, GameRoomVoteRecord> implements
    GameRoomVoteRecordService {

  @Override
  public Boolean isOver(Long roomId) {
    return Objects.isNull(query()
        .eq(GameRoomVoteRecord::getRoomId, roomId)
        .eq(GameRoomVoteRecord::getGameOver, Boolean.FALSE)
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }

  @Override
  public Boolean isOver(Long roomId, Long id) {
    return Objects.isNull(query()
        .eq(GameRoomVoteRecord::getRoomId, roomId)
        .eq(GameRoomVoteRecord::getId, id)
        .eq(GameRoomVoteRecord::getGameOver, Boolean.FALSE)
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }


  @Override
  public GameRoomVoteRecord getEfficientByRoomId(Long roomId) {
    return query()
        .eq(GameRoomVoteRecord::getRoomId, roomId)
        .eq(GameRoomVoteRecord::getGameOver, Boolean.FALSE)
        .orderByDesc(GameRoomVoteRecord::getEndTime)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }
}
