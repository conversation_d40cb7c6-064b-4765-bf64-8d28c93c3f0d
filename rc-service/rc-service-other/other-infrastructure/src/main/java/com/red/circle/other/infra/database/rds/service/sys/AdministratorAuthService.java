package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.AdministratorAuth;
import com.red.circle.other.inner.model.cmd.sys.SysAdministratorAuthCmd;
import java.util.List;

/**
 * <p>
 * APP管理员权限关系表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface AdministratorAuthService extends BaseService<AdministratorAuth> {

  /**
   * 获取用户授权资源id.
   *
   * @param userId 用户id.
   * @return 用户列表
   */
  List<Long> listUserAuthResourceId(Long userId);


  /**
   * 验证用户是否拥有权限.
   */
  boolean existsAuth(Long userId, String authResource);

  /**
   * 验证用户是否拥有权限.
   */
  boolean existsAuth(Long userId, List<String> authResource);

  /**
   * 获取认证资源, 权限.
   */
  List<String> listAuthResources(Long userId);

  List<AdministratorAuth> getSysAdministratorAuthByUserId(Long userId);

  void deleteAndAddAdministratorAuth(SysAdministratorAuthCmd cmd);
}
