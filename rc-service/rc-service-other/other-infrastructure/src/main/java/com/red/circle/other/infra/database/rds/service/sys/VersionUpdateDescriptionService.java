package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.VersionUpdateDescription;
import com.red.circle.other.inner.model.cmd.sys.PageVersionDescQryCmd;
import com.red.circle.other.inner.model.cmd.sys.SysVersionUpdateDescriptionCmd;

/**
 * <p>
 * app版本更新描述 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
public interface VersionUpdateDescriptionService extends BaseService<VersionUpdateDescription> {

  String getUpdateDescription(Long versionId, String language);

  PageResult<VersionUpdateDescription> pageDescription(PageVersionDescQryCmd cmd);

  boolean add(SysVersionUpdateDescriptionCmd param);

  boolean update(SysVersionUpdateDescriptionCmd param);

  VersionUpdateDescription getDescription(SysVersionUpdateDescriptionCmd param);

  boolean deleteById(Long id);

  String getUpdateWorshipDescription(Long id, String reqLanguage);
}
