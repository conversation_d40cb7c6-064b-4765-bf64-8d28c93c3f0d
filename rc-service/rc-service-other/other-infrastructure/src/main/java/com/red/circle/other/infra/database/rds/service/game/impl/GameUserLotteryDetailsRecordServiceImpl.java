package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.convertor.game.GameListConvertor;
import com.red.circle.other.infra.database.rds.dao.game.GameUserLotteryDetailsRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameUserLotteryDetailsRecord;
import com.red.circle.other.infra.database.rds.service.game.GameUserLotteryDetailsRecordService;
import com.red.circle.other.inner.model.dto.game.GameUserLotteryDetailsRecordDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户游戏抽奖明细记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Service
@RequiredArgsConstructor
public class GameUserLotteryDetailsRecordServiceImpl extends
    BaseServiceImpl<GameUserLotteryDetailsRecordDAO, GameUserLotteryDetailsRecord> implements
    GameUserLotteryDetailsRecordService {

  private final GameListConvertor gameListConvertor;

  @Override
  public Map<Long, List<GameUserLotteryDetailsRecordDTO>> mapByLotteryIds(Set<Long> lotteryIds) {

    if (CollectionUtils.isEmpty(lotteryIds)) {
      return Maps.newHashMap();
    }
    return Optional
        .ofNullable(query().in(GameUserLotteryDetailsRecord::getLotteryId, lotteryIds).list())
        .map(giftPackConfigs -> gameListConvertor.toListGameUserLotteryDetailsRecordDTO(
                giftPackConfigs).stream()
            .collect(Collectors.groupingBy(GameUserLotteryDetailsRecordDTO::getLotteryId)))
        .orElse(Maps.newHashMap());

  }
}
