package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.SysMessageCopywritingContent;
import java.util.List;

/**
 * <p>
 * 系统推送文案类容 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public interface SysMessageCopywritingContentService extends
    BaseService<SysMessageCopywritingContent> {

  List<SysMessageCopywritingContent> listByTextTypeId(Long textTypeId);

  List<SysMessageCopywritingContent> listByLanguage(String language);

  void deleteByTypeId(Long id);
}
