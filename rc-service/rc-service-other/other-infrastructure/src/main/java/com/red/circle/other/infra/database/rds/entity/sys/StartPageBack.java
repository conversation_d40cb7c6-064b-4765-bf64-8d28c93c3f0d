package com.red.circle.other.infra.database.rds.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 启动页背景图配置.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_start_page_back")
public class StartPageBack extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 类型.
   */
  @TableField("type")
  private String type;

  /**
   * 背景图.
   */
  @TableField("back_url")
  private String backUrl;

}
