package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLotteryRewardConfig;
import java.util.List;

/**
 * <p>
 * 抽奖游戏奖励配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface GameLotteryRewardConfigService extends BaseService<GameLotteryRewardConfig> {

  /**
   * 获得游戏奖品列表.
   *
   * @param groupId 类型
   */
  List<GameLotteryRewardConfig> listGamePrize(Long groupId);

}
