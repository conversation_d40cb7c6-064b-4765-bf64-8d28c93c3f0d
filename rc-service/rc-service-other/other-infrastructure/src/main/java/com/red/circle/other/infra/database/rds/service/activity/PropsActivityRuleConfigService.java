package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRuleConfig;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRuleConfigParamCmd;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRuleConfigQryCmd;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 活动道具规则配置.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-18
 */
public interface PropsActivityRuleConfigService extends BaseService<PropsActivityRuleConfig> {

  /**
   * 获取平台指定道具类型.
   *
   * @param sysOrigin    平台来源
   * @param activityType 类型
   * @return list
   */
  List<PropsActivityRuleConfig> listRule(SysOriginPlatformEnum sysOrigin,
      PropsActivityTypeEnum activityType);

  /**
   * 爆水晶-获取大于指定等级的规则.
   *
   * @param sysOrigin 归属平台
   * @param level     等级
   * @return rule
   */
  PropsActivityRuleConfig getRuleGtLevelCrystal(SysOriginPlatformEnum sysOrigin, Integer level);

  /**
   * 爆水晶冲刺-获取指定等级的规则.
   *
   * @param sysOrigin 归属平台
   * @param level     等级
   * @return rule
   */
  PropsActivityRuleConfig getGtLevelCrystalSprintRule(SysOriginPlatformEnum sysOrigin, Integer level);

  /**
   * 爆水晶宝箱-获取指定等级的规则.
   *
   * @param sysOrigin 归属平台
   * @param level     等级
   * @return rule
   */
  PropsActivityRuleConfig getGtLevelCrystalLuckyBoxRule(SysOriginPlatformEnum sysOrigin,
      Integer level);

  /**
   * 爆水晶-第一个等级规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  PropsActivityRuleConfig getRuleCrystalFirst(SysOriginPlatformEnum sysOrigin);

  /**
   * LuckyBox-第一个规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  PropsActivityRuleConfig getRuleLuckyBoxFirst(SysOriginPlatformEnum sysOrigin);

  /**
   * 摩天轮Win宝箱奖励最后一个规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  PropsActivityRuleConfig getRuleGameFruitBoxWinLast(SysOriginPlatformEnum sysOrigin);


  /**
   * 摩天轮轮数宝箱奖励最后一个规则
   *
   * @param sysOrigin 归属平台
   * @return rule
   */
  PropsActivityRuleConfig getRuleGameFruitBoxRoundsLast(SysOriginPlatformEnum sysOrigin);

  /**
   * 获取平台指定道具类型.
   *
   * @param sysOrigin    平台来源
   * @param activityType 类型
   * @return list
   */
  List<PropsActivityRuleConfig> listPlatformByActivityType(String sysOrigin,
      PropsActivityTypeEnum activityType);

  /**
   * 查询活动道具规则配置
   */
  Map<Long, PropsActivityRuleConfig> mapByIds(Collection<Long> ids);

  /**
   * 删除活动道具规则配置
   */
  void deleteById(Long id);

  /**
   * 查询活动道具规则配置
   */
  PropsActivityRuleConfig getPropsActivityRuleConfig(PropsActivityRuleConfigParamCmd param);

  /**
   * 分页-查询活动道具规则配置
   */
  PageResult<PropsActivityRuleConfig> pagePropsActivityRuleConfig(
      PropsActivityRuleConfigQryCmd query);
}
