package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.convertor.game.GameListConvertor;
import com.red.circle.other.infra.database.rds.dao.game.GameKtvGiftConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameKtvGiftConfig;
import com.red.circle.other.infra.database.rds.service.game.GameKtvGiftConfigService;
import com.red.circle.other.inner.model.cmd.game.GameKtvGiftConfigQryCmd;
import com.red.circle.other.inner.model.dto.game.GameKtvGiftConfigDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ktv礼物配置信息.
 * </p>
 *
 * <AUTHOR> on 2023-05-05 18:06
 */
@Service
@RequiredArgsConstructor
public class GameKtvGiftConfigServiceImpl extends
    BaseServiceImpl<GameKtvGiftConfigDAO, GameKtvGiftConfig> implements
    GameKtvGiftConfigService {

  private final GameListConvertor gameListConvertor;

  @Override
  public List<GameKtvGiftConfig> listGiftEmojiByRegion(String sysOrigin, String regionId) {

    return Optional.ofNullable(query()
        .eq(GameKtvGiftConfig::getDel, Boolean.FALSE)
        .in(GameKtvGiftConfig::getResourceType,
            List.of("EMOJI", "GIFT"))
        .like(GameKtvGiftConfig::getRegions, regionId)
        .eq(GameKtvGiftConfig::getSysOrigin, sysOrigin)
        .last(PageConstant.formatLimit(20))
        .list()).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public PageResult<GameKtvGiftConfig> getSongGiftInfo(GameKtvGiftConfigQryCmd query) {
    return query().eq(GameKtvGiftConfig::getDel, Boolean.FALSE)
        .eq(Objects.nonNull(query.getResourceId()), GameKtvGiftConfig::getResourceId,
            query.getResourceId())
        .eq(StringUtils.isNotBlank(query.getResourceType()), GameKtvGiftConfig::getResourceType,
            query.getResourceType())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameKtvGiftConfig::getSysOrigin,
            query.getSysOrigin())
        .like(StringUtils.isNotBlank(query.getRegion()), GameKtvGiftConfig::getRegions,
            query.getRegion())
        .page(query.getPageQuery());
  }


  @Override
  public void addSongGift(GameKtvGiftConfigDTO param) {
    Long count = countByResourceId(param);
    ResponseAssert.isFalse(CommonErrorCode.SAVE_FAILURE, count > 0 ? Boolean.TRUE : Boolean.FALSE);
    GameKtvGiftConfig gameKtvGiftConfig = getGameKtvGiftConfig(param);
    ResponseAssert.isTrue(CommonErrorCode.SAVE_FAILURE, save(gameKtvGiftConfig));
  }


  @Override
  public void updateSongGift(GameKtvGiftConfigDTO param) {
    Long count = countByResourceId(param);
    ResponseAssert.isFalse(CommonErrorCode.UPDATE_FAILURE,
        count > 0 ? Boolean.TRUE : Boolean.FALSE);
    GameKtvGiftConfig gameKtvGiftConfig = getGameKtvGiftConfig(param);
    ResponseAssert.isTrue(CommonErrorCode.UPDATE_FAILURE, updateSongGiftInfo(gameKtvGiftConfig));
  }


  @Override
  public void deleteSongGift(Long id, Long updateUser) {
    ResponseAssert.isTrue(CommonErrorCode.DELETE_FAILURE,
        update().set(GameKtvGiftConfig::getDel, Boolean.TRUE)
            .set(GameKtvGiftConfig::getUpdateUser, updateUser)
            .eq(GameKtvGiftConfig::getId, id)
            .execute());
  }


  private Long countByResourceId(GameKtvGiftConfigDTO param) {
    if (CollectionUtils.isNotEmpty(param.getRegionList())) {
      for (String region : param.getRegionList()) {
        long count = query().eq(GameKtvGiftConfig::getSysOrigin, param.getSysOrigin())
            .eq(GameKtvGiftConfig::getResourceId, param.getResourceId())
            .like(GameKtvGiftConfig::getRegions, region)
            .eq(GameKtvGiftConfig::getDel, Boolean.FALSE)
            .ne(Objects.nonNull(param.getId()), GameKtvGiftConfig::getId, param.getId())
            .count();
        if (count > 0) {
          return count;
        }
      }
    }
    return 0L;
  }


  private boolean updateSongGiftInfo(GameKtvGiftConfig gameKtvGiftConfig) {
    return update()
        .set(GameKtvGiftConfig::getResourceId, gameKtvGiftConfig.getResourceId())
        .set(GameKtvGiftConfig::getResourceType, gameKtvGiftConfig.getResourceType())
        .set(GameKtvGiftConfig::getRegions, gameKtvGiftConfig.getRegions())
        .set(GameKtvGiftConfig::getSysOrigin, gameKtvGiftConfig.getSysOrigin())
        .eq(GameKtvGiftConfig::getId, gameKtvGiftConfig.getId())
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  private GameKtvGiftConfig getGameKtvGiftConfig(GameKtvGiftConfigDTO param) {
    GameKtvGiftConfig gameKtvGiftConfig = gameListConvertor.toGameKtvGiftConfig(param);
    gameKtvGiftConfig.setRegions(StringUtils.join(param.getRegionList(), ","));
    return gameKtvGiftConfig;
  }

}
