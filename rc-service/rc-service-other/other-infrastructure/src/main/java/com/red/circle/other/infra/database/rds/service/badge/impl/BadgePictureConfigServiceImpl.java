package com.red.circle.other.infra.database.rds.service.badge.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.badge.BadgePictureConfigDAO;
import com.red.circle.other.infra.database.rds.entity.badge.BadgePictureConfig;
import com.red.circle.other.infra.database.rds.service.badge.BadgePictureConfigService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 徽章图片配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@Service
public class BadgePictureConfigServiceImpl extends
    BaseServiceImpl<BadgePictureConfigDAO, BadgePictureConfig> implements
    BadgePictureConfigService {


  @Override
  public List<BadgePictureConfig> listByIds(Collection<Long> badgeIds, String sysOrigin) {
    if (CollectionUtils.isEmpty(badgeIds) || StringUtils.isBlank(sysOrigin)) {
      return CollectionUtils.newArrayList();
    }
    return query().in(BadgePictureConfig::getBadgeConfigId, badgeIds)
        .eq(BadgePictureConfig::getSysOrigin, sysOrigin).list();
  }

  @Override
  public Map<Long, BadgePictureConfig> mapBadge(String sysOrigin, Set<Long> badgeIds) {
    if (CollectionUtils.isEmpty(badgeIds)) {
      return CollectionUtils.newHashMap();
    }
    return Optional.ofNullable(
            query().in(BadgePictureConfig::getBadgeConfigId, badgeIds)
                .eq(BadgePictureConfig::getSysOrigin, sysOrigin).list())
        .map(badgePictureConfigList -> badgePictureConfigList.stream()
            .collect(Collectors.toMap(BadgePictureConfig::getBadgeConfigId, Function.identity(),
                (v0, v1) -> v1)))
        .orElse(CollectionUtils.newHashMap());
  }

  @Override
  public BadgePictureConfig getByBadgeId(String sysOrigin, Long badgeId) {

    if (StringUtils.isBlank(sysOrigin)) {
      return null;
    }

    if (Objects.isNull(badgeId)) {
      return null;
    }

    return Optional.ofNullable(query()
        .eq(BadgePictureConfig::getSysOrigin, sysOrigin)
        .eq(BadgePictureConfig::getBadgeConfigId, badgeId)
        .last(PageConstant.LIMIT_ONE)
        .getOne()).orElse(null);
  }

  @Override
  public List<BadgePictureConfig> listBySysOrigin(String sysOrigin) {
    return query()
        .eq(BadgePictureConfig::getSysOrigin, sysOrigin)
        .orderByDesc(BadgePictureConfig::getId)
        .list();
  }

  @Override
  public Map<Long, List<BadgePictureConfig>> mapGroupSysBadgePicture(Set<Long> badgeIds) {
    if (CollectionUtils.isEmpty(badgeIds)) {
      return Maps.newHashMap();
    }
    return Optional.ofNullable(query().in(BadgePictureConfig::getBadgeConfigId, badgeIds).list())
        .map(badgeList -> badgeList.stream()
            .collect(Collectors.groupingBy(BadgePictureConfig::getBadgeConfigId)))
        .orElseGet(Maps::newHashMap);
  }

  @Override
  public BadgePictureConfig getByConfigIdBySysOrigin(Long configId, String sysOrigin) {
    return query()
        .eq(BadgePictureConfig::getSysOrigin, sysOrigin)
        .eq(BadgePictureConfig::getBadgeConfigId, configId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

}
