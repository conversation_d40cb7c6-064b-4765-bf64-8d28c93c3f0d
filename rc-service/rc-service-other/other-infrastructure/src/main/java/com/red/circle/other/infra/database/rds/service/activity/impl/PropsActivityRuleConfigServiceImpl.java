package com.red.circle.other.infra.database.rds.service.activity.impl;

import com.google.common.collect.Maps;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.PropsActivityRuleConfigDAO;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRuleConfig;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRuleConfigService;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRuleConfigParamCmd;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRuleConfigQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动道具规则配置.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-18
 */
@Service
@RequiredArgsConstructor
public class PropsActivityRuleConfigServiceImpl extends
    BaseServiceImpl<PropsActivityRuleConfigDAO, PropsActivityRuleConfig> implements
    PropsActivityRuleConfigService {

  @Override
  public List<PropsActivityRuleConfig> listRule(SysOriginPlatformEnum sysOrigin,
      PropsActivityTypeEnum activityType) {
    return query()
        .eq(PropsActivityRuleConfig::getActivityType, activityType)
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .orderByAsc(PropsActivityRuleConfig::getSort)
        .list();
  }

  @Override
  public PropsActivityRuleConfig getGtLevelCrystalLuckyBoxRule(SysOriginPlatformEnum sysOrigin,
      Integer level) {
    return query()
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getActivityType, PropsActivityTypeEnum.CRYSTAL_LUCKY_BOX)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .eq(PropsActivityRuleConfig::getSort, level)
        .orderByAsc(PropsActivityRuleConfig::getSort)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PropsActivityRuleConfig getGtLevelCrystalSprintRule(SysOriginPlatformEnum sysOrigin,
      Integer level) {
    return query()
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getActivityType, PropsActivityTypeEnum.CRYSTAL_TOP)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .eq(PropsActivityRuleConfig::getSort, level)
        .orderByAsc(PropsActivityRuleConfig::getSort)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PropsActivityRuleConfig getRuleGtLevelCrystal(SysOriginPlatformEnum sysOrigin,
      Integer level) {
    return query()
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getActivityType, PropsActivityTypeEnum.CRYSTAL)
        .gt(PropsActivityRuleConfig::getSort, level)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .orderByAsc(PropsActivityRuleConfig::getSort)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PropsActivityRuleConfig getRuleCrystalFirst(SysOriginPlatformEnum sysOrigin) {
    return query()
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .eq(PropsActivityRuleConfig::getActivityType, PropsActivityTypeEnum.CRYSTAL)
        .orderByAsc(PropsActivityRuleConfig::getSort)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PropsActivityRuleConfig getRuleLuckyBoxFirst(SysOriginPlatformEnum sysOrigin) {
    return query()
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .eq(PropsActivityRuleConfig::getActivityType, PropsActivityTypeEnum.LUCKY_BOX)
        .orderByAsc(PropsActivityRuleConfig::getSort)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PropsActivityRuleConfig getRuleGameFruitBoxWinLast(SysOriginPlatformEnum sysOrigin) {
    return query()
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .eq(PropsActivityRuleConfig::getActivityType,
            PropsActivityTypeEnum.GAME_FRUIT_BOX_REWARD_WIN)
        .orderByDesc(PropsActivityRuleConfig::getSort)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }


  @Override
  public PropsActivityRuleConfig getRuleGameFruitBoxRoundsLast(SysOriginPlatformEnum sysOrigin) {
    return query()
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .eq(PropsActivityRuleConfig::getActivityType,
            PropsActivityTypeEnum.GAME_FRUIT_BOX_REWARD_TIMES)
        .orderByDesc(PropsActivityRuleConfig::getSort)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public List<PropsActivityRuleConfig> listPlatformByActivityType(String sysOrigin,
      PropsActivityTypeEnum activityType) {
    return query()
        .eq(PropsActivityRuleConfig::getActivityType, activityType)
        .eq(PropsActivityRuleConfig::getSysOrigin, sysOrigin)
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .orderByAsc(PropsActivityRuleConfig::getSort)
        .list();
  }


  @Override
  public Map<Long, PropsActivityRuleConfig> mapByIds(Collection<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return Maps.newHashMap();
    }
    return Optional.ofNullable(query().in(PropsActivityRuleConfig::getId, ids).list())
        .map(ruleConfigs -> ruleConfigs.stream()
            .collect(Collectors.toMap(PropsActivityRuleConfig::getId, v -> v)))
        .orElse(Maps.newHashMap());
  }

  @Override
  public void deleteById(Long id) {
    update()
        .set(PropsActivityRuleConfig::getDel, Boolean.TRUE)
        .eq(PropsActivityRuleConfig::getId, id)
        .execute();
  }

  @Override
  public PropsActivityRuleConfig getPropsActivityRuleConfig(PropsActivityRuleConfigParamCmd param) {
    return Optional.ofNullable(query()
            .eq(PropsActivityRuleConfig::getActivityType, param.getActivityType())
            .eq(PropsActivityRuleConfig::getResourceGroupId, param.getResourceGroupId())
            .eq(PropsActivityRuleConfig::getSysOrigin, param.getSysOrigin())
            .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .orElse(null);
  }

  @Override
  public PageResult<PropsActivityRuleConfig> pagePropsActivityRuleConfig(
      PropsActivityRuleConfigQryCmd query) {
    return query()
        .like(StringUtils.isNotBlank(query.getRuleDescription()),
            PropsActivityRuleConfig::getRuleDescription, query.getRuleDescription())
        .eq(Objects.nonNull(query.getId()), PropsActivityRuleConfig::getId, query.getId())
        .eq(StringUtils.isNotBlank(query.getActivityType()),
            PropsActivityRuleConfig::getActivityType,
            query.getActivityType())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), PropsActivityRuleConfig::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getResourceGroupId()),
            PropsActivityRuleConfig::getResourceGroupId,
            query.getResourceGroupId())
        .eq(PropsActivityRuleConfig::getDel, Boolean.FALSE)
        .orderByDesc(PropsActivityRuleConfig::getId)
        .page(query.getPageQuery());
  }

}
