package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyGiftRuleConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyGiftRuleConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyGiftRuleConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 幸运礼物规则配置.
 * </p>
 *
 * <AUTHOR> on 2023-06-16 16:20
 */
@Service
@RequiredArgsConstructor
public class GameLuckyGiftRuleConfigServiceImpl extends
    BaseServiceImpl<GameLuckyGiftRuleConfigDAO, GameLuckyGiftRuleConfig> implements
    GameLuckyGiftRuleConfigService {

  @Override
  public GameLuckyGiftRuleConfig getRuleConfig() {

    return query().last(PageConstant.LIMIT_ONE).getOne();
  }

  @Override
  public GameLuckyGiftRuleConfig getLuckyGiftRuleConfig(String sysOrigin) {
    return query().eq(GameLuckyGiftRuleConfig::getSysOrigin, sysOrigin).getOne();
  }

}
