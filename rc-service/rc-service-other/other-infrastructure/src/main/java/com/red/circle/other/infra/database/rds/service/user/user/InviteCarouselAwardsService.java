package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteCarouselAwards;
import com.red.circle.other.inner.model.cmd.user.invite.InviteCarouselAwardsCmd;
import java.util.List;

/**
 * <p>
 * 邀请新用户-转盘奖项 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
public interface InviteCarouselAwardsService extends BaseService<InviteCarouselAwards> {

  List<InviteCarouselAwards> listBySysOrigin(String sysOrigin);
  void saveOrUpd(InviteCarouselAwardsCmd awards);

  void deleteById(Long id);

}
