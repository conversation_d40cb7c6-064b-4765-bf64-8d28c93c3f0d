package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.google.common.collect.Maps;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.MikeTypeConfigDAO;
import com.red.circle.other.infra.database.rds.entity.sys.MikeTypeConfig;
import com.red.circle.other.infra.database.rds.service.sys.MikeTypeConfigService;
import com.red.circle.other.inner.model.cmd.sys.SysMikeTypeConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 麦位类型配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-04-19 15:24
 */
@Service
public class MikeTypeConfigServiceImpl extends
    BaseServiceImpl<MikeTypeConfigDAO, MikeTypeConfig> implements
    MikeTypeConfigService {

  @Override
  public List<MikeTypeConfig> getMikeTypeList(String sysOrigin) {
    return query()
        .eq(MikeTypeConfig::getShowcase, Boolean.TRUE)
        .eq(MikeTypeConfig::getDel, Boolean.FALSE)
        .eq(MikeTypeConfig::getSysOrigin, sysOrigin)
        .orderByDesc(MikeTypeConfig::getSort)
        .last(PageConstant.MAX_LIMIT).list();
  }

  @Override
  public BigDecimal getMikeTypeAmount(String mikeType, String duration,
      SysOriginPlatformEnum sysOrigin) {
    if (Objects.equals(duration, "FIFTEEN_DAYS")) {
      return Optional.ofNullable(
          query().select(MikeTypeConfig::getFifteenMikeCandy)
              .eq(MikeTypeConfig::getShowcase, Boolean.TRUE)
              .eq(MikeTypeConfig::getDel, Boolean.FALSE)
              .eq(MikeTypeConfig::getSysOrigin, sysOrigin)
              .eq(MikeTypeConfig::getMikeType, mikeType)
              .last(PageConstant.LIMIT_ONE)
              .getOne()
      ).map(MikeTypeConfig::getFifteenMikeCandy).orElse(BigDecimal.ZERO);
    }

    if (Objects.equals(duration, "FOREVER")) {
      return Optional.ofNullable(
          query().select(MikeTypeConfig::getLongMikeCandy)
              .eq(MikeTypeConfig::getShowcase, Boolean.TRUE)
              .eq(MikeTypeConfig::getDel, Boolean.FALSE)
              .eq(MikeTypeConfig::getSysOrigin, sysOrigin)
              .eq(MikeTypeConfig::getMikeType, mikeType)
              .last(PageConstant.LIMIT_ONE)
              .getOne()
      ).map(MikeTypeConfig::getLongMikeCandy).orElse(BigDecimal.ZERO);
    }
    return BigDecimal.ZERO;
  }


  @Override
  public Map<String, String> getMikeTypeByType(String sysOrigin) {
    return Optional.ofNullable(query()
            .eq(MikeTypeConfig::getDel, Boolean.FALSE)
            .eq(MikeTypeConfig::getSysOrigin, sysOrigin).list())
        .map(sysMikeTypeConfigs -> sysMikeTypeConfigs.stream().collect(
            Collectors.toMap(MikeTypeConfig::getMikeType, MikeTypeConfig::getMikeCover))).orElse(
            Maps.newHashMap());
  }

  @Override
  public PageResult<MikeTypeConfig> getMikeType(SysMikeTypeConfigQryCmd query) {
    return query().eq(MikeTypeConfig::getDel, Boolean.FALSE)
        .eq(Objects.equals(query.getShowcase(), 1), MikeTypeConfig::getShowcase, Boolean.TRUE)
        .eq(Objects.equals(query.getShowcase(), 2), MikeTypeConfig::getShowcase, Boolean.FALSE)
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), MikeTypeConfig::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getChargeType()), MikeTypeConfig::getChargeType,
            query.getChargeType())
        .orderByAsc(MikeTypeConfig::getSort)
        .page(query.getPageQuery());
  }

  @Override
  public void removeMikeType(Long id) {
    update().set(MikeTypeConfig::getDel, Boolean.TRUE)
        .eq(MikeTypeConfig::getId, id)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public Long countByMikeType(String mikeType, String sysOrigin) {
    return query().eq(MikeTypeConfig::getMikeType, mikeType)
        .eq(MikeTypeConfig::getDel, Boolean.FALSE)
        .eq(MikeTypeConfig::getSysOrigin, sysOrigin)
        .count();
  }

  @Override
  public Long countByMikeTypeAndId(Long id, String mikeType, String sysOrigin) {
    return query().eq(MikeTypeConfig::getMikeType, mikeType)
        .eq(MikeTypeConfig::getDel, Boolean.FALSE)
        .eq(MikeTypeConfig::getSysOrigin, sysOrigin)
        .ne(MikeTypeConfig::getId, id)
        .count();
  }

  @Override
  public Boolean updateInfo(MikeTypeConfig sysMikeTypeConfig) {
    return update().set(MikeTypeConfig::getChargeType, sysMikeTypeConfig.getChargeType())
        .set(MikeTypeConfig::getMikeCover, sysMikeTypeConfig.getMikeCover())
        .set(MikeTypeConfig::getMikeIcon, sysMikeTypeConfig.getMikeIcon())
        .set(MikeTypeConfig::getFifteenMikeCandy, sysMikeTypeConfig.getFifteenMikeCandy())
        .set(MikeTypeConfig::getMikeName, sysMikeTypeConfig.getMikeName())
        .set(MikeTypeConfig::getMikeType, sysMikeTypeConfig.getMikeType())
        .set(MikeTypeConfig::getLongMikeCandy, sysMikeTypeConfig.getLongMikeCandy())
        .set(MikeTypeConfig::getSysOrigin, sysMikeTypeConfig.getSysOrigin())
        .set(MikeTypeConfig::getSort, sysMikeTypeConfig.getSort())
        .set(MikeTypeConfig::getShowcase, sysMikeTypeConfig.getShowcase())
        .eq(MikeTypeConfig::getId, sysMikeTypeConfig.getId())
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }
}
