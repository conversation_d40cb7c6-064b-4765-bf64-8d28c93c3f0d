package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户签到日志.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_check_in_log")
public class CheckInLog extends TimestampBaseEntity implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 打卡类型.
   */
  @TableField("type")
  private String type;

  /**
   * 连续打卡天数.
   */
  @TableField("check_in_number")
  private Integer checkInNumber;

  /**
   * 奖励数量.
   */
  @TableField("quantity")
  private BigDecimal quantity;

  /**
   * 道具ID.
   */
  @TableField("props_id")
  private Long propsId;

}
