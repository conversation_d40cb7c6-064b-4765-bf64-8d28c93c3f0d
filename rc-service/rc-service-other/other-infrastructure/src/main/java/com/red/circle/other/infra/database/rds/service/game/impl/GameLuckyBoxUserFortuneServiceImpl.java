package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxUserFortuneDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxUserFortune;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxUserFortuneService;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 用户幸运值详情 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxUserFortuneServiceImpl extends
    BaseServiceImpl<GameLuckyBoxUserFortuneDAO, GameLuckyBoxUserFortune> implements
    GameLuckyBoxUserFortuneService {

  @Override
  public GameLuckyBoxUserFortune getFortuneValueByUserId(Long userId) {
    return query().eq(GameLuckyBoxUserFortune::getUserId, userId).getOne();
  }
}
