package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxCountDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxCount;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxCountService;
import com.red.circle.other.inner.model.cmd.game.LuckBoxGameQryCmd;
import com.red.circle.other.inner.model.dto.game.GameLuckyBoxCountInfoDTO;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * lucky-box抽奖记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxCountServiceImpl extends
    BaseServiceImpl<GameLuckyBoxCountDAO, GameLuckyBoxCount> implements
    GameLuckyBoxCountService {

  @Override
  public List<GameLuckyBoxCount> listByUserId(Long userId, Long lastId) {
    return query()
        .eq(GameLuckyBoxCount::getUserId, userId)
        .lt(Objects.nonNull(lastId), GameLuckyBoxCount::getId, lastId)
        .orderByDesc(GameLuckyBoxCount::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }

  @Override
  public List<GameLuckyBoxCount> getAwardRecord() {
    return query()
        .orderByDesc(GameLuckyBoxCount::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }

  @Override
  public PageResult<GameLuckyBoxCount> pageByCondition(LuckBoxGameQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getRoomId()), GameLuckyBoxCount::getRoomId, query.getRoomId())
        .eq(Objects.nonNull(query.getUserId()), GameLuckyBoxCount::getUserId, query.getUserId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameLuckyBoxCount::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getFrequency()), GameLuckyBoxCount::getFrequency,
            query.getFrequency())
        .ge(Objects.nonNull(query.getStartTime()), GameLuckyBoxCount::getCreateTime,
            query.getStartTime())
        .le(Objects.nonNull(query.getEndTime()), GameLuckyBoxCount::getCreateTime,
            query.getEndTime())
        .orderByDesc(GameLuckyBoxCount::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public GameLuckyBoxCountInfoDTO countLuckyBoxGame(LuckBoxGameQryCmd query) {
    return baseDAO.countLuckyBoxGame(query);
  }
}
