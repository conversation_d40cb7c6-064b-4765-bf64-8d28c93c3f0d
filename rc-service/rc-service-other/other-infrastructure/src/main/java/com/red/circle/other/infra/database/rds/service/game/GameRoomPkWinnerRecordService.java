package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkWinnerRecord;
import java.util.List;

/**
 * <p>
 * 房间pk游戏 胜利者表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface GameRoomPkWinnerRecordService extends BaseService<GameRoomPkWinnerRecord> {

  /**
   * 获得最新的十位胜利者
   */
  List<GameRoomPkWinnerRecord> listTop10(String sysOrigin);

  /**
   * 周top
   */
  List<GameRoomPkWinnerRecord> weekTop(SysOriginPlatformEnum sysOrigin, Boolean isLastWeek);

  /**
   * 日top
   */
  List<GameRoomPkWinnerRecord> dayTop(SysOriginPlatformEnum sysOrigin, Boolean isYesterday);

  /**
   * 周单挑王
   */
  GameRoomPkWinnerRecord weekSinglesKing(SysOriginPlatformEnum sysOrigin, Boolean isLastWeek);

  /**
   * 根据时间获得区间数据
   */
  List<GameRoomPkWinnerRecord> listSpecifiedTimeWeekTop(SysOriginPlatformEnum sysOrigin,
      String startTime,
      String endTime);

  /**
   * 根据时间获得区间数据
   */
  GameRoomPkWinnerRecord getSpecifiedTimeWeekSinglesKing(SysOriginPlatformEnum origin,
      String startTime, String endTime);

}
