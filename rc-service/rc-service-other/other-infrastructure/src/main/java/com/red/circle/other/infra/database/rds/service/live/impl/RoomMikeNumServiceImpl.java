package com.red.circle.other.infra.database.rds.service.live.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.live.RoomMikeNumDAO;
import com.red.circle.other.infra.database.rds.entity.live.RoomMikeNum;
import com.red.circle.other.infra.database.rds.service.live.RoomMikeNumService;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * 设置房间麦数量服务实现.
 *
 * <AUTHOR> on 2023/3/28 17:25
 */
@Service
public class RoomMikeNumServiceImpl extends
    BaseServiceImpl<RoomMikeNumDAO, RoomMikeNum> implements
    RoomMikeNumService {

  @Override
  public List<RoomMikeNum> listByUserIdRoomMikeNumRecord(Long userId) {
    return query().eq(RoomMikeNum::getUserId, userId).list();
  }

  @Override
  public boolean isPurchaseMikeNum(Long userId, String mikeNumType) {
    return Objects.nonNull(query()
        .select(RoomMikeNum::getId)
        .eq(RoomMikeNum::getUserId, userId)
        .eq(RoomMikeNum::getMikeNumType, mikeNumType)
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }
}
