package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户注册信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_register_info")
public class RegisterInfo extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户ID
   */
  @TableId("user_id")
  private Long userId;

  /**
   * 用户邮箱
   */
  @TableField("email")
  private String email;

  /**
   * 居住地址
   */
  @TableField("residential_address")
  private String residentialAddress;

  /**
   * 认证类型
   */
  @TableField("auth_type")
  private String authType;

  /**
   * 来源平台
   */
  @TableField("origin_platform")
  private String originPlatform;

  /**
   * 来源手机型号
   */
  @TableField("origin_phone_model")
  private String originPhoneModel;
}
