package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsRoomLockDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsRoomLock;
import com.red.circle.other.infra.database.rds.service.props.PropsRoomLockService;
import com.red.circle.tool.core.date.TimestampUtils;
import java.sql.Timestamp;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间锁 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Service
public class PropsRoomLockServiceImpl extends
    BaseServiceImpl<PropsRoomLockDAO, PropsRoomLock> implements
    PropsRoomLockService {

  @Override
  public boolean saveOrUpdate(Long userId, Integer appendDays) {
    PropsRoomLock roomLock = query().eq(PropsRoomLock::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.nonNull(roomLock)) {
      roomLock
          .setExpireTime(TimestampUtils.expiredPlusDays(roomLock.getExpireTime(), appendDays));
      return updateSelectiveById(roomLock);
    }

    return save(new PropsRoomLock()
        .setUserId(userId)
        .setExpireTime(TimestampUtils.nowPlusDays(appendDays))
    );
  }


  @Override
  public Timestamp getExpireTime(Long userId) {
    return Optional.ofNullable(
        query().select(PropsRoomLock::getExpireTime)
            .eq(PropsRoomLock::getUserId, userId)
            .gt(PropsRoomLock::getExpireTime, TimestampUtils.now())
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).map(PropsRoomLock::getExpireTime).orElse(null);
  }

  @Override
  public boolean existsExpireTime(Long userId) {
    return Optional.ofNullable(query().select(PropsRoomLock::getId)
        .eq(PropsRoomLock::getUserId, userId)
        .gt(PropsRoomLock::getExpireTime, TimestampUtils.now())
        .last(PageConstant.LIMIT_ONE)
        .getOne()
    ).map(propsRoomLock -> Objects.nonNull(propsRoomLock.getId())).orElse(Boolean.FALSE);
  }

}
