package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxGiftConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxStandardDetailsConfigQryCmd;
import java.util.List;

/**
 * <p>
 * 抽奖规格礼物配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxGiftConfigService extends BaseService<GameLuckyBoxGiftConfig> {

  List<GameLuckyBoxGiftConfig> listByStandardId(Long standardId);

  void deleteByStandardId(Long id);

  List<GameLuckyBoxGiftConfig> getByStandardId(GameLuckyBoxStandardDetailsConfigQryCmd query);
}
