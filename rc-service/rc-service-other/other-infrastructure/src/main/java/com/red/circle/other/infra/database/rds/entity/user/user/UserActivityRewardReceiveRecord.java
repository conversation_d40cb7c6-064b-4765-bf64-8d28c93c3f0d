package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户活动月度领取记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_activity_reward_receive_record")
public class UserActivityRewardReceiveRecord extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户ID.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 充值年月(yyyyMM).
   */
  @TableField("recharge_date")
  private Integer rechargeDate;

  /**
   * 活动规则ID.
   */
  @TableField("activity_id")
  private Long activityId;

}
