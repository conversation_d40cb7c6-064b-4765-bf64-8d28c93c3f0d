package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxGiftConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxGiftConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxGiftConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxStandardDetailsConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖规格礼物配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxGiftConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxGiftConfigDAO, GameLuckyBoxGiftConfig> implements
    GameLuckyBoxGiftConfigService {

  @Override
  public List<GameLuckyBoxGiftConfig> listByStandardId(Long standardId) {
    return query().eq(GameLuckyBoxGiftConfig::getStandardId, standardId)
        .orderByAsc(GameLuckyBoxGiftConfig::getSort).list();
  }

  @Override
  public void deleteByStandardId(Long standardId) {
    delete().eq(GameLuckyBoxGiftConfig::getStandardId, standardId).execute();
  }

  @Override
  public List<GameLuckyBoxGiftConfig> getByStandardId(
      GameLuckyBoxStandardDetailsConfigQryCmd query) {
    return query().eq(
            GameLuckyBoxGiftConfig::getStandardId, query.getStandardId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()),
            GameLuckyBoxGiftConfig::getSysOrigin, query.getSysOrigin()).list();
  }
}
