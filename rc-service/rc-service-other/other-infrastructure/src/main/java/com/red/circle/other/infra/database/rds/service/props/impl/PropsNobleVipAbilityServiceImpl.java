package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsNobleVipAbilityDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsNobleVipAbility;
import com.red.circle.other.infra.database.rds.service.props.PropsNobleVipAbilityService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 贵族能力 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
@Service
public class PropsNobleVipAbilityServiceImpl extends
    BaseServiceImpl<PropsNobleVipAbilityDAO, PropsNobleVipAbility> implements
    PropsNobleVipAbilityService {

  @Override
  public Map<Long, PropsNobleVipAbility> mapByIds(Set<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newHashMap();
    }

    return Optional.ofNullable(query().in(PropsNobleVipAbility::getId, ids).list())
        .map(propsNobleVipAbilities -> propsNobleVipAbilities.stream()
            .collect(Collectors.toMap(PropsNobleVipAbility::getId, v -> v)))
        .orElse(CollectionUtils.newHashMap());
  }

  @Override
  public List<PropsNobleVipAbility> listByIds(List<Long> ids) {
    return query().in(PropsNobleVipAbility::getId, ids).list();
  }

  @Override
  public List<PropsNobleVipAbility> listByIds(Collection<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newArrayList();
    }

    return query()
        .eq(Objects.equals(ids.size(), 1), PropsNobleVipAbility::getId, ids.iterator().next())
        .in(ids.size() > 1, PropsNobleVipAbility::getId, ids)
        .list();
  }
}
