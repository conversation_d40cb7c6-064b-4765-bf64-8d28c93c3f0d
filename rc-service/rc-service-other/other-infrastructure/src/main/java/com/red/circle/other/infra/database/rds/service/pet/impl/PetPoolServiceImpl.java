package com.red.circle.other.infra.database.rds.service.pet.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.pet.PetPoolDAO;
import com.red.circle.other.infra.database.rds.entity.pet.PetPool;
import com.red.circle.other.infra.database.rds.service.pet.PetPoolService;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 宠物池 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PetPoolServiceImpl extends BaseServiceImpl<PetPoolDAO, PetPool> implements
    PetPoolService {

  @Override
  public IPage<PetPool> pagePet(IPage<PetPool> page, SysOriginPlatformEnum sysOrigin) {
    return query()
        .eq(PetPool::getSysOrigin, sysOrigin)
        .eq(PetPool::getShelf, Boolean.TRUE)
        .orderByAsc(PetPool::getLevel)
        .page(page);
  }

  @Override
  public Long getFirstPetId(SysOriginPlatformEnum sysOrigin) {
    return Optional.ofNullable(query()
            .select(PetPool::getId)
            .eq(PetPool::getSysOrigin, sysOrigin)
            .orderByAsc(PetPool::getLevel)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(PetPool::getId)
        .orElse(null);
  }

}
