package com.red.circle.other.infra.database.rds.service.pet.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.pet.PetUserPetIncomeDetailsDAO;
import com.red.circle.other.infra.database.rds.entity.pet.PetUserPetIncomeDetails;
import com.red.circle.other.infra.database.rds.service.pet.PetUserPetIncomeDetailsService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 我的宠物收益明细 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PetUserPetIncomeDetailsServiceImpl extends
    BaseServiceImpl<PetUserPetIncomeDetailsDAO, PetUserPetIncomeDetails> implements
    PetUserPetIncomeDetailsService {

  @Override
  public List<PetUserPetIncomeDetails> listDetailsTwoSize(Long userId) {
    return query()
        .eq(PetUserPetIncomeDetails::getUserId, userId)
        .eq(PetUserPetIncomeDetails::getReceive, Boolean.FALSE)
        .last(PageConstant.formatLimit(2))
        .list();
  }

  @Override
  public boolean checkCanReceive(Long id) {
    return Optional.ofNullable(getById(id))
        .map(details -> Objects.equals(details.getReceive(), Boolean.FALSE))
        .orElse(Boolean.FALSE);
  }

  @Override
  public boolean updateCharged(PetUserPetIncomeDetails details) {
    return update()
        .set(PetUserPetIncomeDetails::getPetVersion, details.getPetVersion() + 1)
        .set(PetUserPetIncomeDetails::getReceive, Boolean.TRUE)
        .eq(PetUserPetIncomeDetails::getId, details.getId())
        .eq(PetUserPetIncomeDetails::getPetVersion, details.getPetVersion())
        .execute();
  }

  @Override
  public boolean updateVersion(PetUserPetIncomeDetails details) {
    return update()
        .set(PetUserPetIncomeDetails::getPetVersion, details.getPetVersion() + 1)
        .eq(PetUserPetIncomeDetails::getId, details.getId())
        .eq(PetUserPetIncomeDetails::getPetVersion, details.getPetVersion())
        .execute();
  }

  @Override
  public boolean existsNotCharged(Long userId) {
    return Optional.ofNullable(query()
            .select(PetUserPetIncomeDetails::getId)
            .eq(PetUserPetIncomeDetails::getUserId, userId)
            .eq(PetUserPetIncomeDetails::getReceive, Boolean.FALSE)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(detail -> Objects.nonNull(detail.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public boolean updateCasStealQuantity(Long id, BigDecimal stealQuantity,
      BigDecimal casStealQuantity) {
    return update()
        .set(PetUserPetIncomeDetails::getStealQuantity, stealQuantity)
        .eq(PetUserPetIncomeDetails::getStealQuantity, casStealQuantity)
        .eq(PetUserPetIncomeDetails::getId, id)
        .execute();
  }

  @Override
  public Map<Long, List<PetUserPetIncomeDetails>> mapStealableByUserId(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return CollectionUtils.newHashMap();
    }
    List<PetUserPetIncomeDetails> details = query()
        .gt(PetUserPetIncomeDetails::getStealQuantity, 0)
        .eq(PetUserPetIncomeDetails::getReceive, Boolean.FALSE)
        .le(PetUserPetIncomeDetails::getCanReceiveTime, TimestampUtils.now())
        .in(PetUserPetIncomeDetails::getUserId, userIds)
        .list();

    if (CollectionUtils.isEmpty(details)) {
      return CollectionUtils.newHashMap();
    }

    return details.stream().collect(Collectors.groupingBy(PetUserPetIncomeDetails::getUserId));
  }

}
