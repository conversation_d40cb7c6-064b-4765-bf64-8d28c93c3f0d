package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.UserInterviewDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserInterview;
import com.red.circle.other.infra.database.rds.service.user.user.UserInterviewService;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户访问列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-09
 */
@Service
public class UserInterviewServiceImpl extends
    BaseServiceImpl<UserInterviewDAO, UserInterview> implements UserInterviewService {

  @Override
  public UserInterview checkExist(Long userId, Long interviewId) {
    return query()
        .eq(UserInterview::getUserId, userId)
        .eq(UserInterview::getInterviewUserId, interviewId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public List<UserInterview> listByUserId(Long userId, Long lastId) {
    return query()
        .eq(UserInterview::getInterviewUserId, userId)
        .lt(Objects.nonNull(lastId), UserInterview::getId, lastId)
        .orderByDesc(UserInterview::getUpdateTime)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }

  @Override
  public List<UserInterview> pageByUserId(Long userId, Integer pageNumber) {

    if (Objects.isNull(pageNumber) || pageNumber < 1) {
      pageNumber = 1;
    }

    return query()
        .eq(UserInterview::getInterviewUserId, userId)
        .orderByDesc(UserInterview::getUpdateTime)
        .last(getPageSql(pageNumber))
        .list();

  }

  private String getPageSql(Integer pageNumber) {

    return String.format("LIMIT %s, %s", (pageNumber - 1) * PageConstant.DEFAULT_LIMIT_SIZE,
        PageConstant.DEFAULT_LIMIT_SIZE);
  }

}
