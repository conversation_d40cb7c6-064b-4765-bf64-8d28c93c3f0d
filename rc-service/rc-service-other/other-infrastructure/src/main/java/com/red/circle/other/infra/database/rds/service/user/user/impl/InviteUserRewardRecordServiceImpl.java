package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.InviteUserRewardRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteUserRewardRecord;
import com.red.circle.other.infra.database.rds.service.user.user.InviteUserRewardRecordService;
import com.red.circle.other.inner.model.cmd.user.UserInviteRewardQryCmd;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户邀请奖励记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2021/4/27
 */
@RequiredArgsConstructor
@Service
public class InviteUserRewardRecordServiceImpl extends
    BaseServiceImpl<InviteUserRewardRecordDAO, InviteUserRewardRecord> implements
    InviteUserRewardRecordService {

  @Override
  public boolean existsByInviteUserId(Long inviteUserId) {
    return Optional.ofNullable(query()
            .select(InviteUserRewardRecord::getId)
            .eq(InviteUserRewardRecord::getInviteUserId, inviteUserId)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(inviteUserRewardRecord -> Objects.nonNull(inviteUserRewardRecord.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public List<InviteUserRewardRecord> listInviteRewardRecord(Long userId, Long lastId) {
    return query()
        .eq(InviteUserRewardRecord::getUserId, userId)
        .lt(Objects.nonNull(lastId), InviteUserRewardRecord::getId, lastId)
        .last(PageConstant.DEFAULT_LIMIT)
        .orderByDesc(InviteUserRewardRecord::getId)
        .list();
  }

  @Override
  public InviteUserRewardRecord getByInviteUserId(Long userId) {
    return Optional.ofNullable(
        query()
            .eq(InviteUserRewardRecord::getInviteUserId, userId)
            .getOne()
    ).orElse(null);
  }


  @Override
  public PageResult<InviteUserRewardRecord> getUserInviteRewardRecord(
      UserInviteRewardQryCmd query) {
    PageResult<InviteUserRewardRecord> inviteRewardPage = query()
        .eq(Objects.nonNull(query.getUserId()),
            InviteUserRewardRecord::getUserId, query.getUserId())
        .ge(Objects.nonNull(query.getStartTime()),
            InviteUserRewardRecord::getCreateTime, query.startTimeToLocalDateTime())
        .le(Objects.nonNull(query.getEndTime()),
            InviteUserRewardRecord::getCreateTime, query.endTimeToLocalDateTime())
        .orderByDesc(InviteUserRewardRecord::getCreateTime)
        .page(query.getPageQuery());

    if (CollectionUtils.isEmpty(inviteRewardPage.getRecords())) {
      return inviteRewardPage;
    }
    return inviteRewardPage;
  }
}
