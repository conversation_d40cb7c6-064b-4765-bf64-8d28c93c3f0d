package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户领取专属礼物记录.
 * </p>
 *
 * <AUTHOR> on 2024-01-08 18:11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("user_custom_gift_record")
public class UserCustomGiftRecord extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 图片1.
   */
  @TableField("cover_one_url")
  private String coverOneUrl;

  /**
   * 图片2.
   */
  @TableField("cover_two_url")
  private String coverTwoUrl;

  /**
   * 图片3.
   */
  @TableField("cover_three_url")
  private String coverThreeUrl;

  /**
   * 音频资源url.
   */
  @TableField("song_source_url")
  private String songSourceUrl;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 0.待处理 1.已处理.
   */
  @TableField("approve_status")
  private Boolean approveStatus;

  /**
   * 0.未上传资源 1.已上传资源.
   */
  @TableField("upload_resource")
  private Boolean uploadResource;

  /**
   * 区域.
   */
  @TableField("regions")
  private String regions;

  /**
   * 领取年月(YYYYMM).
   */
  @TableField("recharge_date")
  private Integer rechargeDate;

  /**
   * 礼物Code.
   */
  @TableField("gift_code")
  private String giftCode;

  /**
   * 类型(专属制定、增加时长).
   */
  @TableField("custom_type")
  private String customType;

  /**
   * 数量(天数).
   */
  @TableField("quantity")
  private Integer quantity;

  /**
   * 领取需达到的充值金额.
   */
  @TableField("amount")
  private Integer amount;


}
