package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxAwardDetailsConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxAwardDetailsConfigQryCmd;
import java.util.List;


/**
 * <p>
 * 累计抽奖奖励配置详情 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxAwardDetailsConfigService extends
    BaseService<GameLuckyBoxAwardDetailsConfig> {

  List<GameLuckyBoxAwardDetailsConfig> listByAwardId(Long awardId);

  void deleteByAwardId(Long id);

  List<GameLuckyBoxAwardDetailsConfig> listGameLuckyBoxAwardDetailsConfig(
      GameLuckyBoxAwardDetailsConfigQryCmd query);
}
