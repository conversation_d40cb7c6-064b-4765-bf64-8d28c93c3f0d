package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * CP申请记录.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_cp_apply")
public class CpApply extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 发送申请用户.
   */
  @TableField("send_apply_user_id")
  private Long sendApplyUserId;

  /**
   * 接收申请用户.
   */
  @TableField("accept_apply_user_id")
  private Long acceptApplyUserId;

  /**
   * 申请消费金币.
   */
  @TableField("apply_consume_gold")
  private BigDecimal applyConsumeGold;

  /**
   * 申请文案.
   */
  @TableField("apply_text")
  private String applyText;

  /**
   * 状态.
   */
  @TableField("status")
  private String status;

}
