package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteRedPacketHelp;
import com.red.circle.other.inner.model.cmd.user.invite.InviteRedPacketHelpLogPageQryCmd;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 邀请新用户-助力记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
public interface InviteRedPacketHelpService extends BaseService<InviteRedPacketHelp> {

  List<InviteRedPacketHelp> pageHelp(Long lastId, Long inviteUserId, Long redPacketId);


  PageResult<InviteRedPacketHelp> pageHelp(InviteRedPacketHelpLogPageQryCmd cmd);

  Set<Long> getInviteUserIdList(Long userId);

}
