package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.RunningWaterUserPropsDAO;
import com.red.circle.other.infra.database.rds.entity.props.RunningWaterUserProps;
import com.red.circle.other.infra.database.rds.service.props.RunningWaterUserPropsService;
import com.red.circle.other.inner.model.cmd.material.RunningWaterUserPropsQryCmd;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 道具购买流水 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-22
 */
@Service
public class RunningWaterUserPropsServiceImpl extends
    BaseServiceImpl<RunningWaterUserPropsDAO, RunningWaterUserProps> implements
    RunningWaterUserPropsService {

  @Override
  public PageResult<RunningWaterUserProps> pageRunningWaterUserProps(
      RunningWaterUserPropsQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getId()),
            RunningWaterUserProps::getId, query.getId())
        .eq(Objects.nonNull(query.getBuyerId()),
            RunningWaterUserProps::getCreateUser, query.getBuyerId())
        .eq(Objects.nonNull(query.getReceiverId()),
            RunningWaterUserProps::getUserId, query.getReceiverId())
        .eq(Objects.nonNull(query.getPropsId()),
            RunningWaterUserProps::getPropsId, query.getPropsId())
        .ge(Objects.nonNull(query.startTimeToLocalDateTime()),
            RunningWaterUserProps::getCreateTime, query.startTimeToLocalDateTime())
        .le(Objects.nonNull(query.endTimeToLocalDateTime()),
            RunningWaterUserProps::getCreateTime, query.endTimeToLocalDateTime())
        .eq(StringUtils.isNotBlank(query.getOrigin()), RunningWaterUserProps::getOrigin,
            query.getOrigin())
        .orderByDesc(RunningWaterUserProps::getCreateTime)
        .page(query.getPageQuery());
  }
}
