package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户邀请表
 * <AUTHOR> on 2024/3/8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_user")
public class UserInviteUser  extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 标识.
   */
  @TableId(value = "id", type = IdType.INPUT)
  private Long id;

  /**
   * 来源平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 邀请我的用户ID
   */
  @TableField("invite_user_id")
  private Long inviteUserId;
}
