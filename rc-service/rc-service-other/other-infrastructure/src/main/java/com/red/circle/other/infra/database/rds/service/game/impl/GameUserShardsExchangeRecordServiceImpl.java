package com.red.circle.other.infra.database.rds.service.game.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameUserShardsExchangeRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameUserShardsExchangeRecord;
import com.red.circle.other.infra.database.rds.service.game.GameUserShardsExchangeRecordService;
import com.red.circle.other.inner.model.cmd.game.GameEggQryCmd;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户游戏抽奖碎片兑换记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
@Service
public class GameUserShardsExchangeRecordServiceImpl extends
    BaseServiceImpl<GameUserShardsExchangeRecordDAO, GameUserShardsExchangeRecord> implements
    GameUserShardsExchangeRecordService {

  @Override
  public PageResult<GameUserShardsExchangeRecord> pageRecord(GameEggQryCmd query) {

    PageResult<GameUserShardsExchangeRecord> iPage = query()
        .eq(Objects.nonNull(query.getUserId()),
            GameUserShardsExchangeRecord::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotEmpty(query.getSysOrigin()),
            GameUserShardsExchangeRecord::getSysOrigin,
            query.getSysOrigin())
        .orderByDesc(GameUserShardsExchangeRecord::getCreateTime)
        .page(query.getPageQuery());

    if (CollectionUtils.isEmpty(iPage.getRecords())) {
      return iPage;
    }

    return iPage;

  }
}
