package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyGiftRuleConfig;

/**
 * <p>
 * 幸运礼物规则配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-07-18 16:14
 */
public interface GameLuckyGiftRuleConfigService extends BaseService<GameLuckyGiftRuleConfig> {

  GameLuckyGiftRuleConfig getRuleConfig();

  GameLuckyGiftRuleConfig getLuckyGiftRuleConfig(String sysOrigin);
}
