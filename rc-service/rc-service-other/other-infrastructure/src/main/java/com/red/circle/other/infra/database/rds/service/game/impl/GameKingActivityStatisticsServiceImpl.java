package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameKingActivityStatisticsDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameKingActivityStatistics;
import com.red.circle.other.infra.database.rds.service.game.GameKingActivityStatisticsService;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 游戏王临时统计表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-25
 */
@Service
@RequiredArgsConstructor
public class GameKingActivityStatisticsServiceImpl extends
    BaseServiceImpl<GameKingActivityStatisticsDAO, GameKingActivityStatistics> implements
    GameKingActivityStatisticsService {

  private final GameKingActivityStatisticsDAO gameKingActivityStatisticsDAO;


  @Override
  public List<GameKingActivityStatistics> listUserByCondition(String sysOrigin, Integer promotion,
      Set<Long> topUserIds) {

    return Optional.ofNullable(
            gameKingActivityStatisticsDAO.listUserByCondition(sysOrigin, promotion, topUserIds))
        .orElse(Lists.newArrayList());
  }

  @Override
  public void deleteBySysOriginByUserIds(Set<Long> userIds, String sysOrigin) {
    gameKingActivityStatisticsDAO.deleteBySysOriginByUserIds(userIds, sysOrigin);
  }

  @Override
  public List<GameKingActivityStatistics> listOldFruitGameBySysOriginByUserIds(Set<Long> userIds,
      String sysOrigin) {

    return Optional.ofNullable(
            gameKingActivityStatisticsDAO.listOldFruitGameBySysOriginByUserIds(userIds, sysOrigin))
        .orElse(Lists.newArrayList());
  }

  @Override
  public List<GameKingActivityStatistics> listFruitGameBySysOriginByUserIds(Set<Long> userIds,
      String sysOrigin) {

    return Optional.ofNullable(
            gameKingActivityStatisticsDAO.listFruitGameBySysOriginByUserIds(userIds, sysOrigin))
        .orElse(Lists.newArrayList());
  }


}
