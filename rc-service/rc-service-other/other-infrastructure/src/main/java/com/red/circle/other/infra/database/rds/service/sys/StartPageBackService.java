package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.StartPageBack;
import com.red.circle.other.infra.database.rds.enums.StartPageType;

/**
 * <p>
 * 启动页背景图配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
public interface StartPageBackService extends BaseService<StartPageBack> {

  /**
   * 根据类型获取背景.
   *
   * @param sysOrigin     系统
   * @param startPageType 类型
   * @return backUrl
   */
  String getBackByType(SysOriginPlatformEnum sysOrigin, StartPageType startPageType);

}
