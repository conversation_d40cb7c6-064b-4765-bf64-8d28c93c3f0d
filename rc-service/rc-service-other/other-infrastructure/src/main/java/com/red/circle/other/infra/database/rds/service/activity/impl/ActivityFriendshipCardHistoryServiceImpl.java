package com.red.circle.other.infra.database.rds.service.activity.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.ActivityFriendshipCardHistoryDAO;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityFriendshipCardHistory;
import com.red.circle.other.infra.database.rds.service.activity.ActivityFriendshipCardHistoryService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户友谊关系卡 - 活动往日周历史记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-12
 */
@Service
public class ActivityFriendshipCardHistoryServiceImpl extends
    BaseServiceImpl<ActivityFriendshipCardHistoryDAO, ActivityFriendshipCardHistory> implements
    ActivityFriendshipCardHistoryService {

  @Override
  public List<ActivityFriendshipCardHistory> listBySysOrigin(String sysOrigin) {

    return Optional.ofNullable(query()
            .eq(ActivityFriendshipCardHistory::getSysOrigin, sysOrigin)
            .last(PageConstant.formatLimit(20))
            .orderByDesc(ActivityFriendshipCardHistory::getCreateTime)
            .list())
        .orElse(CollectionUtils.newArrayList());

  }
}
