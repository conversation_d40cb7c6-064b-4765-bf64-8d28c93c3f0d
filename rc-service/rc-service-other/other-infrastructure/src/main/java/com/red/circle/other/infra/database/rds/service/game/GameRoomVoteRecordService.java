package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomVoteRecord;

/**
 * <p>
 * 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
public interface GameRoomVoteRecordService extends BaseService<GameRoomVoteRecord> {

  /**
   * 是否已结束
   */
  Boolean isOver(Long roomId);

  /**
   * 是否已结束
   */
  Boolean isOver(Long roomId, Long id);

  /**
   * 根据房间id获得数据
   *
   * @param roomId 房间id
   * @return 信息
   */
  GameRoomVoteRecord getEfficientByRoomId(Long roomId);


}
