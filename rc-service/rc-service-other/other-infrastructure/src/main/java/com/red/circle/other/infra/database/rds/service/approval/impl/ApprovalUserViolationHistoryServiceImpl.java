package com.red.circle.other.infra.database.rds.service.approval.impl;


import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.common.business.core.enums.DataApprovalTypeEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.convertor.MybatisConvertor;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.approval.ApprovalUserViolationHistoryDAO;
import com.red.circle.other.infra.database.rds.entity.approval.ApprovalUserViolationHistory;
import com.red.circle.other.infra.database.rds.service.approval.ApprovalUserViolationHistoryService;
import com.red.circle.other.inner.model.cmd.approval.ApprovalPhotoWallApprovalTableQryCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalUserViolationHistoryQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalUserPhotoWallDTO;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 审批用户资料违规历史记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
@Service
public class ApprovalUserViolationHistoryServiceImpl extends
    BaseServiceImpl<ApprovalUserViolationHistoryDAO, ApprovalUserViolationHistory> implements
    ApprovalUserViolationHistoryService {

  @Override
  public String getLastNotPassAvatar(Long userId) {
    return Optional.ofNullable(
        query()
            .eq(ApprovalUserViolationHistory::getViolationType, DataApprovalTypeEnum.AVATAR)
            .eq(ApprovalUserViolationHistory::getUserId, userId)
            .eq(ApprovalUserViolationHistory::getApprovalResult, ApprovalStatusEnum.NOT_PASS)
            .last(PageConstant.LIMIT_ONE)
            .orderByDesc(ApprovalUserViolationHistory::getCreateTime)
            .getOne()
    ).map(ApprovalUserViolationHistory::getContent).orElse(null);
  }


  @Override
  public PageResult<ApprovalUserPhotoWallDTO> pageUserDetailsPhotoWall(
      ApprovalPhotoWallApprovalTableQryCmd query,
      String type) {
    return MybatisConvertor.toPageResult(
        baseDAO.pageUserDetailsPhotoWall(MybatisConvertor.toPage(query.getPageQuery()), query,
            type));
  }

  @Override
  public PageResult<ApprovalUserViolationHistory> pageHistory(
      ApprovalUserViolationHistoryQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getUserId()), ApprovalUserViolationHistory::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotBlank(query.getViolationType()),
            ApprovalUserViolationHistory::getViolationType, query.getViolationType())
        .ge(Objects.nonNull(query.getStartTime()), ApprovalUserViolationHistory::getCreateTime,
            query.getStartTime())
        .lt(Objects.nonNull(query.getEndTime()), ApprovalUserViolationHistory::getCreateTime,
            query.getEndTime())
        .orderByDesc(ApprovalUserViolationHistory::getCreateTime)
        .page(query.getPageQuery());
  }
}
