package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicTimeline;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 动态-时间轴表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface DynamicTimelineService extends BaseService<DynamicTimeline> {

  /**
   * 根据动态内容id删除时间轴数据
   */
  void deleteByDynamicContentId(Long dynamicContentId);

  /**
   * 根据动态内容id集合删除时间轴数据
   */
  void deleteByDynamicContentIds(Set<Long> dynamicIds);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   * @param createUser 用户id
   * @param isOwn      是否为自己发布的动态
   */
  List<DynamicTimeline> pageByTimeDesc(String region, Integer pageNumber, Long createUser,
      Boolean isOwn);
   List<DynamicTimeline> pageByTimeDesc(List<Long> ids, String region, Integer pageNumber,
                                              Boolean isOwn);

  /**
   * 获得自己发布过的动态
   *
   * @param userId 用户id
   * @return 动态时间轴
   */
  List<DynamicTimeline> ownListByUserId(Long userId);

}
