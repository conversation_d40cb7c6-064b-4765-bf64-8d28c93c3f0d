package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户不活跃清除日志.
 * </p>
 *
 * <AUTHOR> on 2023-09-06 18:08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_not_action_log")
public class NotActionLog extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 房间id.
   */
  @TableField("room_id")
  private Long roomId;

  /**
   * 类型:麦位用户/观众.
   */
  @TableField("user_type")
  private String userType;

  /**
   * 时长(分钟).
   */
  @TableField("duration")
  private String duration;

}
