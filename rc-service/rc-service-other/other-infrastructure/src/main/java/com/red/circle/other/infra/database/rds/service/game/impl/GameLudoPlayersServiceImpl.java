package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLudoPlayersDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLudoPlayers;
import com.red.circle.other.infra.database.rds.service.game.GameLudoPlayersService;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ludo游戏玩家 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Service
public class GameLudoPlayersServiceImpl extends
    BaseServiceImpl<GameLudoPlayersDAO, GameLudoPlayers> implements GameLudoPlayersService {

  @Override
  public List<GameLudoPlayers> listByGameIds(Set<Long> gameIds) {
    return query()
        .in(GameLudoPlayers::getGameId, gameIds)
        .list();
  }
}
