package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsSourceRecord;
import com.red.circle.other.infra.database.rds.entity.sys.Administrator;
import com.red.circle.other.inner.model.cmd.sys.AddSysAdministratorCmd;
import com.red.circle.other.inner.model.cmd.sys.AppUserDetailsQryCmd;
import com.red.circle.other.inner.model.dto.sys.SysAdministratorInfoDTO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * APP管理员表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
public interface AdministratorService extends BaseService<Administrator> {

  /**
   * 获取用户认证.
   *
   * @param userId 用户id
   * @return ignore
   */
  Administrator getByUserId(Long userId);

  /**
   * 是否是管理员.
   *
   * @param userId 验证用户是否是管理员
   * @return true 是，false 不是
   */
  boolean existsAdmin(Long userId);

  /**
   * 是否是超级管理员.
   *
   * @param userId 验证用户是否是管理员
   * @return true 是，false 不是
   */
  boolean existsSupperAdmin(Long userId);

  /**
   * 用户组是否存在可用管理员.
   *
   * @return true 可用，false 不可以
   */
  boolean existsAvailable(Set<Long> userIds);

  /**
   * 新增超级管理员
   */
  void addSysAdministrator(AddSysAdministratorCmd cmd);

  /**
   * 分页查询管理员详情
   */
  PageResult<SysAdministratorInfoDTO> pageUserDetails(
      AppUserDetailsQryCmd appUserDetailsQuery);

}
