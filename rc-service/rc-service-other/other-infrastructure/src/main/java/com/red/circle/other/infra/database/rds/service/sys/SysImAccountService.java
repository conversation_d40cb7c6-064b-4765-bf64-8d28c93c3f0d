package com.red.circle.other.infra.database.rds.service.sys;


import com.red.circle.common.business.dto.cmd.PageUserIdCmd;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.SysImAccount;

/**
 * <p>
 * im登录账号 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
public interface SysImAccountService extends BaseService<SysImAccount> {

  SysImAccount getSysImByUserId(Long userId);

  /**
   * 分页列表
   *
   * @param cmd ignore
   * @return ignore
   */
  PageResult<SysImAccount> pageImAccountVo(PageUserIdCmd cmd);

  /**
   * 删除im账号
   *
   * @param userId ignore
   */
  void deleteImAccount(Long userId);
}
