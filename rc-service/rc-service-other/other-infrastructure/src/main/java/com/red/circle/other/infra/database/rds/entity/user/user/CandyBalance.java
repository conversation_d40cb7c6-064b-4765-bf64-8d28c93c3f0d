package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户糖果余额
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_candy_balance")
@Deprecated
public class CandyBalance extends TimestampBaseEntity implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户id.
   */
  @TableId(value = "user_id", type = IdType.INPUT)
  private Long userId;

  /**
   * 获得糖果.
   */
  @TableField("earn_points")
  private BigDecimal earnPoints;

  /**
   * 消费糖果.
   */
  @TableField("consumption_points")
  private BigDecimal consumptionPoints;

}
