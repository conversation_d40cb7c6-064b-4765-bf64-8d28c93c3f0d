package com.red.circle.other.infra.database.rds.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * APP管理员权限关系表.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_administrator_auth")
public class AdministratorAuth extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 权限菜单ID.
   */
  @TableField("resource_id")
  private Long resourceId;


}
