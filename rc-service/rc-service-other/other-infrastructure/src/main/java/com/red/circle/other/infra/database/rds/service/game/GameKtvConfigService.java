package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameKtvConfig;
import com.red.circle.other.inner.model.cmd.game.GameKtvConfigQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * ktv歌曲配置信息.
 * </p>
 *
 * <AUTHOR> on 2023-05-05 17:18
 */
public interface GameKtvConfigService extends BaseService<GameKtvConfig> {

  /**
   * 获得数据.
   *
   * @param sysOrigin 系统
   * @param regionId  区域
   */
  List<GameKtvConfig> listByRegion(String sysOrigin, String regionId);

  /**
   * 获得歌曲.
   */
  List<GameKtvConfig> listByIds(Set<Long> ids);

  /**
   * 查询数据.
   */
  List<GameKtvConfig> listBySysOrigin(String sysOrigin);

  PageResult<GameKtvConfig> getSongInfo(GameKtvConfigQryCmd query);

  Map<Long, String> mapSongName(String sysOrigin);
}
