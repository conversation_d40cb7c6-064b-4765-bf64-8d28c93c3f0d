package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户靓号
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_beautiful_number")
public class UserBeautifulNumber extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 账号.
   */
  @TableField("user_account")
  private Long userAccount;

  /**
   * 类型-系统分类.
   */
  @TableField("type")
  private String type;

  /**
   * 二级类型.
   */
  @TableField("type_name")
  private String typeName;

  /**
   * tab分组.
   */
  @TableField("tab_name")
  private String tabName;

  /**
   * 价值(美元).
   */
  @TableField("worth")
  private BigDecimal worth;

  /**
   * 价值(金币).
   */
  @TableField("gold")
  private BigDecimal gold;

  /**
   * 0.不可售卖 1.可售卖  2.已出售.
   */
  @TableField("status")
  private Integer status;

  /**
   * 展示.
   */
  @TableField("sort")
  private Integer sort;


  /**
   * 是否可以售卖.
   */
  public boolean checkAvailableForSale() {
    return Objects.equals(status, 1);
  }

  /**
   * 折后节约价格.
   */
  public BigDecimal roomAccountPriceDiscount() {
    return gold.subtract(roomAccountPriceDiscounted());
  }

  /**
   * 折后价格.
   */
  public BigDecimal roomAccountPriceDiscounted() {
    return gold.multiply(BigDecimal.valueOf(1 - discountRatio()))
        .setScale(0, RoundingMode.DOWN);
  }

  /**
   * 优惠比率.
   */
  public double discountRatio() {
    return 0.7;
  }

  /**
   * 计算总价格.
   *
   * @param isRoomAccount true 计算同时购买房间号码的折扣
   */
  public BigDecimal totalPrice(boolean isRoomAccount) {
    return isRoomAccount
        ? gold.add(roomAccountPriceDiscounted())
        .setScale(0, RoundingMode.DOWN)
        : gold;
  }
}
