package com.red.circle.other.infra.database.rds.service.activity.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.ActivityFriendshipCardChampionDAO;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityFriendshipCardChampion;
import com.red.circle.other.infra.database.rds.service.activity.ActivityFriendshipCardChampionService;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户友谊关系卡 - 活动冠军记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
@Service
public class ActivityFriendshipCardChampionServiceImpl extends
    BaseServiceImpl<ActivityFriendshipCardChampionDAO, ActivityFriendshipCardChampion> implements
    ActivityFriendshipCardChampionService {

  @Override
  public void save(Long userId) {

    ActivityFriendshipCardChampion champion = query()
        .eq(ActivityFriendshipCardChampion::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(champion)) {
      save(new ActivityFriendshipCardChampion()
          .setUserId(userId)
          .setChampionFrequency(1)
      );
      return;
    }
    champion.setChampionFrequency(champion.getChampionFrequency() + 1);
    updateSelectiveById(champion);

  }

  @Override
  public Integer getChampionFrequencyByUserId(Long userId) {

    return Optional.ofNullable(query()
            .select(ActivityFriendshipCardChampion::getChampionFrequency)
            .eq(ActivityFriendshipCardChampion::getUserId, userId)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(ActivityFriendshipCardChampion::getChampionFrequency)
        .orElse(0);
  }
}
