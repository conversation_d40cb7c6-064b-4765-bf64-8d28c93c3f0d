package com.red.circle.other.infra.utils;

import com.red.circle.other.infra.database.cache.key.GameListKeys;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;

import java.util.HashSet;
import java.util.Set;

public class RedisUtils {
    public static Set<String> redisScan(RedisTemplate<String, Object> redisTemplate, String pattern) {
        ScanOptions.ScanOptionsBuilder optionsBuilder = ScanOptions.scanOptions().match(pattern + "*").count(10000);
        ScanOptions options = optionsBuilder.build();
        return redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keysTmp = new HashSet<>();
            try (Cursor<byte[]> cursor = connection.scan(options)) {

                while (cursor.hasNext()) {
                    keysTmp.add(new String(cursor.next(), "Utf-8"));
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            return keysTmp;
        });
    }
}
