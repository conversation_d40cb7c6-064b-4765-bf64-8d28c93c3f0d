package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameTurntableRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameTurntableRecord;
import com.red.circle.other.infra.database.rds.service.game.GameTurntableRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 转盘游戏记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-20
 */
@Service
public class GameTurntableRecordServiceImpl extends
    BaseServiceImpl<GameTurntableRecordDAO, GameTurntableRecord> implements
    GameTurntableRecordService {

  @Override
  public GameTurntableRecord getByGameId(Long gameId) {
    return query()
        .eq(GameTurntableRecord::getGameId, gameId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }
}
