package com.red.circle.other.infra.gateway.user;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.SVIPLevelEnum;
import com.red.circle.common.business.enums.SendPropsOrigin;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.domain.gateway.props.ActivitySourceGroupGateway;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.domain.gateway.user.ability.UserSVipGateway;
import com.red.circle.other.domain.model.user.UserProfile;
import com.red.circle.other.domain.model.user.ability.SysSVipLevelConf;
import com.red.circle.other.domain.model.user.ability.UserSVip;
import com.red.circle.other.infra.common.activity.PropsActivitySendCommon;
import com.red.circle.other.infra.common.activity.send.SendRewardGroup;
import com.red.circle.other.infra.common.props.UserBadgeCommon;
import com.red.circle.other.infra.database.cache.service.user.UserCacheService;
import com.red.circle.other.infra.database.rds.entity.user.user.SVipIdentity;
import com.red.circle.other.infra.database.rds.service.user.user.SVipIdentityService;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.other.inner.enums.material.PropsTypeEnum;
import com.red.circle.other.inner.model.dto.activity.props.ActivityPropsRule;
import com.red.circle.other.inner.model.dto.activity.props.ActivityResource;
import com.red.circle.other.inner.model.dto.activity.props.ActivityRewardProps;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.json.JacksonUtils;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

/**
 * 用户SVip 实现.
 *
 * <AUTHOR> on 2023/12/17
 */
@Component
@RequiredArgsConstructor
public class UserSVipGatewayImpl implements UserSVipGateway {

  private final UserBadgeCommon badgeClient;
  private final UserCacheService userCacheService;
  private final UserProfileGateway userProfileGateway;
  private final SVipIdentityService sVipIdentityService;
  private final PropsActivitySendCommon propsActivitySendCommon;
  private final ActivitySourceGroupGateway activitySourceGroupGateway;

  @Override
  public SVIPLevelEnum checkSVipIdentity(Long userId) {
    String val = userCacheService.getSVipIdentity(userId,
        (uid) -> {
          SVipIdentity sVipIdentity = sVipIdentityService.getByUserId(uid);
          return Objects.toString(getVipLevelEnum(sVipIdentity));
        });

    return StringUtils.isBlank(val) ? SVIPLevelEnum.NONE : SVIPLevelEnum.valueOf(val);
  }

  private SVIPLevelEnum getVipLevelEnum(SVipIdentity sVipIdentity) {
    if (Objects.isNull(sVipIdentity)) {
      return SVIPLevelEnum.NONE;
    }

    if (Objects.equals(sVipIdentity.getLevel(), SVIPLevelEnum.NONE.getLevel())) {
      return SVIPLevelEnum.NONE;
    }

    if (TimestampUtils.now().after(sVipIdentity.getExpiredTime())) {
      return SVIPLevelEnum.NONE;
    }

    return SVIPLevelEnum.getByLevel(sVipIdentity.getLevel());
  }

  @Override
  public Map<Long, SVIPLevelEnum> checkSVipIdentity(Set<Long> userIds) {
    List<SVipIdentity> sVipIdentityList = sVipIdentityService.listByUserIds(userIds);
    if (CollectionUtils.isEmpty(sVipIdentityList)) {
      return userIds.stream()
          .collect(Collectors.toMap(userId -> userId, none -> SVIPLevelEnum.NONE));
    }

    Map<Long, SVipIdentity> sVipIdentityMap = sVipIdentityList.stream()
        .collect(Collectors.toMap(SVipIdentity::getUserId, Function.identity(), (v1, v2) -> v2));
    return userIds.stream()
        .collect(Collectors.toMap(k -> k, userId -> getVipLevelEnum(sVipIdentityMap.get(userId))));
  }

  /**
   * 兑换.
   */
  public UserSVip exchange(Long userId) {

    SVipIdentity userVip = getSVipByUserId(userId);
    ResponseAssert.notNull(CommonErrorCode.CAN_NOT_RECEIVE, userVip);

    // 处理升级业务.
    processSVipUpgrade(userVip);

    // 获得最新SVip资料.
    return getUserSVipProfile(userId, userVip.getSysOrigin());
  }

  /**
   * 获得用户SVip资料.
   */
  @Override
  public UserSVip getUserSVipProfile(Long userId, String sysOrigin) {

    // 获得道具
    List<ActivityPropsRule> ruleConfigs = listRuleConfigs(sysOrigin);
    ResponseAssert.notEmpty(CommonErrorCode.CONFIGURATION_ERROR, ruleConfigs);

    // VIP已过期则初始化为没有VIP
    SVipIdentity userVip = sVipIdentityService.getByUserId(userId);
    if (Objects.nonNull(userVip) && TimestampUtils.now().after(userVip.getExpiredTime())) {
      userVip.setLevel(SVIPLevelEnum.NONE.getLevel());
    }

    // 当前用户积分
    long userCurrentIntegral = Objects.isNull(userVip) ? 0L : userVip.getIntegral();

    // 当前用户等级
    SVIPLevelEnum userCurrentLevel =
        Objects.isNull(userVip) ? SVIPLevelEnum.NONE : SVIPLevelEnum.getByLevel(userVip.getLevel());

    // VIP过期时间
    Timestamp expiredTime =
        Objects.isNull(userVip) ? TimestampUtils.now() : userVip.getExpiredTime();

    // 获得当前等级的下一级所需完整积分.
    Long nextCompleteIntegral = getNextCompleteIntegral(userVip, ruleConfigs);

    // 推测最大可升几级.
    ImmutablePair<SVIPLevelEnum, Long> speculate = speculateMaxUpLevel(userVip, ruleConfigs);

    return new UserSVip()
        .setUserId(userId)
        .setUserCurrentIntegral(userCurrentIntegral)
        .setUserCurrentLevel(userCurrentLevel.name())
        .setLevelIntegral(nextCompleteIntegral)
        .setMaxUpLevel(speculate.getLeft().name())
        .setUpLevelSurplusIntegral(speculate.getRight())
        .setExpiredTime(expiredTime);
  }

  /**
   * 推测最大可以升多少级,升完级后余多少积分.
   *
   * @param userVip     用户vip身份
   * @param ruleConfigs 道具
   * @return 最大等级/剩余积分
   */
  private ImmutablePair<SVIPLevelEnum, Long> speculateMaxUpLevel(SVipIdentity userVip,
      List<ActivityPropsRule> ruleConfigs) {

    // 无等级无积分则直接返回.
    if (Objects.isNull(userVip)) {
      return ImmutablePair.of(SVIPLevelEnum.NONE, 0L);
    }
    // 过期的等级将其判定为0级.
    if (TimestampUtils.now().after(userVip.getExpiredTime())) {
      userVip.setLevel(SVIPLevelEnum.NONE.getLevel());
    }
    // 默认返回当前等级与积分.
    AtomicReference<ImmutablePair<SVIPLevelEnum, Long>> result = new AtomicReference<>(ImmutablePair
        .of(SVIPLevelEnum.getByLevel(userVip.getLevel()), 0L));

    // 推测最大升级.
    ruleConfigs.forEach(ruleConfig -> {

      SysSVipLevelConf levelConfig = getSysSVipLevelConf(userVip, ruleConfig);
      if (Objects.isNull(levelConfig)) {
        return;
      }

      userVip.setLevel(levelConfig.getMark());
      userVip.setIntegral(userVip.getIntegral() - levelConfig.getQuantity());

      result.set(ImmutablePair
          .of(SVIPLevelEnum.getByLevel(userVip.getLevel()), userVip.getIntegral()));
    });

    return result.get();
  }

  private SysSVipLevelConf getSysSVipLevelConf(SVipIdentity userVip,
      ActivityPropsRule ruleConfig) {

    SysSVipLevelConf levelConfig = getLevelConfig(ruleConfig);

    // 用户等级大于系统等级则继续往后面拿.
    if (userVip.getLevel() > levelConfig.getMark()) {
      return null;
    }
    // 等级相同时，只有VIP5才能往下执行
    if (Objects.equals(levelConfig.getMark(), userVip.getLevel()) &&
        !Objects.equals(userVip.getLevel(), SVIPLevelEnum.SVIP_5.getLevel())) {
      return null;
    }
    if (levelConfig.getQuantity() > userVip.getIntegral()) {
      return null;
    }
    return levelConfig;
  }


  /**
   * SVip过期与升级处理.
   */
  private void processSVipUpgrade(SVipIdentity userVip) {

    // 获得道具
    List<ActivityPropsRule> ruleConfigs = listRuleConfigs(userVip.getSysOrigin());
    ResponseAssert.notEmpty(CommonErrorCode.CONFIGURATION_ERROR, ruleConfigs);

    // 如果当前等级已经过期则先初始化为没有等级.
    if (TimestampUtils.now().after(userVip.getExpiredTime())) {
      userVip.setLevel(SVIPLevelEnum.NONE.getLevel());
    }

    // 最后一次升级时的奖励规则
    AtomicReference<ActivityPropsRule> lastRuleConfig = new AtomicReference<>();

    // 升级处理.
    ruleConfigs.forEach(ruleConfig -> {

      // 系统获得等级.
      SysSVipLevelConf levelConfig = getSysSVipLevelConf(userVip, ruleConfig);
      if (Objects.isNull(levelConfig)) {
        return;
      }

      // 如果升级之前用户就是有效的VIP5那么则延长VIP5.
      if (Objects.equals(userVip.getLevel(), SVIPLevelEnum.SVIP_5.getLevel())) {
        userVip.setExpiredTime(TimestampUtils.expiredPlusDays(userVip.getExpiredTime(), 90));
      } else {
        userVip.setExpiredTime(TimestampUtils.nowPlusDays(90));
        userVip.setStartTime(TimestampUtils.now());
      }
      // 升级
      userVip.setLevel(levelConfig.getMark());
      userVip.setIntegral(userVip.getIntegral() - levelConfig.getQuantity());
      lastRuleConfig.set(ruleConfig);
    });

    if (Objects.isNull(lastRuleConfig.get())) {
      return;
    }
    sVipIdentityService.updateSelectiveById(userVip);

    // 清除旧SVip道具信息.
    removeSVipReward(userVip);

    // 发送新SVip道具.
    propsActivitySendCommon.sendActivityGroup(SendRewardGroup.builder()
        .trackId(lastRuleConfig.get().getId())
        .sysOrigin(SysOriginPlatformEnum.valueOf(userVip.getSysOrigin()))
        .acceptUserId(userVip.getUserId())
        .resourceGroupId(lastRuleConfig.get().getResourceGroupId())
        .origin(SendPropsOrigin.SVIP_REWARD)
        .build());
  }

  private void removeSVipReward(SVipIdentity vipIdentity) {

    List<ActivityResource> activityResources = getActivityResources(vipIdentity);
    if (CollectionUtils.isEmpty(activityResources)) {
      return;
    }

    activityResources.forEach(resource -> {

      List<ActivityRewardProps> propsList = resource.getPropsGroupActivityRewardProps();
      if (CollectionUtils.isEmpty(propsList)) {
        return;
      }
      badgeClient.reduceDaysAndUnUse(vipIdentity.getUserId(), propsList.stream()
          .filter(props -> Objects.equals(props.getType(), PropsTypeEnum.PROPS.name()))
          .map(props -> Long.parseLong(props.getContent()))
          .collect(Collectors.toSet()), 90);
      badgeClient.reduceDaysAndUnUse(vipIdentity.getUserId(), propsList.stream()
          .filter(props -> Objects.equals(props.getType(), PropsTypeEnum.BADGE.name()))
          .map(props -> Long.parseLong(props.getContent()))
          .collect(Collectors.toSet()), 90);
    });
  }

  private List<ActivityResource> getActivityResources(SVipIdentity vipIdentity) {
    return activitySourceGroupGateway.listActivityResource(
        SysOriginPlatformEnum.valueOf(vipIdentity.getSysOrigin()),
        PropsActivityTypeEnum.SVIP_REWARD);
  }


  // 获得SVip奖励道具.
  private List<ActivityPropsRule> listRuleConfigs(String sysOrigin) {
    return activitySourceGroupGateway.listRule(SysOriginPlatformEnum.valueOf(sysOrigin),
        PropsActivityTypeEnum.SVIP_REWARD);
  }


  /**
   * 获得用户SVip.
   */
  private SVipIdentity getSVipByUserId(Long userId) {
    return sVipIdentityService.getByUserId(userId);
  }

  @Override
  public void incrIntegral(Long userId, Long integral) {

    UserProfile userProfile = userProfileGateway.getByUserId(userId);
    if (Objects.isNull(userProfile)) {
      return;
    }

    SVipIdentity sVipIdentity = getSVipByUserId(userProfile.getId());
    if (Objects.isNull(sVipIdentity)) {

      sVipIdentityService.save(new SVipIdentity()
          .setSysOrigin(userProfile.getOriginSys())
          .setUserId(userProfile.getId())
          .setLevel(SVIPLevelEnum.NONE.getLevel())
          .setIntegral(integral)
          .setStartTime(TimestampUtils.now())
          .setExpiredTime(TimestampUtils.nowPlusDays(90))
      );
      return;
    }

    sVipIdentityService.updateIntegral(userProfile.getId(), integral + sVipIdentity.getIntegral());
  }

  /**
   * 根据当前等级获得下一级完整积分.
   *
   * @param userVip     用户当前等级.
   * @param ruleConfigs 资源配置.
   * @return 所需完整积分.
   */
  private Long getNextCompleteIntegral(SVipIdentity userVip,
      List<ActivityPropsRule> ruleConfigs) {

    Integer nextLevel = SVIPLevelEnum.SVIP_1.getLevel();

    // 不是VIP5最大等级则 +1
    if (Objects.nonNull(userVip)) {
      if (!Objects.equals(userVip.getLevel(), SVIPLevelEnum.SVIP_5.getLevel())) {
        nextLevel = userVip.getLevel() + 1;
      } else {
        nextLevel = 5;
      }
    }

    for (ActivityPropsRule ruleConfig : ruleConfigs) {

      SysSVipLevelConf levelConfig = getLevelConfig(ruleConfig);

      if (Objects.equals(nextLevel, levelConfig.getMark())) {
        return levelConfig.getQuantity();
      }
    }
    return null;
  }

  private SysSVipLevelConf getLevelConfig(ActivityPropsRule ruleConfig) {
    SysSVipLevelConf sVipLevelConfigCO = JacksonUtils
        .readValue(ruleConfig.getJsonData(), SysSVipLevelConf.class);
    ResponseAssert.notNull(CommonErrorCode.CONFIGURATION_ERROR, sVipLevelConfigCO);
    return sVipLevelConfigCO;
  }

  @Override
  public void decrIntegral(Long userId, Long integral) {

    UserProfile userProfile = userProfileGateway.getByUserId(userId);
    if (Objects.isNull(userProfile)) {
      return;
    }

    SVipIdentity sVipIdentity = getSVipByUserId(userProfile.getId());
    if (Objects.isNull(sVipIdentity)) {
      return;
    }

    if (sVipIdentity.getIntegral() > integral) {

      sVipIdentityService.updateIntegral(userId, sVipIdentity.getIntegral() - integral);
      return;
    }

    sVipIdentityService.updateIntegral(userId, 0L);
  }

  @Override
  public Boolean existSVipProps(String sysOrigin, Long propsId) {

    if (Objects.isNull(propsId) || StringUtils.isBlank(sysOrigin)) {
      return Boolean.FALSE;
    }

    List<ActivityResource> activityResources = activitySourceGroupGateway
        .listActivityResource(SysOriginPlatformEnum.valueOf(sysOrigin),
            PropsActivityTypeEnum.SVIP_REWARD);
    if (CollectionUtils.isEmpty(activityResources)) {
      return Boolean.FALSE;
    }

    for (ActivityResource resource : activityResources) {

      List<ActivityRewardProps> propsList = resource.getPropsGroupActivityRewardProps();
      if (CollectionUtils.isEmpty(propsList)) {
        continue;
      }

      if (propsList.stream().anyMatch(props ->
          Objects.equals(props.getType(), PropsTypeEnum.PROPS.name()) &&
              Objects.equals(props.getContent(), Objects.toString(propsId)))) {
        return Boolean.TRUE;
      }
    }

    return Boolean.FALSE;
  }

}
