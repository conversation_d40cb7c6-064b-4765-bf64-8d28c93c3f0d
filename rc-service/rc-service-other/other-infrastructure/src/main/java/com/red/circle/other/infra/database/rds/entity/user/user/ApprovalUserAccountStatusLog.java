package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 审批账户历史记录
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("approval_user_account_status_log")
public class ApprovalUserAccountStatusLog implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 被审批用户
   */
  @TableField("be_approval_user")
  private Long beApprovalUser;

  /**
   * 操作类型
   */
  @TableField("status")
  private String status;

  /**
   * 备注
   */
  @TableField("description")
  private String description;

  /**
   * 审批来源:1.App 0.后台
   */
  @TableField("origin_type")
  private Boolean originType;

  /**
   * 创建时间
   */
  @TableField("create_time")
  private Timestamp createTime;

  /**
   * 创建人
   */
  @TableField("create_user")
  private Long createUser;


}
