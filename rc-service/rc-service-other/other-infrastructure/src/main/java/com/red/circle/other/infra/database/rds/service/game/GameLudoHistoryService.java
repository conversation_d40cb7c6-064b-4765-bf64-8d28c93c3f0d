package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLudoHistory;
import java.util.List;

/**
 * <p>
 * Ludo游戏记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
public interface GameLudoHistoryService extends BaseService<GameLudoHistory> {
  /**
   * 获取游戏历史记录信息.
   */
  List<GameLudoHistory> flowSelective(String sysOrigin, Long roomId, Long lastId);


}
