package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsPetStoreDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsPetStore;
import com.red.circle.other.infra.database.rds.service.props.PropsPetStoreService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 宠物道具商店 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PropsPetStoreServiceImpl extends
    BaseServiceImpl<PropsPetStoreDAO, PropsPetStore> implements PropsPetStoreService {

}
