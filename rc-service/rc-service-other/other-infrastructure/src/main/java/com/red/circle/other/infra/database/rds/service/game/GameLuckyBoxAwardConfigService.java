package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxAwardConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckBoxAwardConfigQryCmd;
import java.util.List;

/**
 * <p>
 * 累计抽奖奖励配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxAwardConfigService extends BaseService<GameLuckyBoxAwardConfig> {

  List<GameLuckyBoxAwardConfig> listBySysOrigin(String sysOriginName);

  PageResult<GameLuckyBoxAwardConfig> pageAwardConfig(
      GameLuckBoxAwardConfigQryCmd query);
}
