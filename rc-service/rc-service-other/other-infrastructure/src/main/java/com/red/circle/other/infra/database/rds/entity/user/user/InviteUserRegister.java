package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户邀请用户注册信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-12
 */
@Data
@Accessors(chain = true)
@TableName("user_invite_user_register")
@EqualsAndHashCode(callSuper = false)
public class InviteUserRegister extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;
  /**
   * 标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 被邀请用户id.
   */
  @TableField("invite_user_id")
  private Long inviteUserId;

  /**
   * 被邀请设备ID
   */
  @TableField("device_id")
  private String deviceId;

  /**
   * 佣金
   */
  @TableField("commission")
  private BigDecimal commission;

  public InviteUserRegister withCreateUser(Long createUser) {
    this.setCreateUser(createUser);
    return this;
  }

}
