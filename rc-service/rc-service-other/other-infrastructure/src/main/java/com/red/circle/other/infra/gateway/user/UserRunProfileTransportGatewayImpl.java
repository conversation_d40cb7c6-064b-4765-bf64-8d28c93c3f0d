package com.red.circle.other.infra.gateway.user;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.red.circle.other.domain.gateway.user.UserRunProfileTransportGateway;
import com.red.circle.other.domain.model.user.UserProfile;
import com.red.circle.other.infra.common.props.UserBadgeCommon;
import com.red.circle.other.infra.convertor.material.BadgeInfraConvertor;
import com.red.circle.other.infra.convertor.material.PropsResourcesInfraConvertor;
import com.red.circle.other.infra.convertor.room.RoomThemeUserInfraConvertor;
import com.red.circle.other.infra.convertor.user.PropsUserInfraConvertor;
import com.red.circle.other.infra.convertor.user.UserProfileInfraConvertor;
import com.red.circle.other.infra.database.mongo.entity.user.profile.UserRunProfile;
import com.red.circle.other.infra.database.mongo.entity.user.profile.UserSpecialId;
import com.red.circle.other.infra.database.mongo.service.user.profile.UserRunProfileService;
import com.red.circle.other.infra.database.mongo.service.user.profile.UserSpecialIdService;
import com.red.circle.other.infra.database.rds.entity.user.user.BaseInfo;
import com.red.circle.other.infra.database.rds.service.badge.BadgeConfigService;
import com.red.circle.other.infra.database.rds.service.badge.BadgePictureConfigService;
import com.red.circle.other.infra.database.rds.service.props.PropsBackpackService;
import com.red.circle.other.infra.database.rds.service.props.PropsSourceRecordService;
import com.red.circle.other.infra.database.rds.service.props.RoomThemeUserBackpackService;
import com.red.circle.other.infra.database.rds.service.props.RoomThemeUserCustomizeService;
import com.red.circle.other.infra.database.rds.service.user.user.BaseInfoService;
import com.red.circle.other.inner.enums.material.BadgeBackpackExpireType;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import com.red.circle.other.inner.model.dto.material.BadgeBackpackDTO;
import com.red.circle.other.inner.model.dto.material.BadgeConfigDTO;
import com.red.circle.other.inner.model.dto.material.BadgePictureConfigDTO;
import com.red.circle.other.inner.model.dto.material.PropsBackpackDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesDTO;
import com.red.circle.other.inner.model.dto.material.RoomThemeUserBackpackDTO;
import com.red.circle.other.inner.model.dto.material.RoomThemeUserCustomizeDTO;
import com.red.circle.other.inner.model.dto.material.UseBadgeDTO;
import com.red.circle.other.inner.model.dto.material.UsePropsDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.json.JacksonUtils;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 获取用户运行时数据缓存.
 *
 * <AUTHOR> on 2022/2/19
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserRunProfileTransportGatewayImpl implements UserRunProfileTransportGateway {

  private final BaseInfoService baseInfoService;
  private final UserBadgeCommon userBadgeCommon;
  private final BadgeConfigService badgeConfigService;
  private final BadgeInfraConvertor badgeInfraConvertor;
  private final PropsBackpackService propsBackpackService;
  private final UserSpecialIdService userSpecialIdService;
  private final UserRunProfileService userRunProfileService;
  private final PropsUserInfraConvertor propsUserInfraConvertor;
  private final PropsSourceRecordService propsSourceRecordService;
  private final BadgePictureConfigService badgePictureConfigService;
  private final UserProfileInfraConvertor userProfileInfraConvertor;
  private final RoomThemeUserInfraConvertor roomThemeUserInfraConvertor;
  private final RoomThemeUserBackpackService roomThemeUserBackpackService;
  private final PropsResourcesInfraConvertor propsResourcesInfraConvertor;
  private final RoomThemeUserCustomizeService roomThemeUserCustomizeService;

  @Override
  public UserProfile getByUserId(Long userId) {
    UserRunProfile userRunProfile = userRunProfileService.getById(userId);
    if (Objects.nonNull(userRunProfile)) {
      // 拥有靓号
      UserSpecialId userSpecialId = userSpecialIdService.getByUserId(userId);
      if (Objects.nonNull(userSpecialId)) {
        userRunProfile.setOwnSpecialId(userSpecialId);
      }
      return userProfileInfraConvertor.toUserProfile(userRunProfile);
    }
    return getFirstUserRunProfile(listByUserIds(Collections.singleton(userId)));
  }

  @Override
  public UserProfile getByAccount(String account) {
    UserRunProfile userRunProfile = userRunProfileService.getByAccount(account);
    if (Objects.nonNull(userRunProfile)) {
      return userProfileInfraConvertor.toUserProfile(userRunProfile);
    }

    BaseInfo baseInfo = baseInfoService.getByAccount(account);
    if (Objects.isNull(baseInfo)) {
      return null;
    }
    return getFirstUserRunProfile(
        assembleUserRunProfiles(Collections.singletonList(baseInfo)));
  }

  @Override
  public UserProfile getByAccount(String account, String sysOrigin) {
    BaseInfo baseInfo = baseInfoService.getByAccount(account,sysOrigin);
    if (Objects.isNull(baseInfo)) {
      return null;
    }
    return getFirstUserRunProfile(
            assembleUserRunProfiles(Collections.singletonList(baseInfo)));
  }

  @Override
  public List<UserProfile> listByAccounts(Set<String> accounts) {

    if (CollectionUtils.isEmpty(accounts)) {
      return CollectionUtils.newArrayList();
    }

    List<UserRunProfile> userRunProfiles = userRunProfileService.listByAccount(accounts);

    if (CollectionUtils.isNotEmpty(userRunProfiles)) {

      if (Objects.equals(accounts.size(), userRunProfiles.size())) {
        return userProfileInfraConvertor.toListUserProfile(userRunProfiles);
      }

      List<String> existsAccounts = userRunProfiles.stream().map(UserRunProfile::getAccount)
          .toList();

      userRunProfiles.addAll(
          assembleUserRunProfiles(baseInfoService.listByAccount(
                  accounts.stream().filter(account -> !existsAccounts.contains(account))
                      .toList()
              )
          )
      );
    }

    return userProfileInfraConvertor.toListUserProfile(
        assembleUserRunProfiles(baseInfoService.listByAccount(accounts))
    );
  }

  private UserProfile getFirstUserRunProfile(List<UserRunProfile> userRunProfiles) {
    if (CollectionUtils.isEmpty(userRunProfiles)) {
      return null;
    }
    return userProfileInfraConvertor.toUserProfile(userRunProfiles.get(0));
  }

  @Override
  public List<UserProfile> listUserProfile(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Lists.newArrayList();
    }

    List<UserRunProfile> userRunProfiles = userRunProfileService.listByIds(userIds);
    if (CollectionUtils.isNotEmpty(userRunProfiles)) {
      if (Objects.equals(userRunProfiles.size(), userIds.size())) {
        return userProfileInfraConvertor.toListUserProfile(userRunProfiles);
      }
      userIds.removeAll(
          userRunProfiles.stream().map(UserRunProfile::getId).collect(Collectors.toSet()));
    }

    List<UserRunProfile> profiles = listByUserIds(userIds);

    if (CollectionUtils.isEmpty(profiles)) {
      return userProfileInfraConvertor.toListUserProfile(userRunProfiles);
    }

    userRunProfiles.addAll(profiles);
    return userProfileInfraConvertor.toListUserProfile(userRunProfiles);
  }

  @Override
  public UserProfile updateUserRunProfile(Long id, String accountStatus) {
    UserRunProfile userRunProfiles = userRunProfileService.getById(id);
    if (Objects.nonNull(userRunProfiles)) {
      userRunProfiles.setAccountStatus(accountStatus);
      userRunProfiles.setUpdateTime(TimestampUtils.now());
      userRunProfileService.updateSelectiveById(userRunProfiles);
      return userProfileInfraConvertor.toUserProfile(userRunProfiles);
    }
    return null;
  }

  private List<UserRunProfile> listByUserIds(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Lists.newArrayList();
    }
    return assembleUserRunProfiles(baseInfoService.listByUserId(userIds));
  }

  private List<UserRunProfile> assembleUserRunProfiles(List<BaseInfo> baseInfos) {

    if (CollectionUtils.isEmpty(baseInfos)) {
      return Lists.newArrayList();
    }

    Set<Long> userIds = baseInfos.stream().map(BaseInfo::getId).collect(Collectors.toSet());

    // 个性签名
    // Map<Long, Expand> expandMap = expandService.mapExpand(userIds);

    // 拥有靓号
    Map<Long, UserSpecialId> userSpecialIdMap = userSpecialIdService.mapByUserIds(userIds);

    // 佩戴道具
    Map<Long, List<UsePropsDTO>> userUsePropsEntityMap = mapUserUsePropsEntity(userIds);
    // 主题信息
    Map<Long, UsePropsDTO> userUseRoomTheme = mapUserUseRoomTheme(userIds);

    // 根据平台分组： 徽章资源来自多个不同平台
    Map<String, List<BaseInfo>> sysOriginGroupUsers = baseInfos.stream()
        .collect(Collectors.groupingBy(BaseInfo::getOriginSys));

    List<UserRunProfile> userRunProfiles = sysOriginGroupUsers.entrySet().stream().map(entry -> {
          List<BaseInfo> userBaseInfos = entry.getValue();
          if (CollectionUtils.isEmpty(userBaseInfos)) {
            return Lists.<UserRunProfile>newArrayList();
          }
          // 佩戴徽章
          Map<Long, List<UseBadgeDTO>> userUseBadgeEntities = mapUserUseBadgeEntity(
              entry.getKey(),
              userBaseInfos.stream().map(BaseInfo::getId).collect(Collectors.toSet()));

          return userBaseInfos.stream().map(userBaseInfo -> {
                UserRunProfile newUserRunProfile = userProfileInfraConvertor.toUserRunProfile(
                    userBaseInfo);
                newUserRunProfile.addWearBadge(userUseBadgeEntities.get(userBaseInfo.getId()));
                newUserRunProfile.addUseProps(userUsePropsEntityMap.get(userBaseInfo.getId()));
                newUserRunProfile.addUseProps(userUseRoomTheme.get(userBaseInfo.getId()));
                newUserRunProfile.setOwnSpecialId(userSpecialIdMap.get(userBaseInfo.getId()));
                return newUserRunProfile;
              }
          ).collect(Collectors.toList());

        })
        .flatMap(Collection::stream)
        .collect(Collectors.toList());

    if (CollectionUtils.isNotEmpty(userRunProfiles)) {
      try {
        userRunProfileService.upsert(userRunProfiles);
      } catch (Exception ex) {
        // ignore
      }
    }
    return userRunProfiles;
  }

  private Map<Long, List<UseBadgeDTO>> mapUserUseBadgeEntity(String sysOrigin,
      Set<Long> userIds) {
    return Optional.ofNullable(this.listUserUseBadgeEntity(sysOrigin, userIds))
        .map(userUseBadgeEntities -> userUseBadgeEntities.stream()
            .collect(Collectors.groupingBy(UseBadgeDTO::getUserId)))
        .orElseGet(Maps::newHashMap);
  }

  private List<UseBadgeDTO> listUserUseBadgeEntity(String sysOrigin, Set<Long> userIds) {
    List<BadgeBackpackDTO> badgeBackpacks = userBadgeCommon.listUseNotExpireTimeByUserIds(userIds);
    if (CollectionUtils.isEmpty(badgeBackpacks)) {
      return Lists.newArrayList();
    }

    Set<Long> badgeIds = badgeBackpacks.stream().map(BadgeBackpackDTO::getBadgeId)
        .collect(Collectors.toSet());

    Map<Long, BadgeConfigDTO> badgeConfigMap = badgeInfraConvertor.tpMapBadgeConfigDTO(
        badgeConfigService.mapByIds(badgeIds)
    );

    Map<Long, BadgePictureConfigDTO> badgePictureConfigMap = badgeInfraConvertor.toMapBadgePictureConfigDTO(
        badgePictureConfigService.mapBadge(sysOrigin,
            badgeConfigMap.values().stream().map(BadgeConfigDTO::getId).collect(Collectors.toSet())
        )
    );

    return badgeBackpacks.stream().map(badgeBackpack -> {
          BadgeConfigDTO badgeConfig = badgeConfigMap.get(badgeBackpack.getBadgeId());
          if (Objects.isNull(badgeConfig)) {
            return null;
          }
          BadgePictureConfigDTO badgePictureConfig = badgePictureConfigMap.get(
              badgeBackpack.getBadgeId());
          if (Objects.isNull(badgePictureConfig)) {
            return null;
          }
          UseBadgeDTO entity = propsUserInfraConvertor.toUseBadgeDTO(badgeConfig);
          entity.setUserId(badgeBackpack.getUserId());
          entity.setSelectUrl(badgePictureConfig.getSelectUrl());
          entity.setNotSelectUrl(badgePictureConfig.getNotSelectUrl());
          entity.setAnimationUrl(badgePictureConfig.getAnimationUrl());
          entity.setExpireTime(
              Objects.equals(badgeBackpack.getExpireType(), BadgeBackpackExpireType.PERMANENT.name())
                  ? TimestampUtils.nowPlusYear(10).getTime()
                  : badgeBackpack.getExpireTime().getTime());
          return entity;
        })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Map<Long, List<UsePropsDTO>> mapUserUsePropsEntity(Set<Long> userIds) {
    return Optional.ofNullable(this.listUserUsePropsEntity(userIds))
        .map(userUsePropsEntities -> userUsePropsEntities.stream()
            .collect(Collectors.groupingBy(UsePropsDTO::getUserId)))
        .orElseGet(Maps::newHashMap);
  }

  private List<UsePropsDTO> listUserUsePropsEntity(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Lists.newArrayList();
    }

    List<PropsBackpackDTO> propsBackpacks = propsUserInfraConvertor.toListPropsBackpackDTO(
        propsBackpackService.listNotExpiredUseProps(userIds)
    );

    if (CollectionUtils.isEmpty(propsBackpacks)) {
      return Lists.newArrayList();
    }

    Map<Long, PropsResourcesDTO> propsSourceRecordMap = propsResourcesInfraConvertor.toMapPropsResourcesDTO(
        propsSourceRecordService.mapByIds(propsBackpacks.stream()
            .filter(propsBackpack -> !PropsCommodityType.SPECIAL_ID.eq(propsBackpack.getType()))
            .map(PropsBackpackDTO::getPropsId).collect(Collectors.toSet()))
    );

    return propsBackpacks.stream()
        .map(propsBackpack -> mergeUserUsePropsEntity(propsSourceRecordMap, propsBackpack))
        .filter(Objects::nonNull).toList();
  }

  private UsePropsDTO mergeUserUsePropsEntity(
      Map<Long, PropsResourcesDTO> propsSourceRecordMap, PropsBackpackDTO propsBackpack) {
    UsePropsDTO entity = new UsePropsDTO();
    PropsResourcesDTO propsSourceRecord = propsSourceRecordMap.get(propsBackpack.getPropsId());
    if (Objects.isNull(propsSourceRecord)) {
      log.warn("【道具】listUserUseProps数据错误：{}", JacksonUtils.toJson(propsBackpack));
      return null;
    }
    entity.setUserId(propsBackpack.getUserId());
    entity.setPropsResources(propsSourceRecord);
    entity.setExpireTime(propsBackpack.getExpireTime().getTime());
    return entity;
  }

  private Map<Long, UsePropsDTO> mapUserUseRoomTheme(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Maps.newHashMap();
    }

    List<RoomThemeUserBackpackDTO> backpacks = roomThemeUserInfraConvertor.toListRoomThemeUserBackpackDTO(
        roomThemeUserBackpackService.listByUserUseTheme(userIds)
    );

    if (CollectionUtils.isEmpty(backpacks)) {
      return Maps.newHashMap();
    }

    Map<Long, PropsResourcesDTO> storeMap = propsResourcesInfraConvertor.toMapPropsResourcesDTO(
        propsSourceRecordService.mapByIds(
            backpacks.stream()
                .filter(backpack -> Objects.equals(backpack.getThemeType(), 0)
                    && backpack.getExpireTime() > TimestampUtils.now().getTime())
                .map(RoomThemeUserBackpackDTO::getThemeId)
                .collect(Collectors.toSet()))
    );

    Map<Long, RoomThemeUserCustomizeDTO> customizeMap = roomThemeUserInfraConvertor.toMapRoomThemeUserUserCustomizeDTO(
        roomThemeUserCustomizeService.mapByIds(
            backpacks.stream()
                .filter(backpack -> Objects.equals(backpack.getThemeType(), 1))
                .map(RoomThemeUserBackpackDTO::getThemeId)
                .collect(Collectors.toSet()))
    );

    return backpacks.stream().map(backpack -> {
          if (Objects.equals(backpack.getThemeType(), 0)) {
            PropsResourcesDTO entity = storeMap.get(backpack.getThemeId());
            if (Objects.isNull(entity)) {
              log.warn("【数据异常】找不到资源信息，主题背景.{}", JacksonUtils.toJson(backpack));
              return null;
            }
            return new UsePropsDTO()
                .setUserId(backpack.getUserId())
                .setPropsResources(entity)
                .setExpireTime(backpack.getExpireTime());
          }

          RoomThemeUserCustomizeDTO customize = customizeMap.get(backpack.getThemeId());
          if (Objects.isNull(customize)) {
            log.error("【数据异常】找不到资源信息，自定义主题背景.{}", JacksonUtils.toJson(backpack));
            return null;
          }
          return new UsePropsDTO()
              .setUserId(backpack.getUserId())
              .setPropsResources(new PropsResourcesDTO()
                  .setId(backpack.getId())
                  .setType(PropsCommodityType.THEME.name())
                  .setCode("CUSTOMIZE")
                  .setName("User Customize Theme")
                  .setCover(customize.getThemeBack())
                  .setSourceUrl(null)
                  .setAmount(customize.getThemeMoney())
              )
              .setExpireTime(backpack.getExpireTime());
        }).filter(Objects::nonNull)
        .collect(
            Collectors.toMap(UsePropsDTO::getUserId, Function.identity(), (v0, v1) -> v1));

  }

}
