package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.OnlineTime;

/**
 * <p>
 * 用户在线时长 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-02
 */
public interface OnlineTimeService extends BaseService<OnlineTime> {

  /**
   * 累加用户在线时长1分钟
   *
   * @param userId    用户id
   * @param sysOrigin 系统来源
   */
  void inrOnlineOneMinute(Long userId, String sysOrigin);

}
