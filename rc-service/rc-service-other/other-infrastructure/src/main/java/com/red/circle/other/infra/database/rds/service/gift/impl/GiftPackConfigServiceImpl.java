package com.red.circle.other.infra.database.rds.service.gift.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.gift.GiftPackConfigDAO;
import com.red.circle.other.infra.database.rds.entity.gift.GiftPackConfig;
import com.red.circle.other.infra.database.rds.service.gift.GiftPackConfigService;
import com.red.circle.other.inner.model.cmd.sys.GiftPackConfigQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 礼包配置 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-15
 */
@Service
public class GiftPackConfigServiceImpl extends
    BaseServiceImpl<GiftPackConfigDAO, GiftPackConfig> implements GiftPackConfigService {

  @Override
  public Map<Long, List<GiftPackConfig>> mapGroupByGiftPackIds(Set<Long> giftPackIds) {
    return Optional.ofNullable(query().in(GiftPackConfig::getGiftPackId, giftPackIds).list())
        .map(giftPackConfigs -> giftPackConfigs.stream()
            .collect(Collectors.groupingBy(GiftPackConfig::getGiftPackId)))
        .orElse(Maps.newHashMap());
  }

  @Override
  public Map<Long, List<GiftPackConfig>> mapGiftPackIds(Set<Long> giftPackIds) {
    if (CollectionUtils.isEmpty(giftPackIds)) {
      return Maps.newHashMap();
    }
    return Optional.ofNullable(query().in(GiftPackConfig::getGiftPackId, giftPackIds).list())
        .map(giftPackConfigs -> giftPackConfigs.stream()
            .collect(Collectors.groupingBy(GiftPackConfig::getGiftPackId)))
        .orElse(Maps.newHashMap());
  }

  @Override
  public List<GiftPackConfig> listByGiftPackId(Long giftPackId) {
    return query().eq(GiftPackConfig::getGiftPackId, giftPackId).list();
  }

  @Override
  public GiftPackConfig getGoldByPackId(Long giftPackId) {
    return query().eq(GiftPackConfig::getGiftPackId, giftPackId)
        .last(PageConstant.LIMIT_ONE).getOne();
  }

  @Override
  public List<GiftPackConfig> getGiftPackConfigInfo(GiftPackConfigQryCmd query) {
    return query()
        .eq(GiftPackConfig::getGiftPackId, query.getGiftPackId())
        .eq(GiftPackConfig::getType, query.getType())
        .orderByAsc(GiftPackConfig::getCreateTime)
        .list();
  }
}
