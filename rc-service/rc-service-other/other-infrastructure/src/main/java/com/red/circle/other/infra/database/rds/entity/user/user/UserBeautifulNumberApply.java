package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户申请靓号记录.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_beautiful_number_apply")
public class UserBeautifulNumberApply extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 申请用户.
   */
  @TableField("apply_user")
  private Long applyUser;

  /**
   * 处理结果(0:待处理，1:同意，2:驳回).
   */
  @TableField("state")
  private Integer state;

}
