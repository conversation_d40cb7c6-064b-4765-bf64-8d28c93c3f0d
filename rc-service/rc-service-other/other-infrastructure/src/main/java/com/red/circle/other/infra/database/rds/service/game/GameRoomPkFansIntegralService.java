package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkFansIntegral;

/**
 * <p>
 * 房间pk粉丝贡献值 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
public interface GameRoomPkFansIntegralService extends BaseService<GameRoomPkFansIntegral> {

  /**
   * 最佳粉丝.
   *
   * @param userId   userId
   * @param timeType timeType:1.上周 2.这周 3.昨天 4.今天
   */
  GameRoomPkFansIntegral getBestFan(Long userId, Integer timeType);

  /**
   * 辅助:查询指定时间数据
   */
  GameRoomPkFansIntegral getSpecifiedTimeBestFan(Long userId, String startTime, String endTime);


}
