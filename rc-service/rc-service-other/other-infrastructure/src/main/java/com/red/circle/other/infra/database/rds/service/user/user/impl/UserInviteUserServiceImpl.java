package com.red.circle.other.infra.database.rds.service.user.user.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.UserInviteUserDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserInviteUser;
import com.red.circle.other.infra.database.rds.service.user.user.UserInviteUserService;
import com.red.circle.other.inner.model.cmd.team.bd.BdTeamWorkStatisticsQryCmd;
import com.red.circle.other.inner.model.dto.user.UserInviteUserCountDTO;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> on 2024/3/8
 */
@Service
@RequiredArgsConstructor
public class UserInviteUserServiceImpl extends
    BaseServiceImpl<UserInviteUserDAO, UserInviteUser> implements UserInviteUserService {

  private final UserInviteUserDAO userInviteUserDAO;

  @Override
  public List<UserInviteUserCountDTO> listInviteStatisticsByUserIds(
      BdTeamWorkStatisticsQryCmd qryCmd,Set<Long> userIds) {
    if(CharSequenceUtil.hasBlank(qryCmd.getStartDate(),qryCmd.getEndDate())){
      qryCmd.setStartDate(LocalDate.now().minusMonths(1).toString());
      qryCmd.setEndDate(LocalDate.now().plusDays(1).toString());
    }
    return userInviteUserDAO.listInviteStatisticsByUserIds(qryCmd,userIds);
  }
}
