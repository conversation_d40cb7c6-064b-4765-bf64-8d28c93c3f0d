package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户cp关系.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_cp_relationship")
public class CpRelationship extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键id.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * cp值id.
   */
  @TableField("cp_val_id")
  private Long cpValId;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * cp用户id.
   */
  @TableField("cp_user_id")
  private Long cpUserId;

  /**
   * 0.发起人 1.接收人
   */
  @TableField("is_initiate")
  private Integer initiate;

  /**
   * 赠送的礼物价值
   */
  @TableField("amount")
  private BigDecimal amount;


}
