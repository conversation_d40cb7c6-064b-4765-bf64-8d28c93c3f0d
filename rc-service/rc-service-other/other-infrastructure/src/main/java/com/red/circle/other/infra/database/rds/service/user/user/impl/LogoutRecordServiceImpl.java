package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.LogoutRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.LogoutRecord;
import com.red.circle.other.infra.database.rds.service.user.user.LogoutRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统注销记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-07
 */
@Service
public class LogoutRecordServiceImpl extends
    BaseServiceImpl<LogoutRecordDAO, LogoutRecord> implements LogoutRecordService {

}
