package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserBeautifulNumber;
import com.red.circle.other.inner.model.cmd.user.BeautifulNumberQryCmd;
import com.red.circle.other.inner.model.cmd.user.UserBeautifulNumberCmd;

/**
 * <p>
 * 用户靓号 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
public interface UserBeautifulNumberService extends BaseService<UserBeautifulNumber> {

  /**
   * true 存在， false 不存在.
   */
  boolean exists(Long account);

  /**
   * 修改靓号信息
   */
  void updateBeautifulNumber(UserBeautifulNumberCmd cmd);

  /**
   * 新增靓号
   */
  void addBeautifulNumber(UserBeautifulNumberCmd dto);

  /**
   * 分页查询用户靓号
   */
  PageResult<UserBeautifulNumber> getBeautifulNumber(BeautifulNumberQryCmd query);
}
