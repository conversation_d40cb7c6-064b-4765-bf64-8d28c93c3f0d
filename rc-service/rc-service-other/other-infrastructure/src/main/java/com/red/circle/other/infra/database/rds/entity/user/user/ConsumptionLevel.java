package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户消费等级.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_consumption_level")
public class ConsumptionLevel extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户id.
   */
  @TableId(value = "user_id", type = IdType.INPUT)
  private Long userId;

  /**
   * 消费金币.
   */
  @TableField("consumption_golds")
  private BigDecimal consumptionGolds;

  /**
   * 消费钻石.
   */
  @TableField("consumption_diamond")
  private BigDecimal consumptionDiamond;

}
