package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.InviteConfigDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteConfig;
import com.red.circle.other.infra.database.rds.service.user.user.InviteConfigService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 邀请新用户-配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@AllArgsConstructor
@Service
public class InviteConfigServiceImpl extends
    BaseServiceImpl<InviteConfigDAO, InviteConfig> implements InviteConfigService {

  @Override
  public InviteConfig getBySysOrigin(String sysOrigin) {

    return query()
        .eq(InviteConfig::getSysOrigin, sysOrigin)
        .getOne();
  }


}
