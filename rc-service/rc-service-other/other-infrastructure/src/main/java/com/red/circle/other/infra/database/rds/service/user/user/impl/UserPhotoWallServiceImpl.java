package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.google.common.collect.Lists;
import com.red.circle.common.business.enums.ViolationEnum;
import com.red.circle.framework.core.dto.PageCommand;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.convertor.MybatisConvertor;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.inner.enums.other.PhotoCensorStatusEnum;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.UserPhotoWallDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.PhotoWall;
import com.red.circle.other.infra.database.rds.service.user.user.UserPhotoWallService;
import com.red.circle.other.inner.model.cmd.user.PhotoWallApprovalTableQryCmd;
import com.red.circle.other.inner.model.dto.user.UserPhotoWallDTO;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 用户照片墙 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-08
 */

@Service
@RequiredArgsConstructor
public class UserPhotoWallServiceImpl extends
    BaseServiceImpl<UserPhotoWallDAO, PhotoWall> implements UserPhotoWallService {


  @Override
  public void deleteById(Long id) {
    delete().eq(PhotoWall::getId, id).execute();
  }

  @Override
  public PageResult<PhotoWall> pageUserPhotoWall(PageCommand command) {
    return query()
        .orderByDesc(PhotoWall::getId)
        .page(command.getPageQuery());
  }

  @Override
  public List<PhotoWall> listUserPhotoWall(Long userId) {
    return query().eq(PhotoWall::getUserId, userId)
        .orderByAsc(PhotoWall::getSort).list();
  }

  @Override
  public PhotoWall userPhotoWall(Long id) {
    return query().eq(PhotoWall::getId, id).getOne();
  }

  @Override
  public List<PhotoWall> listUserPhotoWallNormal(Long userId) {
    return Optional.ofNullable(query().eq(PhotoWall::getUserId, userId)
        .in(PhotoWall::getViolation, PhotoCensorStatusEnum.NORMAL, PhotoCensorStatusEnum.SUSPECTED)
        /*.and(wq-> wq.eq(PhotoWall::getViolation,ViolationEnum.NORMAL).or().eq(PhotoWall::getViolation, ViolationEnum.SUSPECTED))*/
        .orderByAsc(PhotoWall::getSort)
        .list()).orElse(Lists.newArrayList());

  }

  @Override
  public PageResult<UserPhotoWallDTO> pageUserDetailsPhotoWall(
      PhotoWallApprovalTableQryCmd cmd) {
    return MybatisConvertor.toPageResult(
        baseDAO.pageUserDetailsPhotoWall(MybatisConvertor.toPage(cmd.getPageQuery()), cmd));
  }

  @Override
  public boolean updateViolationStatus(List<Long> ids, ViolationEnum violationEnum,
      Long approvalUser) {
    if (CollectionUtils.isEmpty(ids)) {
      return false;
    }
    return update()
        .set(PhotoWall::getViolation, violationEnum.name())
        .set(PhotoWall::getUpdateUser, approvalUser)
        .in(PhotoWall::getId, ids)
        .execute();
  }

}
