package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.StartPageUserDAO;
import com.red.circle.other.infra.database.rds.entity.sys.StartPageUser;
import com.red.circle.other.infra.database.rds.service.sys.StartPageUserService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 启动页展示用户 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
@Service
public class StartPageUserServiceImpl extends
    BaseServiceImpl<StartPageUserDAO, StartPageUser> implements StartPageUserService {


  @Override
  public Map<String, List<StartPageUser>> mapGroupType(String sysOrigin, Set<String> types) {
    return CollectionUtils.isEmpty(types)
        ? CollectionUtils.newHashMap()
        : Optional.ofNullable(query()
                .eq(StartPageUser::getSysOrigin, sysOrigin)
                .in(StartPageUser::getType, types)
                .gt(StartPageUser::getExpireTime, TimestampUtils.now())
                .orderByAsc(StartPageUser::getSort)
                .list())
            .map(startPageUsers -> startPageUsers.stream().collect(Collectors
                .groupingBy(StartPageUser::getType)))
            .orElseGet(CollectionUtils::newHashMap);
  }

}
