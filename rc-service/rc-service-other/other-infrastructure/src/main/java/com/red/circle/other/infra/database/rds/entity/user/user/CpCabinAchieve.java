package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * CP小屋成就.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("user_cp_cabin_achieve")
public class CpCabinAchieve extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键id.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * CP标识ID.
   */
  @TableField("cp_val_id")
  private Long cpValId;

  /**
   * 年月(yyyyMM).
   */
  @TableField("date_number")
  private Integer dateNumber;

  /**
   * cp值.
   */
  @TableField("cp_val")
  private Long cpVal;

}
