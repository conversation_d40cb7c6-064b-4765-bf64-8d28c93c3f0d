package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysFeedbackDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysFeedback;
import com.red.circle.other.infra.database.rds.service.sys.SysFeedbackService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户反馈 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-28
 */
@Service
public class SysFeedbackServiceImpl extends BaseServiceImpl<SysFeedbackDAO, SysFeedback> implements
    SysFeedbackService {

}
