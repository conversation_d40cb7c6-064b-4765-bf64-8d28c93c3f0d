package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteUserRewardRecord;
import com.red.circle.other.inner.model.cmd.user.UserInviteRewardQryCmd;
import java.util.List;

/**
 * <p>
 * 用户邀请奖励记录 服务类
 * </p>
 *
 * <AUTHOR> on 2021/4/27
 * @since pengliang 2021/5/25
 */
public interface InviteUserRewardRecordService extends BaseService<InviteUserRewardRecord> {

  /**
   * 检查是否被邀请过
   *
   * @param inviteUserId 用户id
   * @return true 已被邀请，false 未被邀请
   */
  boolean existsByInviteUserId(Long inviteUserId);

  /**
   * 获取奖励记录.
   *
   * @param userId 用户id
   * @param lastId 最后一条记录id
   * @return ignore
   */
  List<InviteUserRewardRecord> listInviteRewardRecord(Long userId, Long lastId);

  /**
   * 根据被邀请人ID查询
   *
   * @param userId 被邀请人ID
   * @return ignore
   */
  InviteUserRewardRecord getByInviteUserId(Long userId);

  PageResult<InviteUserRewardRecord> getUserInviteRewardRecord(
      UserInviteRewardQryCmd query);
}
