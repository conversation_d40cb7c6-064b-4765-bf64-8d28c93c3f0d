package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.RoomThemeUserBackpack;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 用户房间主题背包 服务类.
 * </p>
 *
 * <AUTHOR> on 2021-01-23
 */
public interface RoomThemeUserBackpackService extends BaseService<RoomThemeUserBackpack> {

  /**
   * 获取用户主题背包
   *
   * @param userId 用户id
   * @return ignore
   */
  List<RoomThemeUserBackpack> listByUserIdOrderByUseThemeDesc(Long userId);

  /**
   * 用户切换使用主题
   *
   * @param userId     用户id
   * @param useThemeId 主题id
   * @param isFree     true 免费，false 不免费
   */
  boolean switchUseTheme(Long userId, Long useThemeId, Boolean isFree);

  /**
   * 清空用户使用主题.
   *
   * @param userId 用户id
   */
  boolean cleanUseTheme(Long userId);

  /**
   * 获取用户主题信息 & 还在有效期之内的主题.
   *
   * @param userId  用户id
   * @param themeId 主题id
   */
  RoomThemeUserBackpack getNotExpireTimeByUserAndThemeId(Long userId, Long themeId);

  /**
   * 添加主题.
   *
   * @param userId  用户id
   * @param themeId 主题id
   * @param days    添加天数
   */
  void addTheme(Long userId, Long themeId, Integer days);

  /**
   * 获取用户正在使用主题
   *
   * @param userId 用户id
   * @return ignore
   */
  RoomThemeUserBackpack getByUserUseTheme(Long userId);

  /**
   * 获取一组用户正在使用背包
   *
   * @param userIds 用户id集合
   * @return ignore
   */
  List<RoomThemeUserBackpack> listByUserUseTheme(Collection<Long> userIds);


}
