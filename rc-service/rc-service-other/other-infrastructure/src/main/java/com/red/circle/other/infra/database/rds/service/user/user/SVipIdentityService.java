package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.SVipIdentity;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * SVip用户 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
public interface SVipIdentityService extends BaseService<SVipIdentity> {

  /**
   * 获得到期用户,且等级大于0.
   */
  List<SVipIdentity> listByExpire(SysOriginPlatformEnum sysOrigin);

  /**
   * 获取一组SVip用户.
   */
  List<SVipIdentity> listByUserIds(Set<Long> userIds);

  /**
   * 修改积分.
   */
  void updateIntegral(Long userId, Long integral);

  /**
   * 删除积分为0，且等级为0的用户.
   */
  void deleteInvalidUser();

  /**
   * 根据用户id获得信息
   */
  SVipIdentity getByUserId(Long userId);

}
