package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameRoomPkFansIntegralDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkFansIntegral;
import com.red.circle.other.infra.database.rds.service.game.GameRoomPkFansIntegralService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间pk粉丝贡献值 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-27
 */
@Service
@RequiredArgsConstructor
public class GameRoomPkFansIntegralServiceImpl extends
    BaseServiceImpl<GameRoomPkFansIntegralDAO, GameRoomPkFansIntegral> implements
    GameRoomPkFansIntegralService {

  private final GameRoomPkFansIntegralDAO gameRoomPkFansIntegralDAO;

  @Override
  public GameRoomPkFansIntegral getBestFan(Long userId, Integer timeType) {
    return gameRoomPkFansIntegralDAO.getBestFan(userId, timeType);
  }

  @Override
  public GameRoomPkFansIntegral getSpecifiedTimeBestFan(Long userId, String startTime,
      String endTime) {
    return gameRoomPkFansIntegralDAO.getSpecifiedTimeBestFan(userId, startTime, endTime);
  }
}
