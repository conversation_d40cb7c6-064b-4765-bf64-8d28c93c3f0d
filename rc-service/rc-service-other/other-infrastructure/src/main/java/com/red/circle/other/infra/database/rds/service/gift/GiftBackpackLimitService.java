package com.red.circle.other.infra.database.rds.service.gift;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.gift.GiftBackpackLimit;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户礼物背包-限制 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-14
 */
public interface GiftBackpackLimitService extends BaseService<GiftBackpackLimit> {

  /**
   * 获取背包id集合.
   *
   * @param userIds 用户id
   * @param giftTab 礼物tab
   * @return 背包id
   */
  List<Long> getBackIdsByGiftTab(Set<Long> userIds, String giftTab);

  /**
   * 删除限制.
   *
   * @param userIds 用户id
   * @param giftTab 礼物tab
   * @return 背包id
   */
  void deleteByGiftTab(Set<Long> userIds, String giftTab);

  /**
   * 获取限制映射.
   *
   * @param userId  用户id
   * @param backIds 背包id
   * @return ignore
   */
  Map<Long, GiftBackpackLimit> mapByBackIds(Long userId, Set<Long> backIds);

  /**
   * 查询数据.
   */
  GiftBackpackLimit getByUserIdByBackId(Long userId, Long backId);

}
