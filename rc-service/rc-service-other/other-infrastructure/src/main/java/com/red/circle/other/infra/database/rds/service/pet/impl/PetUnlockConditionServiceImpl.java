package com.red.circle.other.infra.database.rds.service.pet.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.pet.PetUnlockConditionDAO;
import com.red.circle.other.infra.database.rds.entity.pet.PetUnlockCondition;
import com.red.circle.other.infra.database.rds.service.pet.PetUnlockConditionService;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 解锁条件 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PetUnlockConditionServiceImpl extends
    BaseServiceImpl<PetUnlockConditionDAO, PetUnlockCondition> implements
    PetUnlockConditionService {

  @Override
  public List<PetUnlockCondition> listByPetId(Long petId) {
    return query()
        .eq(PetUnlockCondition::getPetId, petId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }
}
