package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.ConfessionChance;
import java.util.List;

/**
 * <p>
 * 告白机会 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-11-14 15:45
 */
public interface ConfessionChanceService extends BaseService<ConfessionChance> {

  /**
   * 用户剩余告白次数
   */
  Long countConfessionChance(Long userId);

  /**
   * 用户告白机会列表
   */
  List<ConfessionChance> getUserConfessionChance(Long userId,Long lastId);

  /**
   * 获取用户告白机会
   */
  ConfessionChance getChanceById(Long id);

  /**
   * 更新用户告白机会
   */
  void updateChanceById(Long id);
}
