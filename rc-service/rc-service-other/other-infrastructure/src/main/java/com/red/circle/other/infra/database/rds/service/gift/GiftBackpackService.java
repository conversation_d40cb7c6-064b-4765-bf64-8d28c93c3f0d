package com.red.circle.other.infra.database.rds.service.gift;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.gift.GiftBackpack;
import com.red.circle.other.inner.model.dto.game.GameLuckyBoxTicketCountInfoDTO;
import java.util.List;

/**
 * <p>
 * 用户礼物背包 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
public interface GiftBackpackService extends BaseService<GiftBackpack> {

  /**
   * 根据ID删除指定记录.
   *
   * @param ids id
   */
  void deleteByIds(List<Long> ids);

  /**
   * 获取用户礼物背包.
   *
   * @param userId 用户id
   * @return ignore
   */
  List<GiftBackpack> listUserGiftBackPack(Long userId);

  /**
   * 添加礼物.
   *
   * @param userId   用户id
   * @param giftId   礼物id
   * @param quantity 数量
   * @return gift
   */
  GiftBackpack incrGift(Long userId, Long giftId, Integer quantity);

  /**
   * 消费礼物.
   *
   * @param quantity 数量
   * @param giftId   礼物id
   * @return ignore
   */
  boolean consumerGifts(Long id, Long giftId, Long userId, Integer quantity);

  /**
   * 获取用户背包对应礼物.
   *
   * @param userId 用户id
   * @param giftId 礼物id
   * @return ignore
   */
  GiftBackpack getUserGift(Long userId, Long giftId);

  /**
   * 获取用户背包对应礼物.
   *
   * @param userId   用户id
   * @param giftId   礼物id
   * @param quantity 数量
   */
  void deductGift(Long userId, Long giftId, Integer quantity);

  GameLuckyBoxTicketCountInfoDTO ticketCount(Long id, Long aLong);
}
