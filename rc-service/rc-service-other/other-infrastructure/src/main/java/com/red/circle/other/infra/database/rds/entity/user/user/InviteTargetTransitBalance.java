package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 邀请新用户-目标中转账户.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_target_transit_balance")
public class InviteTargetTransitBalance extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户ID.
   */
  @TableId(value = "id", type = IdType.INPUT)
  private Long id;

  /**
   * 系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 收入目标.
   */
  @TableField("income_amount")
  private BigDecimal incomeAmount;

  /**
   * 支出目标.
   */
  @TableField("expenditure_balance")
  private BigDecimal expenditureBalance;


}
