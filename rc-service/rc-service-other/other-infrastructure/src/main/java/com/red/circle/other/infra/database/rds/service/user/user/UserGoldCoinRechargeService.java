package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.other.inner.model.cmd.user.InviteStatisticsQryCmd;
import com.red.circle.other.inner.model.dto.user.UserGoldCoinRechargeDTO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> on 2024/3/22
 */
public interface UserGoldCoinRechargeService {

  List<UserGoldCoinRechargeDTO> listRechargeCoinsByUserIds(Set<Long> userIds);

  Map<Long, BigDecimal> mapGoldCoinRechargeByUserIds(InviteStatisticsQryCmd qryCmd);
}
