package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxBountyConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckBoxBountyConfigQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <p>
 * 赏金任务配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxBountyConfigService extends BaseService<GameLuckyBoxBountyConfig> {

  List<GameLuckyBoxBountyConfig> listBySysOrigin(String sysOriginName);

  Map<Long, GameLuckyBoxBountyConfig> mapByGiftIds(Set<Long> giftIds);

  Map<Long, GameLuckyBoxBountyConfig> getMap(String sysOriginName);

  PageResult<GameLuckyBoxBountyConfig> getBountyConfig(GameLuckBoxBountyConfigQryCmd query);
}
