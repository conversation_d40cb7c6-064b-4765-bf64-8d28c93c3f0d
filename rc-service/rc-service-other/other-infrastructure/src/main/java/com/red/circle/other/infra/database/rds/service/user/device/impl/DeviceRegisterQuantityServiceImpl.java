package com.red.circle.other.infra.database.rds.service.user.device.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.infra.database.rds.dao.user.device.DeviceRegisterQuantityDAO;
import com.red.circle.other.infra.database.rds.entity.user.device.DeviceRegisterQuantity;
import com.red.circle.other.infra.database.rds.service.user.device.DeviceRegisterQuantityService;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备注册数量 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
@Service
public class DeviceRegisterQuantityServiceImpl extends
    BaseServiceImpl<DeviceRegisterQuantityDAO, DeviceRegisterQuantity> implements
    DeviceRegisterQuantityService {

  @Override
  public void incrQuantity(String deviceNo, String sysOrigin) {
    DeviceRegisterQuantity deviceRegisterQuantity = query()
        .eq(DeviceRegisterQuantity::getDeviceNo, deviceNo)
        .eq(DeviceRegisterQuantity::getSysOrigin, sysOrigin)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(deviceRegisterQuantity)) {
      save(new DeviceRegisterQuantity()
          .setDeviceNo(deviceNo)
          .setRegisterNumber(1)
          .setSysOrigin(sysOrigin)
      );
      return;
    }
    deviceRegisterQuantity.setRegisterNumber(deviceRegisterQuantity.getRegisterNumber() + 1);
    updateSelectiveById(deviceRegisterQuantity);
  }

  @Override
  public int getDeviceNoQuantity(String deviceNo, String sysOrigin) {
    DeviceRegisterQuantity deviceRegisterQuantity = query()
        .select(DeviceRegisterQuantity::getRegisterNumber)
        .eq(DeviceRegisterQuantity::getSysOrigin, sysOrigin)
        .eq(DeviceRegisterQuantity::getDeviceNo, deviceNo)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    return Objects.nonNull(deviceRegisterQuantity) && Objects.nonNull(
        deviceRegisterQuantity.getRegisterNumber())
        ? deviceRegisterQuantity.getRegisterNumber() : 0;
  }


  @Override
  public Map<String, DeviceRegisterQuantity> mapDeviceRegisterQuantity(
      Collection<String> deviceNos, String sysOrigin) {
    if (CollectionUtils.isEmpty(deviceNos) || StringUtils.isBlank(sysOrigin)) {
      return Maps.newHashMap();
    }
    return Optional.ofNullable(query()
            .eq(DeviceRegisterQuantity::getSysOrigin, sysOrigin)
            .in(DeviceRegisterQuantity::getDeviceNo, deviceNos)
            .list())
        .map(sysDeviceRegisterQuantities -> sysDeviceRegisterQuantities.stream()
            .collect(Collectors.toMap(DeviceRegisterQuantity::getDeviceNo, Function.identity(),
                (k, v) -> v)))
        .orElse(Maps.newHashMap());
  }
}
