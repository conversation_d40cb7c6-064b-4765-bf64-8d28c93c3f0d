package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.LogoutApply;
import com.red.circle.other.inner.model.cmd.user.UserLogoutApplyQryCmd;

/**
 * <p>
 * 用户注销web页面申请 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-09-18 14:46
 */
public interface LogoutApplyService extends BaseService<LogoutApply> {

  Boolean getByUserAccount(String userAccount, String logoutType);

  PageResult<LogoutApply> pageUserLogoutApply(UserLogoutApplyQryCmd query);
}
