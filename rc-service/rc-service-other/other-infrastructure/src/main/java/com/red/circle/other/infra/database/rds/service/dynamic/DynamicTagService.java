package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicTag;
import com.red.circle.other.inner.model.cmd.dynamic.PageDynamicTagQryCmd;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 动态-朋友圈标签 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface DynamicTagService extends BaseService<DynamicTag> {

  /**
   * 根据ids获得map集合
   *
   * @param ids 主键id
   */
  Map<Long, DynamicTag> mapByIds(Collection<Long> ids);

  /**
   * 获得标签列表
   *
   * @param sysOrigin 系统
   * @param language  语言
   */
  List<DynamicTag> listTag(SysOriginPlatformEnum sysOrigin, String language);

  void addOrUpdate(DynamicTag dynamicTag);

  PageResult<DynamicTag> pageData(PageDynamicTagQryCmd query);
}
