package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <p>
 * 用户基础信息表.
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_base_info")
public class BaseInfo extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.INPUT)
  private Long id;

  /**
   * 账号.
   */
  @TableField("account")
  private String account;

  /**
   * 用户头像.
   */
  @TableField("user_avatar")
  private String userAvatar;

  /**
   * 用户昵称.
   */
  @TableField("user_nickname")
  private String userNickname;

  /**
   * 用户性别:0 女，1 男.
   */
  @TableField("user_sex")
  private Integer userSex;

  /**
   * 用户类型:0.真实用户 1.马甲用户 2.vip马甲 3.视频号关联.
   */
  @TableField("user_type")
  private Integer userType;

  /**
   * 国家id.
   */
  @TableField("country_id")
  private Long countryId;

  /**
   * 国家名称.
   */
  @TableField("country_name")
  private String countryName;

  /**
   * 国家编码.
   */
  @TableField("country_code")
  private String countryCode;

  /**
   * 年龄.
   */
  @TableField("age")
  private Integer age;

  /**
   * 出身年.
   */
  @TableField("born_year")
  private Integer bornYear;

  /**
   * 出身月.
   */
  @TableField("born_month")
  private Integer bornMonth;

  /**
   * 出身日.
   */
  @TableField("born_day")
  private Integer bornDay;

  /**
   * 账号状态.
   */
  @TableField("account_status")
  private String accountStatus;

  /**
   * 冻结时间.
   */
  @TableField("freezing_time")
  private Timestamp freezingTime;

  /**
   * 0.未删除 1.已删除.
   */
  @TableField("is_del")
  private Boolean del;

  /**
   * 系统来源.
   */
  @TableField("origin_sys")
  private String originSys;

  /**
   * 子系统.
   */
  @TableField("sys_origin_child")
  private String sysOriginChild;

}
