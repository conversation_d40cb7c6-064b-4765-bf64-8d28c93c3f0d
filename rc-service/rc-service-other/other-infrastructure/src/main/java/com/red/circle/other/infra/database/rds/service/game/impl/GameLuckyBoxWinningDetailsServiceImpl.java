package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxWinningDetailsDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxWinningDetails;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxWinningDetailsService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Lucky Box中奖记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-13
 */
@Service
public class GameLuckyBoxWinningDetailsServiceImpl extends
    BaseServiceImpl<GameLuckyBoxWinningDetailsDAO, GameLuckyBoxWinningDetails> implements
    GameLuckyBoxWinningDetailsService {


}
