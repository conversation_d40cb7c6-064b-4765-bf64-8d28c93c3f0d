package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 邀请新用户-红包抽奖记录.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_red_packet_draw_log")
public class InviteRedPacketDrawLog extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 红包记录id.
   */
  @TableField("red_packet_id")
  private Long redPacketId;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 奖励类型.
   */
  @TableField("award_type")
  private String awardType;

  /**
   * 奖励数量.
   */
  @TableField("award_number")
  private BigDecimal awardNumber;

  /**
   * 红包总额.
   */
  @TableField("total_amount")
  private Long totalAmount;

  /**
   * 当前红包进度额度.
   */
  @TableField("current_amount")
  private BigDecimal currentAmount;

  /**
   * 剩余抽奖次数.
   */
  @TableField("remain_frequency")
  private Long remainFrequency;

  /**
   * 抽奖人设备号.
   */
  @TableField("imei")
  private String imei;

  /**
   * 抽奖人IP.
   */
  @TableField("ip")
  private String ip;


}
