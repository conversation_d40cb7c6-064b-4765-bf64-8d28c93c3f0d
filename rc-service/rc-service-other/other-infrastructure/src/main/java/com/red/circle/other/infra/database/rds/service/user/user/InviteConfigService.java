package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteConfig;

/**
 * <p>
 * 邀请新用户-配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
public interface InviteConfigService extends BaseService<InviteConfig> {

  InviteConfig getBySysOrigin(String sysOrigin);

}
