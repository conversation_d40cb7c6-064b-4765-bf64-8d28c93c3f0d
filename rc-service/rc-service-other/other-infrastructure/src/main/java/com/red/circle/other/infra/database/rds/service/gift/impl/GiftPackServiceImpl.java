package com.red.circle.other.infra.database.rds.service.gift.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.gift.GiftPackDAO;
import com.red.circle.other.infra.database.rds.entity.gift.GiftPack;
import com.red.circle.other.infra.database.rds.service.gift.GiftPackService;
import com.red.circle.other.inner.model.cmd.sys.GiftPackQryCmd;
import com.red.circle.other.inner.model.dto.sys.SysGiftPackDTO;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 礼包 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-10
 */
@Service
public class GiftPackServiceImpl extends
    BaseServiceImpl<GiftPackDAO, GiftPack> implements GiftPackService {

  @Override
  public PageResult<GiftPack> getGiftPackInfo(GiftPackQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getId()), GiftPack::getId, query.getId())
        .eq(GiftPack::getDel, Boolean.FALSE)
        .orderByDesc(GiftPack::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public void addGiftPack(SysGiftPackDTO sysGiftPackDTO) {
  }

  @Override
  public void updateGiftPack(SysGiftPackDTO sysGiftPackDTO) {
  }

  @Override
  public void deleteGiftPack(Long id) {
    ResponseAssert.isTrue(CommonErrorCode.DELETE_FAILURE,
        update().set(GiftPack::getDel, Boolean.TRUE)
            .eq(GiftPack::getId, id)
            .execute());
  }
}
