package com.red.circle.other.infra.database.rds.service.badge;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.badge.RoomBadgeBackpack;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 房间徽章
 * <p>
 * -背包 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-28
 */
public interface RoomBadgeBackpackService extends BaseService<RoomBadgeBackpack> {

  /**
   * 获取徽章背包.
   *
   * @param roomId 房间id
   * @return ignore
   */
  List<RoomBadgeBackpack> listBadgeBackpackByRoomId(Long roomId);

  /**
   * 获取徽章背包.
   *
   * @param roomIds 房间ids
   * @return ignore
   */
  Map<Long, List<RoomBadgeBackpack>> mapByRoomIds(Set<Long> roomIds);

  /**
   * 获取徽章背包.
   *
   * @param roomIds 房间ids
   * @return ignore
   */
  List<RoomBadgeBackpack> listByRoomIds(Set<Long> roomIds);

  /**
   * 激活-永久徽章.
   *
   * @param roomId  房间id
   * @param userId  用户id
   * @param badgeId 徽章id
   * @return true 激活成功 ,false 激活失败
   */
  boolean activationPermanent(Long roomId, Long userId, Long badgeId);

  /**
   * 激活-临时徽章.
   *
   * @param roomId  房间id
   * @param userId  用户id
   * @param badgeId 徽章id
   * @param days    天数
   * @return true 激活成功 ,false 激活失败
   */
  boolean activationTemporary(Long roomId, Long userId, Long badgeId, Integer days);

  /**
   * 获得徽章
   *
   * @param userId  用户id
   * @param badgeId 徽章id
   * @return 用户房间徽章
   */
  RoomBadgeBackpack getByUserIdByBadgeId(Long userId, Long badgeId);

}
