package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxProbabilityConfig;
import com.red.circle.other.inner.model.dto.game.GameLuckyBoxProbabilityConfigDTO;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 抽奖概率配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxProbabilityConfigService extends
    BaseService<GameLuckyBoxProbabilityConfig> {

  List<GameLuckyBoxProbabilityConfig> getByTimeId(Long timeId);

  void deleteByTimeId(Long id);

  Map<Long, List<GameLuckyBoxProbabilityConfigDTO>> getLuckyBoxProbabilityDetails(String sysOrigin);

  void add(Long timeId, List<GameLuckyBoxProbabilityConfig> gameLuckyBoxProbabilityDetails);
}
