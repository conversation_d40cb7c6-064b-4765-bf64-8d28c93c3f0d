package com.red.circle.other.infra.database.rds.service.gift;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.gift.GiftWall;
import java.util.List;

/**
 * <p>
 * 用户获得礼物梳理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-12
 */
public interface GiftWallService extends BaseService<GiftWall> {

  /**
   * 累计礼物墙
   *
   * @param userId       用户id
   * @param giftId       礼物id
   * @param giftQuantity 礼物数量
   */
  void incrGiftWall(Long userId, Long giftId, Long giftQuantity);

  /**
   * 回收礼物墙.
   */
  void decrGiftWall(Long userId, Long giftId, Long giftQuantity);

  /**
   * 获取用户礼物墙
   *
   * @param userId 用户id
   * @return ignore
   */
  List<GiftWall> listTopByUserId(Long userId);

  /**
   * 获取用户礼物墙
   *
   * @param userId 用户id
   * @return ignore
   */
  List<GiftWall> listGiftWallByUser(Long userId);
}
