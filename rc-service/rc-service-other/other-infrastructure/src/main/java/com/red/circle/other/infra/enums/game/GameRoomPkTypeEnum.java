package com.red.circle.other.infra.enums.game;

/**
 * 游戏状态
 *
 * <AUTHOR> on 2021/9/26
 */
public enum GameRoomPkTypeEnum {

  /**
   * 发起 房间pk.
   */
  GAME_ROOM_PK_SPONSOR,

  /**
   * 接受 房间pk.
   */
  GAME_ROOM_PK_ACCEPT,

  /**
   * 主动结束 房间pk.
   */
  GAME_ROOM_PK_OVER,

  /**
   * 没有人玩，超时结束 房间pk.
   */
  GAME_ROOM_PK_EXPIRED_OVER,

  /**
   * 房间pk 正常结束，没有优胜者.
   */
  GAME_ROOM_PK_NO_WINNER_OVER,

  /**
   * 房间pk 最终输赢结果.
   */
  GAME_ROOM_PK_RESULT

}
