package com.red.circle.other.infra.database.rds.enums;

import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 缓存的key
 *
 * <AUTHOR>
 * @since 2019/11/23 13:54
 */
@Deprecated
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class CacheKeyStructure {

  private static final String SEPARATOR = ":";

  /**
   * key的前缀
   */
  private String prefix;

  /**
   * prefix+key组成最终的键值
   */
  private String key;

  public static CacheKeyStructure of(String prefix) {
    return new CacheKeyStructure(prefix, null);
  }

  public static CacheKeyStructure of(String prefix, String key) {
    return new CacheKeyStructure(prefix, key);
  }

  public static CacheKeyStructure of(String prefix, Long key) {
    return new CacheKeyStructure(prefix, Objects.isNull(key) ? null : key.toString());
  }

  /**
   * 获取缓存
   */
  public String getCacheKey() {
    if (StringUtils.isNotBlank(prefix) && StringUtils.isNotBlank(key)) {
      return prefix.concat(SEPARATOR).concat(key);
    }

    if (StringUtils.isNotBlank(prefix)) {
      return prefix;
    }

    if (StringUtils.isNotBlank(key)) {
      return key;
    }
    return "";
  }
}
