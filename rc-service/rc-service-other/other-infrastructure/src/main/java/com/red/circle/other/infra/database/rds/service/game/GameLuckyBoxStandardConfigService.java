package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxStandardConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckBoxStandardConfigQryCmd;
import java.util.List;

/**
 * <p>
 * 抽奖规格配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxStandardConfigService extends BaseService<GameLuckyBoxStandardConfig> {

  List<GameLuckyBoxStandardConfig> listBySysOrigin(String sysOrigin);

  PageResult<GameLuckyBoxStandardConfig> getStandardConfig(GameLuckBoxStandardConfigQryCmd query);

  void switchStatus(Long id, Boolean status);
}
