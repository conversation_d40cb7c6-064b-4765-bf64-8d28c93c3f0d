package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.OneTimeTask;
import java.math.BigDecimal;

/**
 * <p>
 * 用户只可触发一次的任务 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-15
 */
public interface OneTimeTaskService extends BaseService<OneTimeTask> {

  /**
   * 查询用户首充任务信息
   *
   * @param userId    用户ID
   * @param sysOrigin 系统
   */
  OneTimeTask queryCompletedFirstCharge(Long userId, String sysOrigin);

  /**
   * 标记任务已完成
   *
   * @param userId    用户ID
   * @param sysOrigin 系统
   */
  void completedFirstCharge(Long userId, String sysOrigin);

  /**
   * 标记首充奖励已领取
   *
   * @param userId         用户ID
   * @param sysOrigin      系统
   * @param rewardQuantity 数量
   */
  void firstChargeStatusChangeReceive(Long userId, String sysOrigin, BigDecimal rewardQuantity);

}
