package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameKtvConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameKtvConfig;
import com.red.circle.other.infra.database.rds.service.game.GameKtvConfigService;
import com.red.circle.other.inner.model.cmd.game.GameKtvConfigQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * ktv歌曲配置信息.
 * </p>
 *
 * <AUTHOR> on 2023-05-05 17:18
 */
@Service
public class GameKtvConfigServiceImpl extends
    BaseServiceImpl<GameKtvConfigDAO, GameKtvConfig> implements GameKtvConfigService {

  @Override
  public List<GameKtvConfig> listByRegion(String sysOrigin, String regionId) {

    return Optional.ofNullable(query()
        .eq(GameKtvConfig::getDel, Boolean.FALSE)
        .like(GameKtvConfig::getRegions, regionId)
        .eq(GameKtvConfig::getShelfStatus, Boolean.TRUE)
        .eq(GameKtvConfig::getSysOrigin, sysOrigin)
        .orderByAsc(GameKtvConfig::getSort)
        .last(PageConstant.formatLimit(500))
        .list()).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public List<GameKtvConfig> listByIds(Set<Long> ids) {

    return Optional.ofNullable(query()
        .in(GameKtvConfig::getId, ids)
        .last(PageConstant.formatLimit(ids.size()))
        .list()).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public List<GameKtvConfig> listBySysOrigin(String sysOrigin) {

    return Optional.ofNullable(query()
            .eq(GameKtvConfig::getDel, Boolean.FALSE)
            .eq(GameKtvConfig::getShelfStatus, Boolean.TRUE)
            .eq(GameKtvConfig::getSysOrigin, sysOrigin)
            .orderByAsc(GameKtvConfig::getSort)
            .last(PageConstant.formatLimit(2000))
            .list())
        .orElse(Lists.newArrayList());

  }


  @Override
  public PageResult<GameKtvConfig> getSongInfo(GameKtvConfigQryCmd query) {

    return query().eq(GameKtvConfig::getDel, Boolean.FALSE)
        .eq(Objects.nonNull(query.getSongId()), GameKtvConfig::getId, query.getSongId())
        .like(StringUtils.isNotBlank(query.getSongName()), GameKtvConfig::getSongName,
            query.getSongName())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameKtvConfig::getSysOrigin,
            query.getSysOrigin())
        .like(StringUtils.isNotBlank(query.getRegion()), GameKtvConfig::getRegions,
            query.getRegion())
        .ge(Objects.nonNull(query.getStartCreateDate()), GameKtvConfig::getCreateTime,
            query.getStartCreateDate())
        .le(Objects.nonNull(query.getEndCreateDate()), GameKtvConfig::getCreateTime,
            query.getEndCreateDate())
        .eq(Objects.nonNull(query.getShelfStatus()), GameKtvConfig::getShelfStatus,
            query.getShelfStatus())
        .orderByAsc(query.isAsc(), GameKtvConfig::getSort)
        .orderByDesc(query.isDesc(), GameKtvConfig::getSort)
        .page(query.getPageQuery());
  }


  @Override
  public Map<Long, String> mapSongName(String sysOrigin) {
    List<GameKtvConfig> gameKtvConfigs = query()
        .eq(GameKtvConfig::getDel, Boolean.FALSE)
        .eq(GameKtvConfig::getSysOrigin, sysOrigin)
        .list();
    if (CollectionUtils.isEmpty(gameKtvConfigs)) {
      return Maps.newHashMap();
    }
    return gameKtvConfigs.stream()
        .collect(Collectors.toMap(GameKtvConfig::getId, v -> v.getSongName()));
  }
}
