package com.red.circle.other.infra.database.rds.service.badge.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.badge.RoomBadgeBackpackDAO;
import com.red.circle.other.infra.database.rds.entity.badge.RoomBadgeBackpack;
import com.red.circle.other.infra.database.rds.service.badge.RoomBadgeBackpackService;
import com.red.circle.other.inner.enums.material.BadgeBackpackExpireType;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间徽章
 * <p>
 * -背包 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-28
 */
@Service
public class RoomBadgeBackpackServiceImpl extends
    BaseServiceImpl<RoomBadgeBackpackDAO, RoomBadgeBackpack> implements RoomBadgeBackpackService {


  @Override
  public List<RoomBadgeBackpack> listBadgeBackpackByRoomId(Long roomId) {
    return Optional.ofNullable(query()
            .eq(RoomBadgeBackpack::getRoomId, roomId)
            .orderByDesc(RoomBadgeBackpack::getId)
            .list())
        .map(badgeBackpacks -> badgeBackpacks.stream().filter(this::filterAvailable)
            .collect(Collectors.toList()))
        .orElseGet(CollectionUtils::newArrayList);
  }

  @Override
  public Map<Long, List<RoomBadgeBackpack>> mapByRoomIds(Set<Long> roomIds) {

    return CollectionUtils.isEmpty(roomIds) ? CollectionUtils.newHashMap()
        : Optional.ofNullable(query()
                .in(RoomBadgeBackpack::getRoomId, roomIds)
                .orderByDesc(RoomBadgeBackpack::getId)
                .list()
            ).map(badgeBackpacks -> badgeBackpacks.stream().filter(this::filterAvailable)
                .collect(Collectors.groupingBy(RoomBadgeBackpack::getRoomId)))
            .orElseGet(CollectionUtils::newHashMap);
  }

  @Override
  public List<RoomBadgeBackpack> listByRoomIds(Set<Long> roomIds) {

    return CollectionUtils.isEmpty(roomIds) ? CollectionUtils.newArrayList()
        : Optional.ofNullable(query()
                .in(RoomBadgeBackpack::getRoomId, roomIds)
                .orderByDesc(RoomBadgeBackpack::getId)
                .list()
            ).map(badgeBackpacks -> badgeBackpacks.stream().filter(this::filterAvailable)
                .collect(Collectors.toList()))
            .orElseGet(CollectionUtils::newArrayList);
  }

  @Override
  public boolean activationPermanent(Long roomId, Long userId, Long badgeId) {
    deleteBadgeBackpack(roomId, badgeId);
    return save(new RoomBadgeBackpack()
        .setRoomId(roomId)
        .setUserId(userId)
        .setBadgeId(badgeId)
        .setExpireType(BadgeBackpackExpireType.PERMANENT.name())
    );
  }

  @Override
  public boolean activationTemporary(Long roomId, Long userId, Long badgeId, Integer days) {
    deleteBadgeBackpack(roomId, badgeId);
    return save(new RoomBadgeBackpack()
        .setRoomId(roomId)
        .setUserId(userId)
        .setBadgeId(badgeId)
        .setExpireType(BadgeBackpackExpireType.TEMPORARY.name())
        .setExpireTime(TimestampUtils.nowPlusDays(days))
    );
  }


  private void deleteBadgeBackpack(Long roomId, Long badgeId) {
    delete()
        .eq(RoomBadgeBackpack::getRoomId, roomId)
        .eq(RoomBadgeBackpack::getBadgeId, badgeId)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  private boolean filterAvailable(RoomBadgeBackpack badgeBackpack) {
    return Objects.equals(badgeBackpack.getExpireType(),
        BadgeBackpackExpireType.PERMANENT.name())
        || badgeBackpack.getExpireTime().after(TimestampUtils.now());
  }

  @Override
  public RoomBadgeBackpack getByUserIdByBadgeId(Long userId, Long badgeId) {
    return Optional.ofNullable(query()
        .eq(RoomBadgeBackpack::getUserId, userId)
        .eq(RoomBadgeBackpack::getBadgeId, badgeId)
        .last(PageConstant.LIMIT_ONE)
        .getOne()).orElse(null);
  }

}
