package com.red.circle.other.infra.database.rds.service.dynamic;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.TopDynamic;
import com.red.circle.other.inner.model.cmd.dynamic.TopDynamicCmd;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 系统置顶动态 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-17
 */
public interface TopDynamicService extends BaseService<TopDynamic> {

  List<TopDynamic> listBySysOrigin(SysOriginPlatformEnum sysOrigin);

  void setUpTop(TopDynamicCmd param);

  void closeTop(Long dynamicId);

  void deleteByDynamicIds(Set<Long> ids);
}
