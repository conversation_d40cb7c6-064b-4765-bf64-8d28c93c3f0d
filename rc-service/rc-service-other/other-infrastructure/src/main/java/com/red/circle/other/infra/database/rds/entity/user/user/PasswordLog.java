package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 用户密码日志.
 * </p>
 *
 * <AUTHOR> on 2023-12-07 12:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_password_log")
public class PasswordLog extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 归属平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户ID.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 操作类型（设置、修改）.
   */
  @TableField("operate_type")
  private String operateType;

  /**
   * 业务类型（账号、手机号、支付...）.
   */
  @TableField("business_type")
  private String businessType;

  /**
   * 密码.
   */
  @TableField("pwd")
  private String pwd;

  /**
   * 机器码.
   */
  @TableField("imei")
  private String imei;


}
