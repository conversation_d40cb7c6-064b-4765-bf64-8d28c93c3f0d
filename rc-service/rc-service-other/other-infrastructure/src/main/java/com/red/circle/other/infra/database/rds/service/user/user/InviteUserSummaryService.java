package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteUserSummary;
import java.math.BigDecimal;

/**
 * <p>
 * 邀请用户充值佣金与数量累计 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
public interface InviteUserSummaryService extends BaseService<InviteUserSummary> {

  /**
   * 累计用户数量.
   *
   * @param userId   用户id
   * @param quantity 数量
   */
  void incrUser(Long userId, Integer quantity);

  /**
   * 累计佣金.
   *
   * @param userId     用户id
   * @param commission 佣金
   */
  void incrCommission(Long userId, BigDecimal commission);

  /**
   * 获得用户汇总
   */
  InviteUserSummary getByUserId(Long userId);


}
