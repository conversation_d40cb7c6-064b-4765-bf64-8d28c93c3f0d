package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.RegisterInfo;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户基本信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
public interface RegisterInfoService extends BaseService<RegisterInfo> {

  /**
   * 注册信息映射集合
   *
   * @param userIds 用户id集合
   * @return 映射集合
   */
  Map<Long, RegisterInfo> mapRegisterInfo(Set<Long> userIds);

  /**
   * 查询注册信息
   */
  RegisterInfo getRegisterInfo(Long userId);
}
