package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.AuthType;
import com.red.circle.other.inner.enums.user.AuthTypeEnum;
import java.util.List;

/**
 * <p>
 * 用户认证信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-23
 */
public interface AuthTypeService extends BaseService<AuthType> {

  /**
   * 移除认证.
   *
   * @param userId 用户id
   */
  void deleteAuth(Long userId);

  /**
   * 恢复认证.
   *
   * @param userId 用户id
   */
  void restoreAuthDelByUserId(Long userId);

  /**
   * 移除认证类型-逻辑删除.
   */
  void deleteLogicAuthType(Long userId, String type);

  /**
   * 移除认证类型-物理删除.
   */
  void deleteAuthType(Long userId, String type);

  /**
   * 获取认证信息.
   *
   * @param openId 开放id
   * @return ignore
   */
  AuthType getAuthInfo(String openId);

  /**
   * 获取认证信息.
   *
   * @param type   类型
   * @param openId 开放id
   * @return ignore
   */
  AuthType getAuthInfo(String type, String openId);

  /**
   * 用户是否存在指定类型认证.
   *
   * @param userId   用户id
   * @param authType 认证类型
   * @return true 存在，false 不存在
   */
  Boolean existsAuthType(Long userId, AuthTypeEnum authType);

  /**
   * 获取认证信息
   *
   * @param userId ignore
   * @return ignore
   */
  AuthType getByUserIdOne(Long userId);

  /**
   * 用户认证类型.
   *
   * @param userId 用户id
   * @return ignore
   */
  List<String> listUserAuthType(Long userId);

  /**
   * 获得用户认证信息列表
   */
  List<AuthType> listByUserId(Long userId);

  /**
   * 修改或添加认证.
   */
  void saveUserAuthType(Long userId, String type, String openId);

  /**
   * 获取认证信息.
   *
   * @param type   类型
   * @param userId 用户id
   * @return ignore
   */
  AuthType getAuthInfoByUserId(String type, Long userId);
}
