package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxCountDetailsDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxCountDetails;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxCountDetailsService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * <p>
 * lucky-box抽奖记录详情 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxCountDetailsServiceImpl extends
    BaseServiceImpl<GameLuckyBoxCountDetailsDAO, GameLuckyBoxCountDetails> implements
    GameLuckyBoxCountDetailsService {

  @Override
  public List<GameLuckyBoxCountDetails> getCountDetails(Set<Long> ids) {
    return CollectionUtils.isEmpty(ids) ? Lists.newArrayList()
        : Optional.ofNullable(query().in(GameLuckyBoxCountDetails::getCountId, ids).list())
            .orElse(Lists.newArrayList());
  }
}
