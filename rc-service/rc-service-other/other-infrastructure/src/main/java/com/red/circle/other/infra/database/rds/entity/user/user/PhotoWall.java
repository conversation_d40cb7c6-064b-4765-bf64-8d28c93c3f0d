package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户照片墙
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_photo_wall")
public class PhotoWall extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @TableId(value = "id", type = IdType.INPUT)
  private Long id;

  /**
   * 用户ID
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 资源图片地址
   */
  @TableField("resource_url")
  private String resourceUrl;

  /**
   * 图片宽
   */
  @TableField("width")
  private Integer width;

  /**
   * 图片高
   */
  @TableField("height")
  private Integer height;

  /**
   * 序号
   */
  @TableField("sort")
  private Integer sort;

  /**
   * 违规状态
   */
  @TableField("violation")
  private String violation;

  /**
   * 机器审核标签
   */
  @TableField("label_names")
  private String labelNames;
}
