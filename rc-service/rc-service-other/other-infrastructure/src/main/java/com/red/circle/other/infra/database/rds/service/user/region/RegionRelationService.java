package com.red.circle.other.infra.database.rds.service.user.region;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.reigon.RegionRelation;
import com.red.circle.other.inner.model.cmd.user.SysRegionRelationCmd;
import com.red.circle.other.inner.model.cmd.user.SysRegionRelationQryCmd;
import com.red.circle.other.inner.enums.user.RegionRelationGroupEnum;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 区域关系
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-03
 */
public interface RegionRelationService extends BaseService<RegionRelation> {

  List<RegionRelation> listByRegionId(String regionId, String sysOrigin,
      RegionRelationGroupEnum group);

  PageResult<RegionRelation> pageRelation(SysRegionRelationQryCmd query);

  void add(SysRegionRelationCmd param);

  void update(SysRegionRelationCmd param);

  Set<Long> getRelationIdsByRegionId(String regionId, String name);
}
