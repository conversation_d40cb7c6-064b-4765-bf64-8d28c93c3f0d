package com.red.circle.other.infra.database.rds.service.sys;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.NoticeMessage;
import com.red.circle.other.inner.model.cmd.sys.SysNoticeMessageQryCmd;
import java.util.List;

/**
 * <p>
 * 系统公告通知消息 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
public interface NoticeMessageService extends BaseService<NoticeMessage> {

  /**
   * 最近一个月的活动.
   *
   * @return ignore
   */
  List<NoticeMessage> listLastOneMonth(String sysOrigin);

  /**
   * 分页查询系统通知消息
   */
  PageResult<NoticeMessage> getNoticeMessage(SysNoticeMessageQryCmd query);
}
