package com.red.circle.other.infra.database.rds.service.approval;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.approval.ApprovalUserViolationHistory;
import com.red.circle.other.inner.model.cmd.approval.ApprovalPhotoWallApprovalTableQryCmd;
import com.red.circle.other.inner.model.cmd.approval.ApprovalUserViolationHistoryQryCmd;
import com.red.circle.other.inner.model.dto.approval.ApprovalUserPhotoWallDTO;

/**
 * <p>
 * 审批用户资料违规历史记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-29
 */
public interface ApprovalUserViolationHistoryService extends
    BaseService<ApprovalUserViolationHistory> {

  /**
   * 获取最新的用户id
   *
   * @param userId ignore
   * @return ignore
   */
  String getLastNotPassAvatar(Long userId);

  /**
   * 查找历史记录
   *
   * @param query ignore
   * @param type  ignore
   * @return ignore
   */
  PageResult<ApprovalUserPhotoWallDTO> pageUserDetailsPhotoWall(
      ApprovalPhotoWallApprovalTableQryCmd query,
      String type);

  PageResult<ApprovalUserViolationHistory> pageHistory(ApprovalUserViolationHistoryQryCmd query);
}
