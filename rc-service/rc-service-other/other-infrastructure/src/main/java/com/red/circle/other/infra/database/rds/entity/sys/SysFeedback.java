package com.red.circle.other.infra.database.rds.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 意见反馈.
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_feedback")
public class SysFeedback extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;


  /**
   * 注解.
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 反馈内容.
   */
  @TableField("content")
  private String content;

  /**
   * 反馈图片地址.
   */
  @TableField("image_urls")
  private String imageUrls;

  @TableField("video_urls")
  private String videoUrls;

  /**
   * 0.未处理 1.已处理 3.待处理.
   */
  @TableField("approval_status")
  private Integer approvalStatus;

  /**
   * 审核备注.
   */
  @TableField("approval_remarks")
  private String approvalRemarks;

  /**
   * APP版本.
   */
  @TableField("app_version")
  private String appVersion;

  /**
   * 机器码.
   */
  @TableField("imei")
  private String imei;

  /**
   * 请求ip.
   */
  @TableField("ip")
  private String ip;

  /**
   * 手机型号.
   */
  @TableField("phone_model")
  private String phoneModel;

}
