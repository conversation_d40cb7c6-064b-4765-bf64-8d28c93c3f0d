package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.LuckyGiftProbability;
import com.red.circle.other.inner.model.cmd.game.GameLuckGiftProbabilityConfigQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 礼物规格概率基础配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-06-16 17:14
 */
public interface LuckyGiftProbabilityService extends BaseService<LuckyGiftProbability> {

  List<LuckyGiftProbability> listByStandardId(Long standardId);

  LuckyGiftProbability getOneByStandardIdByQuantity(Long standardId, Integer quantity);

  Map<Long, List<LuckyGiftProbability>> getLuckyGiftProbabilityMap(Set<Long> collect);

  List<LuckyGiftProbability> getLuckyGiftProbabilityByStandardId(Long id);

  Boolean deleteByStandardId(Long id);

  List<LuckyGiftProbability> getLuckyGiftProbabilityConfig(GameLuckGiftProbabilityConfigQryCmd query);
}
