package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsNobleVipAbility;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 贵族能力 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
public interface PropsNobleVipAbilityService extends BaseService<PropsNobleVipAbility> {

  /**
   * 获取贵族能力映射.
   *
   * @param ids id集合
   * @return map
   */
  Map<Long, PropsNobleVipAbility> mapByIds(Set<Long> ids);

  /**
   * 获取贵族能力.
   *
   * @param ids id集合
   * @return list
   */
  List<PropsNobleVipAbility> listByIds(List<Long> ids);

  /**
   * 获取贵族能力.
   *
   * @param ids id集合
   * @return list
   */
  List<PropsNobleVipAbility> listByIds(Collection<Long> ids);

}
