package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserActivityRewardReceiveRecord;
import com.red.circle.other.inner.model.cmd.user.UserActivityRewardReceiveRecordQryCmd;
import com.red.circle.other.inner.model.dto.user.UserActivityRewardReceiveRecordDTO;
import java.util.Map;

/**
 * <p>
 * 用户活动月度领取记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-19
 */
public interface UserActivityRewardReceiveRecordService extends
    BaseService<UserActivityRewardReceiveRecord> {

  /**
   * 当月是否存在指定的活动id.
   *
   * @param activityId 活动id
   * @return true 存在 ， false 不存在
   */
  boolean existsThisMonth(Long userId, Long activityId);

  /**
   * 获取本月领取奖励信息.
   *
   * @param userId 用户id
   * @return ignore
   */
  Map<Long, UserActivityRewardReceiveRecordDTO> mapByThisMonth(Long userId);

  /**
   * 分页查询用户活动月度领取记录
   */
  PageResult<UserActivityRewardReceiveRecord> pageUserActivityRewardReceiveRecord(
      UserActivityRewardReceiveRecordQryCmd query);
}
