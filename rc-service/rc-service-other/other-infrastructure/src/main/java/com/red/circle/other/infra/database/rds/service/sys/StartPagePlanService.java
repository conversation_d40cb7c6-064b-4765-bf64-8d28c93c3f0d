package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.StartPagePlan;
import com.red.circle.other.inner.model.cmd.sys.SysStartPagePlanQryCmd;
import java.util.List;

/**
 * <p>
 * 启动页展示图片 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-08
 */
public interface StartPagePlanService extends BaseService<StartPagePlan> {

  /**
   * 获取系统启动页.
   *
   * @param sysOrigin 系统
   * @return list
   */
  List<StartPagePlan> listBySysOrigin(String sysOrigin);

  /**
   * 分页查询APP启动页
   */
  PageResult<StartPagePlan> pageStartPage(SysStartPagePlanQryCmd query);
}
