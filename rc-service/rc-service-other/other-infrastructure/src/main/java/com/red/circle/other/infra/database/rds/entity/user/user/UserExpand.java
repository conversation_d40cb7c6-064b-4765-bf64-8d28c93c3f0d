package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户扩展信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_expand")
public class UserExpand extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 用户id.
   */
  @TableId("user_id")
  private Long userId;

  /**
   * 用户使用的语言.
   */
  @TableField("language")
  private String language;

  /**
   * 用户最新时区.
   */
  @Deprecated
  @TableField("last_zone_id")
  private String lastZoneId;

  /**
   * 解封次数.
   */
  @Deprecated
  @TableField("unlock_archive_size")
  private Integer unlockArchiveSize;

  /**
   * 最近活跃时间.
   */
  @TableField("last_active_time")
  private Timestamp lastActiveTime;

  /**
   * 是否购买过内购 0.否 1.是
   */
  @TableField("is_purchasing")
  private Boolean purchasing;

  /**
   * 个性签名.
   */
  @TableField("signature")
  private String signature;

  /**
   * 注册国家code.
   */
  @TableField("register_country_code")
  private String registerCountryCode;

}
