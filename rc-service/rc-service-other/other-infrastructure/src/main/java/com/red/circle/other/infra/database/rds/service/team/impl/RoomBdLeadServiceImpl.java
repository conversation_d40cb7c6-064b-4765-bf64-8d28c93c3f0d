package com.red.circle.other.infra.database.rds.service.team.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.team.RoomBdLeadDAO;
import com.red.circle.other.infra.database.rds.entity.team.RoomBdLead;
import com.red.circle.other.infra.database.rds.service.team.RoomBdLeadService;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * BD Lead.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-4-7
 */
@Service
public class RoomBdLeadServiceImpl extends BaseServiceImpl<RoomBdLeadDAO, RoomBdLead> implements
    RoomBdLeadService {

  @Override
  public boolean checkBdLeader(Long userId) {
    return Objects.nonNull(query()
        .eq(RoomBdLead::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }

  @Override
  public RoomBdLead getByUserId(Long userId) {

    return query()
        .eq(RoomBdLead::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public void deleteById(Long id) {
    delete().eq(RoomBdLead::getId, id).execute();
  }

}
