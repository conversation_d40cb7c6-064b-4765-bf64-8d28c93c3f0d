package com.red.circle.other.infra.database.rds.service.user.device;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.device.IpRegisterQuantity;

/**
 * <p>
 * ip注册数量 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-05-22 11:51
 */
public interface IpRegisterQuantityService extends BaseService<IpRegisterQuantity> {

  /**
   * 累计数量.
   *
   * @param ip ip地址
   */
  void incrQuantity(String ip, String sysOrigin);

  /**
   * 获取注册ip数量.
   */
  int getIpQuantity(String ip, String sysOrigin);

}
