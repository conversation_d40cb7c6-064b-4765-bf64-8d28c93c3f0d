package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLudoHistoryDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLudoHistory;
import com.red.circle.other.infra.database.rds.service.game.GameLudoHistoryService;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Ludo游戏记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-08
 */
@Service
public class GameLudoHistoryServiceImpl extends
    BaseServiceImpl<GameLudoHistoryDAO, GameLudoHistory> implements GameLudoHistoryService {

  @Override
  public List<GameLudoHistory> flowSelective(String sysOrigin, Long roomId, Long lastId) {
    return query()
        .eq(StringUtils.isNotBlank(sysOrigin), GameLudoHistory::getSysOrigin, sysOrigin)
        .eq(Objects.nonNull(roomId), GameLudoHistory::getRoomId, roomId)
        .lt(Objects.nonNull(lastId), GameLudoHistory::getId, lastId)
        .orderByDesc(GameLudoHistory::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }
}
