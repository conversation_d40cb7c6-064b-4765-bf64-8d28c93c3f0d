package com.red.circle.other.infra.database.rds.service.user.user;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteUserRegister;
import com.red.circle.other.infra.database.rds.query.user.InviteUserQry;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 用户邀请用户注册信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-12
 */
public interface InviteUserRegisterService extends BaseService<InviteUserRegister> {

  /**
   * 验证邀请用户是否存在.
   *
   * @param inviteUserId 被邀请用户
   * @return true/false
   */
  boolean existsByInviteUserId(Long inviteUserId);

  /**
   * 验证邀请用户是否存在.
   *
   * @param deviceId 设备编号
   * @return true/false
   */
  boolean existsByDeviceId(String deviceId);

  /**
   * 获取被邀请人用户id
   *
   * @param inviteUserId 被邀请用户
   * @return 用户id
   */
  Long getUserIdByInviteUserId(Long inviteUserId);

  /**
   * 获取被邀请人用户id
   *
   * @param inviteUserId 被邀请用户
   * @return obj
   */
  InviteUserRegister getByInviteUserId(Long inviteUserId);

  /**
   * 用户邀请id记录.
   *
   * @param sysOrigin 系统平台
   * @param userId    用户id
   * @param lastId    最后一条记录id
   * @return ignore
   */
  List<Long> listInviteRecord(SysOriginPlatformEnum sysOrigin, Long userId, Long lastId);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   * @param userId     邀请人用户id
   * @return 被邀请人信息
   */
  List<InviteUserRegister> pageByCommissionDesc(Integer pageNumber, Long userId);

  /**
   * 累计佣金.
   *
   * @param inviteUserId 被邀请用户ID
   * @param commission   佣金
   */
  void incrCommission(Long inviteUserId, BigDecimal commission);

  /**
   * 分页查询用户邀请用户注册信息
   */
  PageResult<InviteUserRegister> pageQuery(
      IPage<InviteUserRegister> page, InviteUserQry query);
}
