package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * SVIP用户.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_svip_identity")
public class SVipIdentity extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户ID.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 等级(1-5)， 0则不是SVip.
   */
  @TableField("level")
  private Integer level;

  /**
   * 积分.
   */
  @TableField("integral")
  private Long integral;

  /**
   * 开始时间.
   */
  @TableField("start_time")
  private Timestamp startTime;

  /**
   * 过期时间.
   */
  @TableField("expired_time")
  private Timestamp expiredTime;


}
