package com.red.circle.other.infra.database.rds.service.live;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.live.RoomMikeNum;
import java.util.List;

/**
 * 金币购买房间麦数量服务
 *
 * <AUTHOR> on 2023/3/28 17:12
 */
public interface RoomMikeNumService extends BaseService<RoomMikeNum> {


  /**
   * 根据用户id查询购买过的麦位数量
   *
   * @param userId 用户id
   */
  List<RoomMikeNum> listByUserIdRoomMikeNumRecord(Long userId);

  /**
   * 校验是否购买过所选麦位数量
   *
   * @param userId      用户id
   * @param mikeNumType 购买麦位数量的类型
   */
  boolean isPurchaseMikeNum(Long userId, String mikeNumType);
}
