package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.LogoutApplyDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.LogoutApply;
import com.red.circle.other.infra.database.rds.service.user.user.LogoutApplyService;
import com.red.circle.other.inner.model.cmd.user.UserLogoutApplyQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户注销web页面申请 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-09-18 14:46
 */
@Service
public class LogoutApplyServiceImpl extends BaseServiceImpl<LogoutApplyDAO, LogoutApply> implements
    LogoutApplyService {

  @Override
  public Boolean getByUserAccount(String userAccount, String logoutType) {
    return Objects.nonNull(query().eq(LogoutApply::getUserAccount, userAccount)
        .eq(LogoutApply::getLogoutType, logoutType)
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }

  @Override
  public PageResult<LogoutApply> pageUserLogoutApply(UserLogoutApplyQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getUserAccount()), LogoutApply::getUserAccount,
            query.getUserAccount())
        .eq(StringUtils.isNotBlank(query.getLogoutType()), LogoutApply::getLogoutType,
            query.getLogoutType())
        .ge(Objects.nonNull(query.getStartTime()), LogoutApply::getCreateTime,
            query.startTimeToLocalDateTime())
        .le(Objects.nonNull(query.getEndTime()), LogoutApply::getCreateTime,
            query.endTimeToLocalDateTime())
        .orderByDesc(LogoutApply::getCreateTime)
        .page(query.getPageQuery());
  }
}
