package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicCommentLike;
import java.util.Collection;
import java.util.Map;

/**
 * <p>
 * 动态-朋友圈评论点赞 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface DynamicCommentLikeService extends BaseService<DynamicCommentLike> {

  /**
   * 根据评论ids与用户ID获得点赞信息
   *
   * @param commentIds 评论id
   * @param userId     用户id
   * @return true 已点赞
   */
  Map<Long, Boolean> mapByCommentIdsByUserId(Collection<Long> commentIds, Long userId);

  /**
   * 是否存在数据
   *
   * @param contentId  内容ID
   * @param commentId  评论ID
   * @param createUser 创建人
   * @return true存在
   */
  Boolean exist(Long contentId, Long commentId, Long createUser);

  /**
   * 删除点赞记录
   *
   * @param contentId  内容ID
   * @param commentId  评论ID
   * @param createUser 创建人
   */
  void delete(Long contentId, Long commentId, Long createUser);

  /**
   * 根据根评论id删除评论
   *
   * @param rootCommentId 根评论id
   */
  void deleteByRootCommentId(Long rootCommentId);

  /**
   * 根据根评论id查询子评论点赞数量
   *
   * @param rootCommentId 根评论id
   * @param notUserId     过滤该id
   * @return 子评论点赞数量
   */
  Integer getNumberByRootCommentIdByNotUserId(Long rootCommentId, Long notUserId);


}
