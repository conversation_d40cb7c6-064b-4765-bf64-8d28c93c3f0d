package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.StartPageUser;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 启动页展示用户 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
public interface StartPageUserService extends BaseService<StartPageUser> {

  /**
   * 根据type分组.
   *
   * @param sysOrigin 来源系统
   * @param types     类型
   * @return map
   */
  Map<String, List<StartPageUser>> mapGroupType(String sysOrigin, Set<String> types);
}
