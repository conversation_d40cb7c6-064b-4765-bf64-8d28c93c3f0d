package com.red.circle.other.infra.database.rds.service.approval;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.approval.ApprovalDynamicContent;
import com.red.circle.other.inner.model.cmd.approval.ApprovalDynamicQryCmd;
import java.util.Set;

/**
 * <p>
 * 审核动态内容 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-24
 */
public interface ApprovalDynamicContentService extends BaseService<ApprovalDynamicContent> {

  PageResult<ApprovalDynamicContent> pageApprovalDynamic(ApprovalDynamicQryCmd query);

  void approvalPass(Set<Long> ids);

  void approvalNotPass(Set<Long> ids);

  void deleteByDynamicId(Long dynamicId);
}
