package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxCountDetails;
import java.util.List;
import java.util.Set;


/**
 * <p>
 * lucky-box抽奖记录详情 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxCountDetailsService extends BaseService<GameLuckyBoxCountDetails> {

  List<GameLuckyBoxCountDetails> getCountDetails(Set<Long> collect);
}
