package com.red.circle.other.infra.database.rds.service.user.device;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.LatestMobileDevice;
import com.red.circle.other.inner.model.cmd.user.device.PageLatestMobileDeviceQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户最新设备信息 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
public interface LatestMobileDeviceService extends BaseService<LatestMobileDevice> {

  /**
   * 获取用户设备号.
   *
   * @param userId 用户id
   * @return 设备编码
   */
  String getImeiByUserId(Long userId);

  /**
   * 根据用户id查询.
   */
  LatestMobileDevice getByUserId(Long userId);
  /**
   * 根据用户id查询.
   */
  List<LatestMobileDevice> getByUserId(Set<Long> userIds);

  /**
   * 获取设备注册用户.
   */
  List<Long> listUserIdByImei(String imei, String sysOrigin);

  /**
   * 获取设备号注册信息
   */
  List<LatestMobileDevice> listByImei(String imei, String sysOrigin);

  /**
   * 保存最新设备信息.
   *
   * @param latestMobileDevice ignore
   */
  void saveLatestMobileDevice(LatestMobileDevice latestMobileDevice);

  /**
   * 获得用户最新语言.
   *
   * @param userId 用户id
   * @return 语言
   */
  String getLatestLanguage(Long userId);

  /**
   * 根据用户id获得关联ip与设备的用户id集合.
   *
   * @param userId 用户ID
   * @return 用户ID
   */
  Set<Long> getIpOrImeiUserId(Long userId);

  /**
   * 根据用户id获得关联ip,设备信息.
   */
  Set<String> getImeiByUserIp(Long userId);

  /**
   * 根据用户id 设备号.判断是否在新设备第一次登录
   */
  Boolean isFirstLogin(Long id, String imei);

  /**
   * 获取设备最新信息分页结果
   */
  PageResult<LatestMobileDevice> pageLatestMobileDevice(
      PageLatestMobileDeviceQryCmd query);

  /**
   * 设备号
   *
   * @param userIds 用户
   * @return 设备信息
   */
  Map<Long, String> mapImeiByIds(Set<Long> userIds);

  /**
   * 根据IP查询数量.
   */
  Long getCountByIp(String ip,String sysOrigin);

  /**
   * 根据设备查询数量.
   */
  Long getCountByImei(String imei,String sysOrigin);
  /**
   * 根据设备+ip查询数量.
   */
  Long getCountByImeiAndIp(String imei,String ip ,String sysOrigin);

  Set<Long> getImeiUserId(Long userId);

}
