package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicComment;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 动态-朋友圈评论 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface DynamicCommentService extends BaseService<DynamicComment> {


  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   * @param contentId  动态内容ID
   */
  List<DynamicComment> pageByTimeDesc(Integer pageNumber, Long contentId);

  /**
   * 获得子评论数
   *
   * @param rootCommentId 根评论id
   * @param notUserId     过滤该用户id
   * @return 数量
   */
  Integer getNumberByRootIdByNotUserId(Long rootCommentId, Long notUserId);

  /**
   * 获得子评论
   *
   * @param rootCommentId 根评论id
   * @param notUserId     过滤该用户id
   * @return list
   */
  List<DynamicComment> listNumberByRootIdByNotUserId(Long rootCommentId, Long notUserId);

  /**
   * 获得子评论数
   *
   * @param rootCommentId 根评论id
   * @return 数量
   */
  Integer getNumberByRootId(Long rootCommentId);

  /**
   * 动态评论数量
   *
   * @param dynamicId 动态id
   * @return 数量
   */
  Integer getNumberByDynamicId(Long dynamicId);

  /**
   * 根据根评论id删除评论
   *
   * @param rootCommentId 根评论id
   */
  void deleteByRootCommentId(Long rootCommentId);

  /**
   * 根据评论id删除评论
   *
   * @param id 评论id
   */
  void deleteById(Long id);

  /**
   * 根据动态ID获得动态评论数量
   *
   * @param contentIds 动态内容ID
   */
  Map<Long, Integer> mapCommentQuantity(Collection<Long> contentIds);

}
