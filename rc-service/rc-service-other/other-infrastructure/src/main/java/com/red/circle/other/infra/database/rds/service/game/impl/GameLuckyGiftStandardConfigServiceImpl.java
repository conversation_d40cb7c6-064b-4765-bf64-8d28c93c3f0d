package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyGiftStandardConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyGiftStandardConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyGiftStandardConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckGiftStandardConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 礼物规格配置 服务类.
 *
 * <AUTHOR> on 2024/1/24
 */
@Service
@RequiredArgsConstructor
public class GameLuckyGiftStandardConfigServiceImpl extends
    BaseServiceImpl<GameLuckyGiftStandardConfigDAO, GameLuckyGiftStandardConfig> implements
    GameLuckyGiftStandardConfigService {

  @Override
  public List<GameLuckyGiftStandardConfig> getLuckyGiftStandard(String sysOrigin) {
    return query()
        .eq(GameLuckyGiftStandardConfig::getSysOrigin, sysOrigin)
        .last(PageConstant.formatLimit(500))
        .list();
  }

  @Override
  public PageResult<GameLuckyGiftStandardConfig> getStandardConfig(
      GameLuckGiftStandardConfigQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameLuckyGiftStandardConfig::getSysOrigin,
            query.getSysOrigin())
        .like(StringUtils.isNotBlank(query.getRemarks()), GameLuckyGiftStandardConfig::getRemarks,
            query.getRemarks())
        .orderByDesc(GameLuckyGiftStandardConfig::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public void deleteStandardConfig(Long id, Long userId) {
    delete().eq(GameLuckyGiftStandardConfig::getId, id).execute();
  }
}
