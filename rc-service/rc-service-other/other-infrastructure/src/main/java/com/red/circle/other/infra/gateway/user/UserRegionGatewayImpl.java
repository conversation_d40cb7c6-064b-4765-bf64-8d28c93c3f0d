package com.red.circle.other.infra.gateway.user;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.domain.gateway.user.ability.UserRegionGateway;
import com.red.circle.other.domain.model.user.UserProfile;
import com.red.circle.other.domain.model.user.ability.RegionConfig;
import com.red.circle.other.infra.convertor.user.UserRegionInfraConvertor;
import com.red.circle.other.infra.database.cache.service.user.SysRegionCacheService;
import com.red.circle.other.infra.database.cache.service.user.UserRegionCacheService;
import com.red.circle.other.infra.database.mongo.entity.team.team.TeamMember;
import com.red.circle.other.infra.database.mongo.entity.team.team.TeamProfile;
import com.red.circle.other.infra.database.mongo.entity.user.region.SysRegionAssistConfig;
import com.red.circle.other.infra.database.mongo.entity.user.region.SysRegionConfig;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamMemberService;
import com.red.circle.other.infra.database.mongo.service.team.team.TeamProfileService;
import com.red.circle.other.infra.database.mongo.service.user.region.SysRegionAssistConfigService;
import com.red.circle.other.infra.database.mongo.service.user.region.SysRegionConfigService;
import com.red.circle.other.inner.enums.user.RegionAssistTypeEnum;
import com.red.circle.other.inner.model.dto.agency.agency.UserTeamRegionDTO;
import com.red.circle.other.inner.model.dto.user.reigon.HostApplyQuitTeamCheckDTO;
import com.red.circle.other.inner.model.dto.user.reigon.SysRegionAssistProbabilityDTO;
import com.red.circle.other.inner.model.dto.user.reigon.UserRegionDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.regex.RegexConstant;
import com.red.circle.tool.core.text.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户区域 实现.
 *
 * <AUTHOR> on 2023/12/17
 */
@Log4j2
@Component
@RequiredArgsConstructor
public class UserRegionGatewayImpl implements UserRegionGateway {

  private final TeamMemberService teamMemberService;
  private final TeamProfileService teamProfileService;
  private final UserProfileGateway userProfileGateway;
  private final SysRegionCacheService sysRegionCacheService;
  private final SysRegionConfigService sysRegionConfigService;
  private final UserRegionInfraConvertor userRegionInfraConvertor;
  private final SysRegionAssistConfigService sysRegionAssistConfigService;
  private final UserRegionCacheService userRegionCacheService;

  /**
   * key=用户id, value=区域code 获取一组区域code.
   */
  @Override
  public Map<Long, String> mapRegionCode(Set<Long> userIds) {

    List<UserRegionDTO> userRegions = matchUserRegionDefaultRule(userIds);
    if (CollectionUtils.isEmpty(userRegions)) {
      return CollectionUtils.newHashMap();
    }

    return userRegions.stream()
        .collect(Collectors.toMap(UserRegionDTO::getUserId, UserRegionDTO::getRegionCode));
  }

  @Override
  public boolean checkEqRegion(Long userId, Long compareUserId) {
    Map<Long, String> userRegions = mapRegionCode(
        Set.of(userId, compareUserId));

    return Objects.equals(userRegions.get(userId), userRegions.get(compareUserId));
  }

  /**
   * 获取用户区域编号.
   */
  @Override
  public String getRegionCode(Long userId) {
    return Optional.ofNullable(getUserRegion(userId))
        .map(UserRegionDTO::getRegionCode)
        .orElse("OTHER");
  }

  /**
   * 获取用户区域ID.
   */
  @Override
  public String getRegionId(Long userId) {
    return Optional.ofNullable(getUserRegion(userId))
        .map(UserRegionDTO::getRegionId)
        .orElse(null);
  }

  /**
   * 匹配用户分区.
   */
  @Override
  public UserRegionDTO getUserRegion(Long userId) {
    List<UserRegionDTO> userRegions = matchUserRegionDefaultRule(Set.of(userId));
    log.info("getUserRegion userId:{}, userRegions:{}", userId, userRegions);
    if (CollectionUtils.isEmpty(userRegions)) {
      return null;
    }

    return userRegions.get(0);
  }

  /**
   * 匹配用户分区.
   */
  private List<UserRegionDTO> matchUserRegionDefaultRule(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return CollectionUtils.newArrayList();
    }

    List<UserRegionDTO> userRegions = CollectionUtils.newArrayList();

    Set<Long> continueMatchRegionUserIds = new HashSet<>(userIds);

    // 1. 主播代理团队区域
    List<UserTeamRegionDTO> userTeamRegions = listUserTeamRegion(userIds);
    if (CollectionUtils.isNotEmpty(userTeamRegions)
        && Objects.equals(userTeamRegions.size(), userIds.size())) {

      Map<String, SysRegionConfig> regionConfigMap = sysRegionConfigService.mapAvailable(
          userTeamRegions.stream().map(UserTeamRegionDTO::getRegionId).collect(Collectors.toSet())
      );

      if (CollectionUtils.isNotEmpty(regionConfigMap)) {
        userRegions.addAll(
            userTeamRegions.stream().map(conf -> {
              SysRegionConfig regionConfig = regionConfigMap.get(conf.getRegionId());

              if (Objects.isNull(regionConfig)) {
                return null;
              }

              return new UserRegionDTO()
                  .setUserId(conf.getUserId())
                  .setSysOrigin(conf.getSysOrigin())
                  .setRegionId(conf.getRegionId())
                  .setRegionCode(regionConfig.getRegionCode())
                  .setMetadata(regionConfig.getMetadata());
            }).filter(Objects::nonNull).toList()
        );
      }

      continueMatchRegionUserIds.removeAll(
          userRegions.stream().map(UserRegionDTO::getUserId).collect(Collectors.toSet())
      );
    }

    if (CollectionUtils.isEmpty(continueMatchRegionUserIds)) {
      return userRegions;
    }

    // 2. 配置规则匹配区域
    List<UserProfile> userProfiles = userProfileGateway.listByUserIds(continueMatchRegionUserIds);
    log.info("userProfiles: {}", userProfiles);

    if (CollectionUtils.isEmpty(userProfiles)) {
      return userRegions;
    }

    // 各系统区域配置
    Map<String, List<SysRegionConfig>> sysOriginRegionConfigMap = mapOriginSysRegionConfigs(userProfiles);
    for (UserProfile userProfile : userProfiles) {

      List<SysRegionConfig> configs = sysOriginRegionConfigMap.get(userProfile.getOriginSys());

      if (CollectionUtils.isEmpty(configs)) {
        continue;
      }

      // 2.1 国家匹配
      if (StringUtils.isNotBlank(userProfile.getCountryCode())) {
        SysRegionConfig regionConfig = configs.stream()
            .filter(region -> StringUtils.containsIgnoreCase(region.getCountryCodes(),
                userProfile.getCountryCode())
            ).findFirst().orElse(null);
        UserRegionDTO userRegion = createUserRegionDTO(userProfile, regionConfig);
        if (Objects.nonNull(userRegion)) {
          userRegions.add(userRegion);
          continue;
        }
      }

      // 2.2 语言匹配
      String langeCode = userProfileGateway.getLanguage(userProfile.getId());
      if (StringUtils.isNotBlank(langeCode)) {
        SysRegionConfig regionConfig = configs.stream()
            .filter(region -> StringUtils.containsIgnoreCase(region.getLangeCodes(),
                langeCode.toLowerCase())
            ).findFirst().orElse(null);
        UserRegionDTO userRegion = createUserRegionDTO(userProfile,
            regionConfig);
        if (Objects.nonNull(userRegion)) {
          userRegions.add(userRegion);
          continue;
        }
      }

      // 填充默认值
      UserRegionDTO userRegion = createUserRegionDTO(userProfile,
          getSysOriginDefaultRegion(configs));
      if (Objects.nonNull(userRegion)) {
        userRegions.add(userRegion);
      }
    }
    return userRegions;
  }

  private List<UserTeamRegionDTO> listUserTeamRegion(Set<Long> userIds) {
    List<TeamMember> teamMembers = teamMemberService.listByMemberIds(userIds);

    if (CollectionUtils.isEmpty(teamMembers)) {
      return CollectionUtils.newArrayList();
    }

    Map<Long, TeamProfile> teamProfilesMap = teamProfileService.mapProfileByIds(
        teamMembers.stream().map(TeamMember::getTeamId).collect(Collectors.toSet()));

    if (CollectionUtils.isEmpty(teamProfilesMap)) {
      return CollectionUtils.newArrayList();
    }

    return teamMembers.stream().map(member -> new UserTeamRegionDTO()
        .setUserId(member.getMemberId())
        .setSysOrigin(member.getSysOrigin())
        .setTeamId(member.getTeamId())
        .setRegionId(teamProfilesMap.get(member.getTeamId()).getRegion())
    ).toList();
  }

  private UserRegionDTO createUserRegionDTO(UserProfile userProfile,
      SysRegionConfig regionConfig) {
    if (Objects.nonNull(regionConfig)) {
      return new UserRegionDTO()
          .setUserId(userProfile.getId())
          .setSysOrigin(userProfile.getOriginSys())
          .setRegionId(regionConfig.getId())
          .setRegionCode(regionConfig.getRegionCode())
          .setMetadata(regionConfig.getMetadata());
    }

    return null;
  }

  private Map<String, List<SysRegionConfig>> mapOriginSysRegionConfigs(List<UserProfile> userProfiles) {

    Set<String> allSysOrigin = userProfiles.stream().map(UserProfile::getOriginSys).collect(Collectors.toSet());

    Map<String, List<SysRegionConfig>> sysOriginRegionConf = CollectionUtils.newHashMap();
    for (String sysOrigin : allSysOrigin) {
      List<SysRegionConfig> configs = Optional.ofNullable(listSysRegionConfig(sysOrigin))
          .map(list -> list.stream().peek(region -> {
                region.setRegionCode(StringUtils.isBlankOrElse(region.getRegionCode(), StringUtils.EMPTY));
                region.setLangeCodes(StringUtils.isBlankOrElse(region.getLangeCodes(), StringUtils.EMPTY));
              }).toList()
          ).orElseGet(CollectionUtils::newArrayList);
      sysOriginRegionConf.put(sysOrigin, configs);
    }
    return sysOriginRegionConf;
  }

  /**
   * 检查两个用户在业务分区是否一致.
   * <p>在一些特效情况下， 运营对当前区域进行整合， 如： 阿拉伯+土耳其可以互相访问</p>
   *
   * @return true 相同业务区域，false 不同业务区域
   */
  @Override
  public boolean checkBusinessRegion(Long userId, Long compareUserId) {

    if (Objects.isNull(userId) || Objects.isNull(compareUserId)) {
      return false;
    }

    if (Objects.equals(userId, compareUserId)) {
      return true;
    }

    String userIdRegions = userRegionCacheService.getUserRegions(userId);
    String compareUserIdRegions = userRegionCacheService.getUserRegions(compareUserId);
    log.warn("redis user region code: {}|{}|{}|{}", userId, userIdRegions, compareUserId, compareUserIdRegions);
    // 两个用户不为空就对比一下
    if (Objects.nonNull(userIdRegions) && Objects.nonNull(compareUserIdRegions)) {
      return checkBusinessRegionCode(userIdRegions, compareUserIdRegions);
    }

    // 两个用户的区域都不存在
    if (Objects.isNull(userIdRegions) && Objects.isNull(compareUserIdRegions)) {
      Map<Long, String> userRegions = mapRegionCode(Set.of(userId, compareUserId));
      log.info("userRegions: {}", userRegions);
      String regionOne = userRegions.get(userId);
      String regionTwo = userRegions.get(compareUserId);
      userRegionCacheService.add(userRegions);
      return checkBusinessRegionCode(regionOne, regionTwo);
    }
    // 进入房间用户不存在的情况
    Map<Long, String> userRegions = new HashMap<>();
    if (Objects.isNull(userIdRegions)) {
      Map<Long, String> userIdRegionsMap = mapRegionCode(Set.of(userId));
      userRegions.putAll(userIdRegionsMap);
      userRegionCacheService.add(userIdRegionsMap);
    } else {
      userRegions.put(userId, userIdRegions);
    }
    // 目标房间用户不存在的情况
    if (Objects.isNull(compareUserIdRegions)) {
      Map<Long, String> compareUserIdRegionsMap = mapRegionCode(Set.of(compareUserId));
      userRegions.putAll(compareUserIdRegionsMap);
      userRegionCacheService.add(compareUserIdRegionsMap);
    } else {
      userRegions.put(compareUserId, compareUserIdRegions);
    }
    log.info("userRegions: {}", userRegions);
    String regionOne = userRegions.get(userId);
    String regionTwo = userRegions.get(compareUserId);
    userRegionCacheService.add(userRegions);

    return checkBusinessRegionCode(regionOne, regionTwo);
  }

  /**
   * 检查两个code在业务分区是否一致.
   * <p>在一些特效情况下， 运营对当前区域进行整合， 如： 阿拉伯+土耳其可以互相访问</p>
   *
   * @return true 相同业务区域，false 不同业务区域
   */
  @Override
  public boolean checkBusinessRegionCode(String regionOne, String regionTwo) {
    // 区域相同
    if (equalsRegion(regionOne, regionTwo)) {
      return true;
    }

    // 互通区域1 = 阿拉伯 and 土耳其
    List<String> arRegions = List.of("AR", "TR");
    boolean isOnAr = equalsRegion(arRegions, regionOne);
    boolean isTwoAr = equalsRegion(arRegions, regionTwo);
    if (isOnAr && isTwoAr) {
      return true;
    }

    // 互通区域2 = 不是阿拉伯 and 不是土耳其
    // 2024.08.12 孟加拉，印度，测试区，单独隔离ID 印尼1区 IA印尼2区
    List<String> notPassRegions = List.of("AR", "TR", "BD", "Test", "IN", "UK", "ID", "ID-02", "AR-02");
    boolean isOneNotPassRegions = equalsRegion(notPassRegions, regionOne);
    boolean isTwoNotPassRegions = equalsRegion(notPassRegions, regionTwo);
    if (!isOneNotPassRegions && !isTwoNotPassRegions) {
      return true;
    }

    return false;
  }

  @Override
  public boolean checkOpenDailyAutoSalary(String regionId, String sysOrigin) {
    if (StringUtils.isBlank(regionId) || StringUtils.isBlank(sysOrigin)) {
      return Boolean.FALSE;
    }

    List<SysRegionConfig> sysRegionConfigs = sysRegionCacheService.getCache(sysOrigin);
    if (CollectionUtils.isEmpty(sysRegionConfigs)) {
      return Boolean.FALSE;
    }

    Map<String, SysRegionConfig> regionMap = sysRegionConfigs.stream()
        .collect(Collectors.toMap(SysRegionConfig::getId, v -> v));
    if (CollectionUtils.isEmpty(regionMap)) {
      return Boolean.FALSE;
    }

    SysRegionConfig sysRegionConfig = regionMap.get(regionId);
    if (Objects.isNull(sysRegionConfig)) {
      return Boolean.FALSE;
    }

    if (CollectionUtils.isEmpty(sysRegionConfig.getMetadata())) {
      return Boolean.FALSE;
    }

    return Objects.equals(sysRegionConfig.getMetadata().get("openDailyAutoSalary"), "true");
  }

  /**
   * 获得系统中的默认区域.
   */
  private SysRegionConfig getSysOriginDefaultRegion(List<SysRegionConfig> configs) {
    if (CollectionUtils.isEmpty(configs)) {
      return null;
    }
    return configs.stream()
        .filter(region -> StringUtils.equalsIgnoreCase("OTHER", region.getRegionCode())).findFirst()
        .orElse(null);
  }

  /**
   * 获得系统中的默认区域.
   */
  private SysRegionConfig getSysOriginDefaultRegion(String sysOrigin) {
    return getSysOriginDefaultRegion(listSysRegionConfig(sysOrigin));
  }

  private List<SysRegionConfig> listSysRegionConfig(String sysOrigin) {
    return sysRegionCacheService.getCache(sysOrigin);
  }


  /**
   * 剩余目标兑换比例.
   */
  @Override
  public BigDecimal getResidueTargetExchangeRatio(String regionId) {

    if (StringUtils.isBlank(regionId)) {
      return null;
    }

    SysRegionAssistConfig assistConfig = sysRegionAssistConfigService
        .getRegionAssistConfig(RegionAssistTypeEnum.RESIDUE_TARGET_EXCHANGE_PROPORTION,
            regionId);

    if (Objects.isNull(assistConfig) || StringUtils.isBlank(assistConfig.getData())) {
      return null;
    }

    double data = Double.parseDouble(assistConfig.getData());
    if (data <= 0) {
      return null;
    }

    return BigDecimal.valueOf(data);

  }

  /**
   * 接收他人礼物获得金币比例.
   */
  @Override
  public BigDecimal getAssistGiftToOtherGoldRatio(String regionId) {

    BigDecimal defaultRatio = BigDecimal.valueOf(0.1);
    if (StringUtils.isBlank(regionId)) {
      return defaultRatio;
    }

    return getAssistRatio(sysRegionAssistConfigService
            .getRegionAssistConfig(RegionAssistTypeEnum.GIFT_TO_OTHER_GOLD_RATIO, regionId),
        defaultRatio);
  }


  /**
   * 接收自己礼物获得金币比例.
   */
  @Override
  public BigDecimal getAssistGiftToOwnGoldRatio(String regionId) {
    BigDecimal defaultRatio = BigDecimal.valueOf(0.1);
    if (StringUtils.isBlank(regionId)) {
      return defaultRatio;
    }

    return getAssistRatio(sysRegionAssistConfigService
                    .getRegionAssistConfig(RegionAssistTypeEnum.GIFT_TO_OWN_GOLD_RATIO, regionId),
            defaultRatio);
  }

  /**
   * 接收他人礼物获得金币比例.代理
   */
  @Override
  public BigDecimal getAssistGiftToOtherGoldRatioAgency(String regionId) {

    BigDecimal defaultRatio = BigDecimal.valueOf(0.1);
    if (StringUtils.isBlank(regionId)) {
      return defaultRatio;
    }

    return getAssistRatio(sysRegionAssistConfigService
                    .getRegionAssistConfig(RegionAssistTypeEnum.GIFT_TO_OTHER_GOLD_RATIO_AGENCY, regionId),
            defaultRatio);
  }


  /**
   * 接收自己礼物获得金币比例.
   */
  @Override
  public BigDecimal getAssistGiftToOwnGoldRatioAgency(String regionId) {
    BigDecimal defaultRatio = BigDecimal.valueOf(0.1);
    if (StringUtils.isBlank(regionId)) {
      return defaultRatio;
    }

    return getAssistRatio(sysRegionAssistConfigService
            .getRegionAssistConfig(RegionAssistTypeEnum.GIFT_TO_OWN_GOLD_RATIO_AGENCY, regionId),
        defaultRatio);
  }

  /**
   * 接收礼物获得目标比例.
   */
  @Override
  public BigDecimal getAssistGiftTargetRatio(String regionId) {
    BigDecimal defaultRatio = BigDecimal.valueOf(1);
    if (StringUtils.isBlank(regionId)) {
      return defaultRatio;
    }

    return getAssistRatio(sysRegionAssistConfigService.getRegionAssistConfig(RegionAssistTypeEnum.GIFT_TARGET_RATIO, regionId), defaultRatio);
  }


  /**
   * 房间贡献活动奖励比例.
   */
  @Override
  public ImmutablePair<String, BigDecimal> getAssistRoomContributionActivityRatio(Long userId) {
    BigDecimal defaultRatio = BigDecimal.valueOf(0.05);
    String regionId = getRegionId(userId);
    if (StringUtils.isBlank(regionId)) {
      return ImmutablePair.of("", defaultRatio);
    }

    SysRegionAssistConfig assistConfig = sysRegionAssistConfigService
        .getRegionAssistConfig(RegionAssistTypeEnum.ROOM_CONTRIBUTION_ACTIVITY_RATIO,
            regionId);
    if (Objects.isNull(assistConfig)) {
      return ImmutablePair.of("", defaultRatio);
    }

    return ImmutablePair.of(assistConfig.getImgUrl(), getAssistRatio(assistConfig, defaultRatio));
  }

  @Override
  public SysRegionAssistProbabilityDTO getAssistRoomContributionActivityRatioDTO(Long userId) {
    ImmutablePair<String, BigDecimal> immutablePair = getAssistRoomContributionActivityRatio(
        userId);
    if (Objects.isNull(immutablePair)) {
      return null;
    }
    SysRegionAssistProbabilityDTO probabilityDTO = new SysRegionAssistProbabilityDTO();
    probabilityDTO.setRatio(immutablePair.getRight());
    probabilityDTO.setImgUrl(immutablePair.getLeft());
    return probabilityDTO;
  }


  private BigDecimal getAssistRatio(SysRegionAssistConfig assistConfig,
      BigDecimal defaultRatio) {
    if (Objects.isNull(assistConfig)) {
      return defaultRatio;
    }

    if (StringUtils.isBlank(assistConfig.getData())) {
      return defaultRatio;
    }

    if (!assistConfig.getData().matches(RegexConstant.NUMBER)) {
      return defaultRatio;
    }

    long data = Long.parseLong(assistConfig.getData());
    if (data <= 0) {
      return BigDecimal.ZERO;
    }

    return BigDecimal.valueOf(data).divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN);
  }


  private boolean equalsRegion(String regionCodeOne, String regionCodeTwo) {
    return StringUtils.equalsIgnoreCase(regionCodeOne, regionCodeTwo);
  }

  private boolean equalsRegion(List<String> regions, String regionCode) {
    if (CollectionUtils.isEmpty(regions) || StringUtils.isBlank(regionCode)) {
      return Boolean.FALSE;
    }

    for (String region : regions) {
      if (equalsRegion(region, regionCode)) {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }

  /**
   * 获取一批用户区域配置.
   *
   * @param sysOrigin 可选，不传递内部识别用户系统
   * @param userIds   被查询的用户
   */
  @Override
  public Map<Long, RegionConfig> mapUserRegionConfig(String sysOrigin, Set<Long> userIds) {

    if (CollectionUtils.isEmpty(userIds)) {
      return Maps.newHashMap();
    }

    List<TeamMember> teamMembers = teamMemberService.listByMemberIds(userIds);

    if (CollectionUtils.isEmpty(teamMembers)) {
      return userRegionInfraConvertor.toMapRegionConfig(
          matchRegion(sysOrigin, userIds)
      );
    }

    Map<Long, RegionConfig> resultMap = Maps.newHashMap();
    Set<Long> nextProcessUserIds = Sets.newHashSet();

    Map<Long, SysRegionConfig> sysRegionConfigMap = mapTeamMemberRegion(
        teamMembers.stream().map(TeamMember::getTeamId).collect(
            Collectors.toSet()));

    Map<Long, SysRegionConfig> userRegionConfigMap = Maps.newHashMap();

    teamMembers.forEach(member -> userRegionConfigMap.put(member.getMemberId(),
        sysRegionConfigMap.get(member.getTeamId())));

    userIds.forEach(userId -> {
      RegionConfig sysRegionConfig = userRegionInfraConvertor.toRegionConfig(
          userRegionConfigMap.get(userId));
      if (Objects.isNull(sysRegionConfig)) {
        nextProcessUserIds.add(userId);
        return;
      }
      resultMap.put(userId, sysRegionConfig);
    });

    if (CollectionUtils.isEmpty(nextProcessUserIds)) {
      return resultMap;
    }

    // 获取存在团队成员
    Set<Long> existsTeamUserIds = teamMembers.stream().map(TeamMember::getMemberId)
        .collect(Collectors.toSet());

    nextProcessUserIds.addAll(userIds.stream()
        .filter(userId -> !existsTeamUserIds.contains(userId)).collect(Collectors.toSet()));

    resultMap.putAll(userRegionInfraConvertor.toMapRegionConfig(
            matchRegion(sysOrigin, nextProcessUserIds)
        )
    );
    return resultMap;
  }

  /**
   * 获取团队成员区域.
   */
  private Map<Long, SysRegionConfig> mapTeamMemberRegion(Set<Long> teamIds) {
    List<TeamProfile> teamProfiles = teamProfileService.listByIds(teamIds);

    if (CollectionUtils.isEmpty(teamProfiles)) {
      return Maps.newHashMap();
    }
    List<SysRegionConfig> sysRegionConfigs = sysRegionConfigService.listAvailable(
        teamProfiles.stream().map(TeamProfile::getRegion).collect(Collectors.toSet())
    );
    if (CollectionUtils.isEmpty(sysRegionConfigs)) {
      return Maps.newHashMap();
    }

    Map<String, SysRegionConfig> sysRegionConfigMap = sysRegionConfigs.stream()
        .collect(Collectors.toMap(SysRegionConfig::getId, v -> v));

    return teamProfiles.stream()
        .collect(
            Collectors.toMap(TeamProfile::getId, v -> sysRegionConfigMap.get(v.getRegion())));
  }

  /**
   * 获取用户区域.
   */
  @Override
  public RegionConfig getRegionConfigByUserId(Long userId) {
    if (Objects.isNull(userId)) {
      return new RegionConfig();
    }
    String regionId = getRegionId(userId);
    if (StringUtils.isBlank(regionId)) {
      return null;
    }
    return userRegionInfraConvertor.toRegionConfig(sysRegionConfigService.getById(regionId)
    );
  }

  @Override
  public List<RegionConfig> listRegionConfigBySysOrigin(String sysOrigin) {
    return userRegionInfraConvertor.toListRegionConfig(
        sysRegionCacheService.getCache(sysOrigin)
    );
  }

  /**
   * 主播申请退出团队校验金额.
   */
  private HostApplyQuitTeamCheckDTO getHostApplyQuitTeamAmount(RegionConfig regionConfig) {

    if (Objects.isNull(regionConfig)) {
      return new HostApplyQuitTeamCheckDTO()
          .setQuitAmount(0L)
          .setAllowQuit(Boolean.FALSE);
    }

    return new HostApplyQuitTeamCheckDTO()
        .setQuitAmount(getHostTerminationFee(regionConfig.getId()))
        .setAllowQuit(checkOpenHostApplyQuitTeam(regionConfig));
  }

  /**
   * 根据区域ID获得对应的主播解约需支付的金币费用.
   */
  private Long getHostTerminationFee(String regionId) {
    SysRegionAssistConfig assistConfig = sysRegionAssistConfigService
        .getRegionAssistConfig(RegionAssistTypeEnum.HOST_TERMINATION_FEE, regionId);

    if (Objects.isNull(assistConfig)) {
      return 0L;
    }
    if (StringUtils.isBlank(assistConfig.getData())) {
      return 0L;
    }
    return Long.parseLong(assistConfig.getData());
  }

  /**
   * 该区域当前是否允许主播申请解约.
   *
   * @return true 允许.
   */
  private boolean checkOpenHostApplyQuitTeam(RegionConfig regionConfig) {
    if (Objects.isNull(regionConfig)) {
      return Boolean.FALSE;
    }

    if (CollectionUtils.isEmpty(regionConfig.getMetadata())) {
      return Boolean.FALSE;
    }

    return Objects.equals(regionConfig.getMetadata().get("openHostApplyQuitTeam"), "true");
  }

  @Override
  public HostApplyQuitTeamCheckDTO getHostApplyQuitTeamCheckByUserId(Long userId) {
    return getHostApplyQuitTeamAmount(getRegionConfigByUserId(userId));
  }

  @Override
  public HostApplyQuitTeamCheckDTO getHostApplyQuitTeamCheckByRegionId(String regionId) {
    return getHostApplyQuitTeamAmount(userRegionInfraConvertor.toRegionConfig(
        sysRegionConfigService.getById(regionId)
    ));
  }

  @Override
  public List<UserRegionDTO> mapRegionIdByUserIds(Set<Long> userIdSet) {
    if (CollectionUtils.isEmpty(userIdSet)) {
      return Lists.newArrayList();
    }
    return matchUserRegionDefaultRule(userIdSet);
  }

  private Map<Long, SysRegionConfig> matchRegion(String sysOrigin, Set<Long> nextProcessUserIds) {
    Map<Long, SysRegionConfig> resultMap = Maps.newHashMap();

    if (StringUtils.isBlank(sysOrigin)) {

      List<UserProfile> userProfiles = userProfileGateway.listByUserIds(nextProcessUserIds);

      if (CollectionUtils.isEmpty(userProfiles)) {
        return resultMap;
      }

      userProfiles.stream().collect(Collectors.groupingBy(UserProfile::getOriginSys))
          .forEach((sysOriginKey, sysOriginUserIds) -> resultMap.putAll(matchSysRegion(sysOriginKey,
              sysOriginUserIds.stream().map(UserProfile::getId).collect(
                  Collectors.toSet()))));

      return resultMap;
    }
    resultMap.putAll(matchSysRegion(sysOrigin, nextProcessUserIds));
    return resultMap;
  }


  /**
   * 匹配用户区域.
   */
  private Map<Long, SysRegionConfig> matchSysRegion(String sysOrigin, Set<Long> userIds) {

    if (CollectionUtils.isEmpty(userIds)) {
      return Maps.newHashMap();
    }

    List<SysRegionConfig> configs = listSysRegionConfig(sysOrigin);

    if (CollectionUtils.isEmpty(configs)) {
      return Maps.newHashMap();
    }

    List<UserProfile> userProfiles = userProfileGateway.listByUserIds(userIds);
    if (CollectionUtils.isEmpty(userProfiles)) {
      return Maps.newHashMap();
    }

    Map<Long, SysRegionConfig> resultMap = Maps.newHashMap();
    for (UserProfile userProfile : userProfiles) {

      if (StringUtils.isNotBlank(userProfile.getCountryCode())) {

        SysRegionConfig sysRegionConfig = configs.stream()
            .filter(region -> StringUtils.isNotBlank(region.getCountryCodes())
                && StringUtils.isNotBlank(userProfile.getCountryCode())
                && region.getCountryCodes().toLowerCase()
                .contains(userProfile.getCountryCode().toLowerCase())).findFirst()
            .orElse(null);

        if (Objects.nonNull(sysRegionConfig)) {
          resultMap.put(userProfile.getId(), sysRegionConfig);
          continue;
        }
      }

      String langeCode = getLanguage(userProfile.getId());
      if (StringUtils.isBlank(langeCode)) {
        resultMap.put(userProfile.getId(), getSysOriginDefaultRegion(configs));
        continue;
      }

      resultMap.put(userProfile.getId(), configs.stream()
          .filter(region -> StringUtils.isNotBlank(region.getLangeCodes())
              && StringUtils.isNotBlank(langeCode)
              && region.getLangeCodes().toLowerCase()
              .contains(langeCode.toLowerCase())).findFirst()
          .orElse(getSysOriginDefaultRegion(configs)));
    }

    return resultMap;
  }

  private String getLanguage(Long userId) {
    return userProfileGateway.getLanguage(userId);
  }

}
