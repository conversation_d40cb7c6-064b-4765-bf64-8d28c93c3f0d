package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyGiftCountDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyGiftCount;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyGiftCountService;
import com.red.circle.other.inner.model.cmd.game.LuckGiftGameCountQryCmd;
import com.red.circle.other.inner.model.cmd.game.LuckGiftGameQryCmd;
import com.red.circle.other.inner.model.dto.game.GameLuckyGiftCountDTO;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 幸运礼物送礼明细记录.
 *
 * <AUTHOR> on 2023/6/19
 */
@Service
@AllArgsConstructor
public class GameLuckyGiftCountServiceImpl extends
    BaseServiceImpl<GameLuckyGiftCountDAO, GameLuckyGiftCount> implements
    GameLuckyGiftCountService {

  private GameLuckyGiftCountDAO gameLuckyGiftCountDAO;

  @Override
  public List<GameLuckyGiftCount> lastTop30AndGetHundredfold() {

    return query()
        .gt(GameLuckyGiftCount::getMultiple, 99)
        .orderByDesc(GameLuckyGiftCount::getId)
        .last(PageConstant.formatLimit(30))
        .list();
  }

  @Override
  public List<GameLuckyGiftCount> pageByUserIdAndLastId(Long userId, Long lastId) {

    return query()
        .gt(GameLuckyGiftCount::getMultiple, 0)
        .eq(GameLuckyGiftCount::getUserId, userId)
        .lt(Objects.nonNull(lastId), GameLuckyGiftCount::getId, lastId)
        .orderByDesc(GameLuckyGiftCount::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }

  @Override
  public PageResult<GameLuckyGiftCount> pageByCondition(LuckGiftGameQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getGiftId()), GameLuckyGiftCount::getGiftId, query.getGiftId())
        .eq(Objects.nonNull(query.getRoomId()), GameLuckyGiftCount::getRoomId, query.getRoomId())
        .eq(Objects.nonNull(query.getUserId()), GameLuckyGiftCount::getUserId, query.getUserId())
        .eq(Objects.nonNull(query.getQuantity()), GameLuckyGiftCount::getQuantity,
            query.getQuantity())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameLuckyGiftCount::getSysOrigin,
            query.getSysOrigin())
        .ge(Objects.nonNull(query.getStartTime()), GameLuckyGiftCount::getCreateTime,
            query.getStartTime())
        .le(Objects.nonNull(query.getEndTime()), GameLuckyGiftCount::getCreateTime,
            query.getEndTime())
        .orderByDesc(GameLuckyGiftCount::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public GameLuckyGiftCountDTO countLuckyGiftGame(LuckGiftGameCountQryCmd query) {
    return gameLuckyGiftCountDAO.countLuckyGiftGame(query);
  }
}
