package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.LocalDateUtils;
import com.red.circle.other.infra.convertor.user.UserActivityInfraConvertor;
import com.red.circle.other.infra.database.rds.dao.user.user.UserActivityRewardReceiveRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserActivityRewardReceiveRecord;
import com.red.circle.other.infra.database.rds.service.user.user.UserActivityRewardReceiveRecordService;
import com.red.circle.other.inner.model.cmd.user.UserActivityRewardReceiveRecordQryCmd;
import com.red.circle.other.inner.model.dto.user.UserActivityRewardReceiveRecordDTO;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户活动月度领取记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-19
 */
@Service
@RequiredArgsConstructor
public class UserActivityRewardReceiveRecordServiceImpl extends
    BaseServiceImpl<UserActivityRewardReceiveRecordDAO, UserActivityRewardReceiveRecord> implements
    UserActivityRewardReceiveRecordService {

  private final UserActivityInfraConvertor userActivityInfraConvertor;


  @Override
  public boolean existsThisMonth(Long userId, Long activityId) {
    return Optional.ofNullable(query()
            .select(UserActivityRewardReceiveRecord::getId)
            .eq(UserActivityRewardReceiveRecord::getActivityId, activityId)
            .eq(UserActivityRewardReceiveRecord::getRechargeDate,
                LocalDateUtils.nowFirstDayOfMonthToInteger())
            .eq(UserActivityRewardReceiveRecord::getUserId, userId)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(userActivityRewardReceiveRecord -> Objects
            .nonNull(userActivityRewardReceiveRecord.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public Map<Long, UserActivityRewardReceiveRecordDTO> mapByThisMonth(Long userId) {
    return Optional.ofNullable(query()
            .eq(UserActivityRewardReceiveRecord::getRechargeDate,
                LocalDateUtils.nowFirstDayOfMonthToInteger())
            .eq(UserActivityRewardReceiveRecord::getUserId, userId).list())
        .map(userRewardReceive -> userActivityInfraConvertor.toUserActivityRewardReceiveRecordDTO(
            userRewardReceive).stream().collect(
            Collectors.toMap(UserActivityRewardReceiveRecordDTO::getActivityId, Function.identity(),
                (k, v) -> v)))
        .orElse(CollectionUtils.newHashMap());
  }


  @Override
  public PageResult<UserActivityRewardReceiveRecord> pageUserActivityRewardReceiveRecord(
      UserActivityRewardReceiveRecordQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getUserId()),
            UserActivityRewardReceiveRecord::getUserId, query.getUserId())
        .ge(Objects.nonNull(query.startTimeToLocalDateTime()),
            UserActivityRewardReceiveRecord::getCreateTime, query.startTimeToLocalDateTime())
        .le(Objects.nonNull(query.endTimeToLocalDateTime()),
            UserActivityRewardReceiveRecord::getCreateTime, query.endTimeToLocalDateTime())
        .orderByDesc(UserActivityRewardReceiveRecord::getCreateTime)
        .page(query.getPageQuery());
  }
}
