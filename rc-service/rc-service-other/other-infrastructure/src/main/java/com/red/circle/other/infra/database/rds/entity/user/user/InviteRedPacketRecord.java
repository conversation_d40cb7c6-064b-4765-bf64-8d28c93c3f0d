package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 邀请新用户-红包记录.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_red_packet_record")
public class InviteRedPacketRecord extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户ID.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 红包状态.
   */
  @TableField("status")
  private String status;

  /**
   * 总额.
   */
  @TableField("total_amount")
  private Long totalAmount;

  /**
   * 当前收集金额.
   */
  @TableField("current_amount")
  private BigDecimal currentAmount;

  /**
   * 沙特时间年月日.
   */
  @TableField("date_number")
  private Integer dateNumber;

  /**
   * 邀请人用户ID.
   */
  @TableField("invite_user_id")
  private Long inviteUserId;

  /**
   * 开红包来源.
   */
  @TableField("red_packet_source")
  private String redPacketSource;

  /**
   * 当前红包邀请成员数.
   */
  @TableField("invite_member_count")
  private Long inviteMemberCount;

  /**
   * 操作设备号.
   */
  @TableField("imei")
  private String imei;

  /**
   * 操作IP.
   */
  @TableField("ip")
  private String ip;

  /**
   * 过期时间.
   */
  @TableField("expires_time")
  private Timestamp expiresTime;

  /**
   * 是否违规（FALSE:违规，不能集满，TRUE：可以集满）
   */
  @TableField("violation_or_not")
  private Boolean violationOrNot;


}
