package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.SVIPLevelEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.SVipIdentityDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.SVipIdentity;
import com.red.circle.other.infra.database.rds.service.user.user.SVipIdentityService;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * <p>
 * SVip用户 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-18
 */
@Service
public class SVipIdentityServiceImpl extends
    BaseServiceImpl<SVipIdentityDAO, SVipIdentity> implements SVipIdentityService {

  @Override
  public List<SVipIdentity> listByExpire(SysOriginPlatformEnum sysOrigin) {

    return query()
        .eq(SVipIdentity::getSysOrigin, sysOrigin)
        .le(SVipIdentity::getExpiredTime, LocalDateTime.now())
        .gt(SVipIdentity::getLevel, SVIPLevelEnum.NONE.getLevel())
        .list();
  }

  @Override
  public List<SVipIdentity> listByUserIds(Set<Long> userIds) {

    if (CollectionUtils.isEmpty(userIds)) {
      return CollectionUtils.newArrayList();
    }

    return query().in(SVipIdentity::getUserId, userIds)
        .last(PageConstant.formatLimit(userIds.size())).list();
  }

  @Override
  public void updateIntegral(Long userId, Long integral) {
    update()
        .set(SVipIdentity::getIntegral, integral)
        .eq(SVipIdentity::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public void deleteInvalidUser() {

    delete()
        .eq(SVipIdentity::getLevel, SVIPLevelEnum.NONE.getLevel())
        .eq(SVipIdentity::getIntegral, 0L)
        .execute();
  }

  @Override
  public SVipIdentity getByUserId(Long userId) {
    return query().eq(SVipIdentity::getUserId, userId).last(PageConstant.LIMIT_ONE).getOne();
  }
}
