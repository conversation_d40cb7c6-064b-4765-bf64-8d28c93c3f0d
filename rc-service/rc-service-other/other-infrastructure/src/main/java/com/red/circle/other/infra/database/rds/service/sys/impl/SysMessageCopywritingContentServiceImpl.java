package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysMessageCopywritingContentDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysMessageCopywritingContent;
import com.red.circle.other.infra.database.rds.service.sys.SysMessageCopywritingContentService;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统推送文案类容 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Service
public class SysMessageCopywritingContentServiceImpl extends
    BaseServiceImpl<SysMessageCopywritingContentDAO, SysMessageCopywritingContent> implements
    SysMessageCopywritingContentService {


  @Override
  public List<SysMessageCopywritingContent> listByTextTypeId(Long textTypeId) {
    return query()
        .eq(SysMessageCopywritingContent::getTypeId, textTypeId)
        .orderByAsc(SysMessageCopywritingContent::getCreateTime)
        .list();
  }

  @Override
  public List<SysMessageCopywritingContent> listByLanguage(String language) {
    return query()
        .ne(SysMessageCopywritingContent::getLanguage, language).list();
  }

  @Override
  public void deleteByTypeId(Long typeId) {
    delete().eq(SysMessageCopywritingContent::getTypeId, typeId).execute();
  }
}
