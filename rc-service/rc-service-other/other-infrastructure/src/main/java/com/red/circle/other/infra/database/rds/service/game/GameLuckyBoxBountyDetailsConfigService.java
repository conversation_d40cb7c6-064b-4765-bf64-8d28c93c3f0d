package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxBountyDetailsConfig;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxBountyDetailsConfigQryCmd;
import java.util.List;


/**
 * <p>
 * 赏金任务配置详情 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
public interface GameLuckyBoxBountyDetailsConfigService extends
    BaseService<GameLuckyBoxBountyDetailsConfig> {

  List<GameLuckyBoxBountyDetailsConfig> getBoxBountyDetailsConfig(
      String sysOriginName);

  void deleteByBountyId(Long id);

  List<GameLuckyBoxBountyDetailsConfig> getGameLuckyBoxBountyDetailsConfig(
      GameLuckyBoxBountyDetailsConfigQryCmd query);
}
