package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户认证信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("user_auth_type")
public class AuthType extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * git 主键标识
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户id
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 类型
   */
  @TableField("`type`")
  private String type;

  /**
   * 开放id
   */
  @TableField("open_id")
  private String openId;

  /**
   * 0.未删除 1.已删除
   */
  @TableField("is_del")
  private Boolean del;

}
