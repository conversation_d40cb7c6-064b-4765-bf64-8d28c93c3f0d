package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLotteryRewardGroup;
import com.red.circle.other.infra.enums.game.LotteryGameTypeEnum;

/**
 * <p>
 * 抽奖游戏奖励配置分组 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface GameLotteryRewardGroupService extends BaseService<GameLotteryRewardGroup> {

  /**
   * 获取抽奖游戏组.
   *
   * @param sysOrigin 平台来源
   * @param gameType  抽奖游戏类型
   * @return list
   */
  GameLotteryRewardGroup getGameGroupByGameType(SysOriginPlatformEnum sysOrigin,
      LotteryGameTypeEnum gameType);

}
