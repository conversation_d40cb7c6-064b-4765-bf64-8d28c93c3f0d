package com.red.circle.other.infra.database.rds.service.team;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.mongo.entity.team.team.TeamPolicyManager;
import com.red.circle.other.infra.database.rds.entity.team.BillDiamondBalance;
import com.red.circle.other.infra.database.rds.entity.team.BusinessDevelopmentBaseInfo;
import com.red.circle.tool.core.tuple.PennyAmount;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 用户账单钻石余额 服务类.
 * </p>
 *
 * <AUTHOR> on 2024-03-19 18:08
 */
public interface BillDiamondBalanceService extends BaseService<BillDiamondBalance> {

    /**
     * 根据账单归属和用户id查询用户账单余额信息
     *
     * @param sysOrigin 系统
     * @param billBelong   账单归属(YYYYMM)
     * @param userId   用户id
     * @return 策略
     */
    BillDiamondBalance getEntityByBillBelongAndUserId(String sysOrigin, Integer billBelong, Long userId);

    /**
     *
     * @param sysOrigin 系统
     * @param billBelong 账单归属(YYYYMM)
     * @param teamId 团队id
     * @return
     */
    List<BillDiamondBalance> getEntityByBillBelongAndTeamId(String sysOrigin, Integer billBelong, Long teamId);

    void add(BillDiamondBalance param);

    void addAgent(BillDiamondBalance param);

    void addBalance(BillDiamondBalance param);

    void addAgentBalance(BillDiamondBalance param);

    boolean exists(Long userId, Integer billBelong);

    BillDiamondBalance getBalance(Long userId, Integer billBelong);

    BillDiamondBalance decr(Long userId, BigDecimal amount, Integer billBelong);

    BillDiamondBalance decrAgent(Long userId, BigDecimal amount, Integer billBelong);

    void removeBatchByTeamIds(List<Long> teamIds, Integer billBelong);

    void removeByUserId(Long userId, Integer billBelong);

}
