package com.red.circle.other.infra.database.rds.service.live;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.live.RoomMikeType;
import com.red.circle.other.inner.model.cmd.live.MikeTypePropsQryCmd;
import java.math.BigDecimal;
import java.util.List;

/**
 * 设置活动房间麦类型服务
 *
 * <AUTHOR> on 2023/3/24 17:35
 */
public interface RoomMikeTypeService extends BaseService<RoomMikeType> {


  /**
   * 添加
   *
   * @param userId         用户id
   * @param sysOrigin      系统来源
   * @param mikeType       麦位类型
   * @param roomMikeAmount 购买金额
   */
  boolean save(Long userId, String sysOrigin, String mikeType,
      BigDecimal roomMikeAmount);

  /**
   * 添加
   *
   * @param userId         用户id
   * @param sysOrigin      系统来源
   * @param mikeType       麦位类型
   * @param duration       麦位时长类型
   * @param roomMikeAmount 购买金额
   */
  boolean save(Long userId, String sysOrigin, String mikeType, String duration,
      BigDecimal roomMikeAmount);

  /**
   * 根据用户id查询购买过的麦位类型
   *
   * @param userId 用户id
   */
  List<RoomMikeType> listByUserIdRoomMikeTypes(Long userId);

  /**
   * 校验是否已购买过特殊麦位类型
   *
   * @param userId   用户id
   * @param mikeType 麦位类型
   */
  boolean isPurchaseMikeType(Long userId, String mikeType);

  /**
   * 校验是否已购买过特殊麦位类型
   *
   * @param userId     用户id
   * @param mikeType   麦位类型
   * @param expireType 过期类型
   */
  boolean isPurchaseMikeType(Long userId, String mikeType, String expireType);

  /**
   * 分页查询活动房间特殊麦位.
   */
  PageResult<RoomMikeType> pageRoomMikeTypeList(MikeTypePropsQryCmd query);
}
