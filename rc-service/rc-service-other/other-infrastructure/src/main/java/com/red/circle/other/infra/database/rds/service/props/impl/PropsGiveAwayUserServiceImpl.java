package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsGiveAwayUserDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsGiveAwayUser;
import com.red.circle.other.infra.database.rds.service.props.PropsGiveAwayUserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户赠送道具 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-04-10 17:00
 */
@Service
public class PropsGiveAwayUserServiceImpl extends
    BaseServiceImpl<PropsGiveAwayUserDAO, PropsGiveAwayUser> implements
    PropsGiveAwayUserService {

}
