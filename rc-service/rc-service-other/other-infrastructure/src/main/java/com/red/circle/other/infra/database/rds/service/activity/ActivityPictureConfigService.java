package com.red.circle.other.infra.database.rds.service.activity;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityPictureConfig;
import com.red.circle.other.inner.model.cmd.activity.ActivityPictureQryCmd;

/**
 * <p>
 * 活动图片配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-20 16:37
 */
public interface ActivityPictureConfigService extends BaseService<ActivityPictureConfig> {

  PageResult<ActivityPictureConfig> getActivityPicture(ActivityPictureQryCmd query);

  void removeActivityPicture(Long id);
}
