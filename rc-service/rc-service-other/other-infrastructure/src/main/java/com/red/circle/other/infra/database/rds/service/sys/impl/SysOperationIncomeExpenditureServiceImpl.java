package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.google.common.collect.Maps;
import com.red.circle.common.business.enums.IncomeExpenditureEnum;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysOperationIncomeExpenditureDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysOperationIncomeExpenditure;
import com.red.circle.other.infra.database.rds.service.sys.SysOperationIncomeExpenditureService;
import com.red.circle.other.inner.enums.sys.SysCurrencyDeductReasonEnum;
import com.red.circle.other.inner.enums.sys.SysCurrencySendReasonEnum;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringPool;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户糖果收入支持明细 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 * @since pengliang 2021-10-11
 */

@Slf4j
@RequiredArgsConstructor
@Service
public class SysOperationIncomeExpenditureServiceImpl extends
    BaseServiceImpl<SysOperationIncomeExpenditureDAO, SysOperationIncomeExpenditure> implements
    SysOperationIncomeExpenditureService {

  @Override
  public Map<String, String> mapAssociateRemarks(Set<String> associateIds) {
    if (CollectionUtils.isEmpty(associateIds)) {
      return Maps.newHashMap();
    }
    return Optional.ofNullable(query()
            .in(SysOperationIncomeExpenditure::getAssociateId, associateIds)
            .list())
        .map(associates -> associates.stream().collect(Collectors
            .toMap(SysOperationIncomeExpenditure::getAssociateId,
                sys -> String.format("%s/%s",
                    getReasonDesc(sys.getType(), sys.getReason()),
                    sys.getRemarks()
                ))))
        .orElseGet(Maps::newHashMap);
  }

  private String getReasonDesc(Integer type, Integer reason) {
    if (IncomeExpenditureEnum.INCOME.eq(type)) {
      return SysCurrencySendReasonEnum.getReasonDesc(reason);
    }

    if (IncomeExpenditureEnum.EXPENDITURE.eq(type)) {
      return SysCurrencyDeductReasonEnum.getReasonDesc(reason);
    }
    return StringPool.EMPTY;
  }
}
