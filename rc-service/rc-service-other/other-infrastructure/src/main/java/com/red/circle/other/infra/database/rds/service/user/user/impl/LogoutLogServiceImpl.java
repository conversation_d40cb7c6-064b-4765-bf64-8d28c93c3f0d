package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.LogoutLogDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.LogoutLog;
import com.red.circle.other.infra.database.rds.service.user.user.LogoutLogService;
import com.red.circle.other.inner.model.cmd.user.UserLogoutLogQryCmd;
import com.red.circle.tool.core.date.LocalDateTimeUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户注销记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-09-12 11:01
 */
@Service
@RequiredArgsConstructor
public class LogoutLogServiceImpl extends BaseServiceImpl<LogoutLogDAO, LogoutLog> implements
    LogoutLogService {

  @Override
  public Boolean getByUserId(Long userId, String logoutType) {

    return Objects.nonNull(query()
        .eq(LogoutLog::getUserId, userId)
        .eq(LogoutLog::getLogoutType, logoutType)
        .last(PageConstant.LIMIT_ONE)
        .getOne());
  }

  @Override
  public PageResult<LogoutLog> pageUserLogoutLog(UserLogoutLogQryCmd query) {
    if (query.getStartTime() != null && query.getEndTime() != null){
      query.setStartCreateDate(LocalDateTimeUtils.convert(query.getStartTime()));
      query.setEndCreateDate(LocalDateTimeUtils.convert(query.getEndTime()));
    }
    return query()
        .eq(Objects.nonNull(query.getUserId()), LogoutLog::getUserId, query.getUserId())
        .eq(StringUtils.isNotBlank(query.getLogoutType()), LogoutLog::getLogoutType,
            query.getLogoutType())
        .ge(Objects.nonNull(query.getStartTime()), LogoutLog::getCreateTime,
            query.getStartCreateDate())
        .le(Objects.nonNull(query.getEndTime()), LogoutLog::getCreateTime,
            query.getEndCreateDate())
        .orderByDesc(LogoutLog::getCreateTime)
        .page(query.getPageQuery());
  }
}
