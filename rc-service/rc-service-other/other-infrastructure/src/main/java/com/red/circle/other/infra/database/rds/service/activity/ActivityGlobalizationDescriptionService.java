package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityGlobalizationDescription;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 活动国际化语言描述 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
public interface ActivityGlobalizationDescriptionService extends
    BaseService<ActivityGlobalizationDescription> {

  Map<Long, List<ActivityGlobalizationDescription>> mapByRelationIds(Set<Long> relationIds);

  List<ActivityGlobalizationDescription> listByRelationId(Long relationId);

}
