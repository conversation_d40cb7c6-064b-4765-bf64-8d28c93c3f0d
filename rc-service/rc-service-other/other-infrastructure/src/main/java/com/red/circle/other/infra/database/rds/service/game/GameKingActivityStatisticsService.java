package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameKingActivityStatistics;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 游戏王临时统计表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-25
 */
public interface GameKingActivityStatisticsService extends BaseService<GameKingActivityStatistics> {

  /**
   * 根据条件获得晋级用户id与金额。
   *
   * @param promotion 晋级赛：1.32强 2.16强 3.8强 4.4强 5.半决赛 6.总决赛
   */
  List<GameKingActivityStatistics> listUserByCondition(String sysOrigin,
      Integer promotion, Set<Long> topUserIds);


  /**
   * 将水果，扑克，骰子等游戏中奖信息插入至该表配合计算，先删除再插入。
   */
  void deleteBySysOriginByUserIds(Set<Long> userIds, String sysOrigin);

  /**
   * 统计老水果机游戏
   */
  List<GameKingActivityStatistics> listOldFruitGameBySysOriginByUserIds(Set<Long> userIds,
      String sysOrigin);

  /**
   * 统计水果机游戏
   */
  List<GameKingActivityStatistics> listFruitGameBySysOriginByUserIds(Set<Long> userIds,
      String sysOrigin);


}

