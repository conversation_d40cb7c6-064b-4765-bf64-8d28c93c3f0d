package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.component.redis.service.RedisService;
import com.red.circle.other.infra.database.cache.key.GameEggKey;
import com.red.circle.other.infra.database.cache.key.GameListKeys;
import com.red.circle.other.infra.database.rds.service.game.GameEggCacheService;
import com.red.circle.tool.core.date.LocalDateUtils;
import java.time.LocalDate;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 砸金蛋.
 *
 * <AUTHOR> on 2021/11/16
 */
@Service
@RequiredArgsConstructor
public class GameEggCacheServiceImpl implements GameEggCacheService {

  private final RedisService redisService;

  @Override
  public String getThisDaysTop20(String sysOrigin) {
    return redisService
        .getString(GameListKeys.getKey(GameListKeys.GAME_EGG_TOP20.name(), sysOrigin));
  }

  @Override
  public void cacheThisDaysTop20(String sysOrigin, String data) {
    redisService
        .setIfAbsent(GameListKeys.getKey(GameListKeys.GAME_EGG_TOP20.name(), sysOrigin), data, 1,
            TimeUnit.DAYS);
  }

  @Override
  public Long getThisMonthPrizePool(String sysOrigin) {
    Long val = redisService.getStringToLong(
        GameEggKey.PRIZE_POOL.getKey(sysOrigin, LocalDateUtils.nowThisMonthToInteger()));
    return Objects.isNull(val) ? 0L : val;
  }

  @Override
  public void incrThisMonthPrizePool(String sysOrigin, Long amount) {
    redisService.increment(
        GameEggKey.PRIZE_POOL.getKey(sysOrigin, LocalDateUtils.nowThisMonthToInteger()),
        amount,
        40 - (LocalDate.now().getDayOfMonth() - 1),
        TimeUnit.DAYS
    );
  }

  @Override
  public void decrThisMonthPrizePool(String sysOrigin, Long amount) {
    redisService.decrement(
        GameEggKey.PRIZE_POOL.getKey(sysOrigin, LocalDateUtils.nowThisMonthToInteger()),
        amount,
        40 - (LocalDate.now().getDayOfMonth() - 1),
        TimeUnit.DAYS
    );
  }

  @Override
  public String getExtractRatio(String sysOrigin, String defaultRatio) {
    return Optional.ofNullable(redisService.getString(
            GameEggKey.EXTRACT_RATIO.getKey(sysOrigin)))
        .orElse(defaultRatio);
  }

  @Override
  public void setExtractRatio(String sysOrigin, String ratio) {
    redisService.setIfAbsent(GameEggKey.EXTRACT_RATIO.getKey(sysOrigin), ratio);
  }

  @Override
  public Long getConsumeStockQuantity(Long id) {
    return redisService.getStringToLong(GameEggKey.KC.getKey(id));
  }

  @Override
  public void setConsumeStockQuantity(Long id, Long quantity) {
    redisService.setIfAbsent(GameEggKey.KC.getKey(id), quantity, 1,
        TimeUnit.DAYS);
  }

  @Override
  public void decrConsumeStockQuantity(Long id, Long quantity) {
    redisService.decrement(GameEggKey.KC.getKey(id), quantity);
  }

  @Override
  public void removeConsumeStockQuantity(Long id) {
    redisService.delete(GameEggKey.KC.getKey(id));
  }

}
