package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.BankIdentityInfo;
import com.red.circle.other.inner.model.cmd.user.UserBankIdentityInfoExportQryCmd;
import com.red.circle.other.inner.model.cmd.user.UserBankIdentityInfoQryCmd;
import java.util.List;

/**
 * <p>
 * 用户身份信息 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-06-15 17:13
 */
public interface BankIdentityInfoService extends BaseService<BankIdentityInfo> {

  /**
   * 用户，是否存在通过记录.
   */
  boolean existsPass(Long userId);

  /**
   * 获取最新一次提交.
   */
  BankIdentityInfo getLatest(Long userId);

  PageResult<BankIdentityInfo> pageListQuery(UserBankIdentityInfoQryCmd query);

  List<BankIdentityInfo> listExport(UserBankIdentityInfoExportQryCmd query);

  boolean updateInfo(BankIdentityInfo userBankIdentityInfo);
}
