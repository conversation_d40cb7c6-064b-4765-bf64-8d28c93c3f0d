package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.WeekStarGift;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 周星礼物 服务类.
 * </p>
 *
 * <AUTHOR> on  2021-04-20
 */
public interface WeekStarGiftService extends BaseService<WeekStarGift> {

  /**
   * 获取指定分组的礼物id.
   *
   * @param groupId 分组id
   * @return 礼物id
   */
  List<Long> listGiftIdsByGroupId(Long groupId);

  /**
   * 获取指定分组的礼物.
   *
   * @param groupId 分组id
   * @return 礼物
   */
  List<WeekStarGift> listByGroupId(Long groupId);

  /**
   * 获取礼物分组信息.
   *
   * @param groupIds 分组id集合
   * @return ignore
   */
  Map<Long, List<WeekStarGift>> mapGroupByGroupId(List<Long> groupIds);

  /**
   * 删除分组相关元素.
   *
   * @param id ignore
   */
  void deleteByGroupId(Long id);
}
