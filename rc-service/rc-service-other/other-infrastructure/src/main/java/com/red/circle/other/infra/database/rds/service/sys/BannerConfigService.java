package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.BannerConfig;
import com.red.circle.other.inner.model.cmd.sys.SysBannerConfigQryCmd;
import java.util.List;

/**
 * <p>
 * banner配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
public interface BannerConfigService extends BaseService<BannerConfig> {

  /**
   * 获取有效的banner.
   *
   * @param sysOrigin 来源系统
   * @return ignore
   */
  List<BannerConfig> listEffective(String sysOrigin);

  PageResult<BannerConfig> pageBanner(SysBannerConfigQryCmd query);

  Boolean updateInfo(BannerConfig bannerConfig);
}
