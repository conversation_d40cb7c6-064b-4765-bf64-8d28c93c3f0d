package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameTrumpetBlacklist;
import com.red.circle.other.inner.model.cmd.game.GameTrumpetBlacklistQryCmd;

/**
 * <p>
 * 喇叭-黑名单 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-06 16:18
 */
public interface GameTrumpetBlacklistService extends BaseService<GameTrumpetBlacklist> {

  boolean exist(Long userId);

  void deleteByUserId(Long userId);

  PageResult<GameTrumpetBlacklist> page(GameTrumpetBlacklistQryCmd query);
}
