package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.LuckyGiftProbabilityDetailsDAO;
import com.red.circle.other.infra.database.rds.entity.game.LuckyGiftProbabilityDetails;
import com.red.circle.other.infra.database.rds.service.game.LuckyGiftProbabilityDetailsService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 礼物规格概率详情.
 * </p>
 *
 * <AUTHOR> on 2023-05-05 17:18
 */
@Service
@RequiredArgsConstructor
public class LuckyGiftProbabilityDetailsServiceImpl extends
    BaseServiceImpl<LuckyGiftProbabilityDetailsDAO, LuckyGiftProbabilityDetails> implements
    LuckyGiftProbabilityDetailsService {

  private final LuckyGiftProbabilityDetailsDAO luckyGiftProbabilityDetailsDAO;

  @Override
  public Integer getMaxMultiple(Long probabilityId) {

    if (Objects.isNull(probabilityId)) {
      return 0;
    }

    return Optional.ofNullable(luckyGiftProbabilityDetailsDAO.getMaxMultiple(probabilityId))
        .orElse(0);
  }

  @Override
  public Map<Long, Integer> mapMaxMultiple(Set<Long> probabilityIds) {

    if (CollectionUtils.isEmpty(probabilityIds)) {
      return null;
    }

    return Optional.ofNullable(
            luckyGiftProbabilityDetailsDAO.listMaxMultiple(Lists.newArrayList(probabilityIds)))
        .map(details -> details.stream()
            .collect(Collectors.toMap(LuckyGiftProbabilityDetails::getProbabilityId,
                LuckyGiftProbabilityDetails::getMultiple)))
        .orElse(Maps.newHashMap());

  }

  @Override
  public List<LuckyGiftProbabilityDetails> listByProbabilityId(Long probabilityId) {

    return query()
        .eq(LuckyGiftProbabilityDetails::getProbabilityId, probabilityId)
        .last(PageConstant.formatLimit(50))
        .list();
  }

  @Override
  public void deleteByProbabilityIds(Set<Long> probabilityIds) {
    delete().in(LuckyGiftProbabilityDetails::getProbabilityId, probabilityIds).execute();
  }

  @Override
  public Map<Long, List<LuckyGiftProbabilityDetails>> getLuckyGiftProbabilityDetails(
      String sysOrigin) {
    return Optional.ofNullable(
            query().eq(LuckyGiftProbabilityDetails::getSysOrigin, sysOrigin).list())
        .map(probabilityDetails ->
            probabilityDetails.stream().collect(
                Collectors.groupingBy(LuckyGiftProbabilityDetails::getProbabilityId)))
        .orElse(Maps.newHashMap());
  }

  @Override
  public List<LuckyGiftProbabilityDetails> getDetailByProbabilityId(Long probabilityId) {
    return Optional.ofNullable(
        query().eq(LuckyGiftProbabilityDetails::getProbabilityId, probabilityId).list()).orElse(
        Lists.newArrayList());
  }

  @Override
  public void add(Long probabilityId,
      List<LuckyGiftProbabilityDetails> gameLuckyGiftProbabilityDetails) {
    delete().eq(LuckyGiftProbabilityDetails::getProbabilityId, probabilityId).execute();
    saveBatch(gameLuckyGiftProbabilityDetails);
  }

}
