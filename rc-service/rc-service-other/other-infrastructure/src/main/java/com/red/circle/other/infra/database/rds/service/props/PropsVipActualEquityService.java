package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsVipActualEquity;
import java.util.Set;

/**
 * <p>
 * 用户所得贵族实际权益表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-21
 */
public interface PropsVipActualEquityService extends BaseService<PropsVipActualEquity> {

  /**
   * 用户是否存在没有过期的贵族道具ID集合.
   *
   * @param userId 用户id
   * @return list
   */
  Set<Long> getActualEquityNotExpired(Long userId);

  /**
   * 是否存在可用道具, true 存在， false 不存在.
   */
  boolean existsNotExpiredPropsId(Long propsId);

  /**
   * 保存.
   *
   * @param userId     用户id.
   * @param vipPropsId 贵族道具id.
   * @param days       天数.
   */
  void save(Long userId, Long vipPropsId, Long days);

  /**
   * 根据贵族道具id + 用户id 获得信息
   *
   * @return obj
   */
  PropsVipActualEquity getByPropsIdByUserId(Long userId, Long vipPropsId);

  /**
   * 用户赠送道具
   *
   * @param acceptUserId 接收用户id
   * @param userId       用户id
   * @param propsId      道具id
   */
  void changeBelongUserId(Long acceptUserId, Long userId, Long propsId);
}
