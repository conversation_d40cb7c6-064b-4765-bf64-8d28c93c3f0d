package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.GameListConfigDAO;
import com.red.circle.other.infra.database.rds.entity.sys.GameListConfig;
import com.red.circle.other.infra.database.rds.service.sys.GameListConfigService;
import com.red.circle.other.inner.model.cmd.sys.SysGameListQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 游戏列表配置 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Service
public class GameListConfigServiceImpl extends
    BaseServiceImpl<GameListConfigDAO, GameListConfig> implements GameListConfigService {

  @Override
  public List<GameListConfig> listShowcaseConfig(String sysOrigin,boolean fullScreen,boolean hotGame) {
    return query()
        .eq(GameListConfig::getSysOrigin, sysOrigin)
        .eq(GameListConfig::getShowcase, Boolean.TRUE)
            .ne(hotGame,GameListConfig::getGameOrigin,"HOTGAME")
        .eq(GameListConfig::getFullScreen, fullScreen)
        .orderByDesc(GameListConfig::getSort)
        .list();
  }

  @Override
  public String getCoverByGameCode(String gameCode) {
    return Optional.ofNullable(query()
            .select(GameListConfig::getCover)
            .eq(GameListConfig::getGameCode, gameCode)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(GameListConfig::getCover)
        .orElse(null);
  }

  @Override
  public PageResult<GameListConfig> pageQuery(SysGameListQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameListConfig::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getShowcase()), GameListConfig::getShowcase, query.getShowcase())
        .eq(StringUtils.isNotBlank(query.getGameOrigin()), GameListConfig::getGameOrigin,
            query.getGameOrigin())
        .like(StringUtils.isNotBlank(query.getRegion()), GameListConfig::getRegions,
            query.getRegion())
        .orderByDesc(GameListConfig::getSort)
        .page(query.getPageQuery());
  }


  @Override
  public GameListConfig getByGameCode(String gameCode, String sysOrigin) {
    return query()
        .eq(GameListConfig::getGameCode, gameCode)
        .eq(GameListConfig::getSysOrigin, sysOrigin)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public Boolean checkGameUnshelve(Set<Long> gameIds) {
    return Optional.ofNullable(query()
            .in(GameListConfig::getId, gameIds)
            .last(PageConstant.formatLimit(gameIds.size()))
            .list())
        .map(list -> list.stream().anyMatch(item -> item.getShowcase() == Boolean.FALSE))
        .orElse(Boolean.FALSE);
  }
}
