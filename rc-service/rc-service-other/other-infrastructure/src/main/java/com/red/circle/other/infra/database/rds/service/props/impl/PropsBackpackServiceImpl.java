package com.red.circle.other.infra.database.rds.service.props.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsBackpackDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsBackpack;
import com.red.circle.other.infra.database.rds.service.props.PropsBackpackService;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间用户道具 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-22
 */
@Service
@RequiredArgsConstructor
public class PropsBackpackServiceImpl extends
    BaseServiceImpl<PropsBackpackDAO, PropsBackpack> implements PropsBackpackService {

  private final PropsBackpackDAO propsBackpackDAO;

  @Override
  public void deleteProps(Collection<Long> userIds, Collection<Long> propsIds,
      PropsCommodityType propsType) {

    if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(propsIds) || Objects
        .isNull(propsType)) {
      return;
    }

    delete()
        .eq(PropsBackpack::getType, propsType)
        .in(PropsBackpack::getUserId, userIds)
        .in(PropsBackpack::getPropsId, propsIds)
        .execute();
  }

  @Override
  public void deleteProps(Long userId, Collection<Long> propsIds) {
    if (Objects.isNull(userId) || CollectionUtils.isEmpty(propsIds)) {
      return;
    }
    delete()
        .eq(PropsBackpack::getUserId, userId)
        .in(PropsBackpack::getPropsId, propsIds)
        .execute();
  }


  @Override
  public void deleteByUserIdsAndPropsIds(Collection<Long> userIds, Collection<Long> propsIds) {
    if (CollectionUtils.isEmpty(userIds) || CollectionUtils.isEmpty(propsIds)) {
      return;
    }
    delete()
        .in(PropsBackpack::getUserId, userIds)
        .in(PropsBackpack::getPropsId, propsIds)
        .execute();
  }

  @Override
  public boolean existsUseNotExpiredProps(Long userId, PropsCommodityType propsType) {
    return Optional.ofNullable(
        query().select(PropsBackpack::getId)
            .eq(PropsBackpack::getType, propsType)
            .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
            .eq(PropsBackpack::getUserId, userId)
            .eq(PropsBackpack::getUseProps, Boolean.TRUE)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).map(propsBackpack -> Objects.nonNull(propsBackpack.getId())).orElse(Boolean.FALSE);
  }

  @Override
  public boolean existsNotExpiredProps(Long userId, PropsCommodityType propsType) {
    return Optional.ofNullable(
        query().select(PropsBackpack::getId)
            .eq(PropsBackpack::getType, propsType)
            .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
            .eq(PropsBackpack::getUserId, userId)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).map(propsBackpack -> Objects.nonNull(propsBackpack.getId())).orElse(Boolean.FALSE);
  }

  @Override
  public Map<Long, List<PropsBackpack>> mapUserNotExpiredNobleVip(Set<Long> userIds) {
    return CollectionUtils.isEmpty(userIds)
        ? Maps.newHashMap()
        : Optional.ofNullable(
                query()
                    .eq(PropsBackpack::getType, PropsCommodityType.NOBLE_VIP)
                    .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
                    .in(PropsBackpack::getUserId, userIds)
                    .list()
            ).map(nobleVips -> nobleVips.stream()
                .collect(Collectors.groupingBy(PropsBackpack::getUserId)))
            .orElseGet(Maps::newHashMap);
  }

  @Override
  public List<PropsBackpack> listNotExpiredByType(Long userId, PropsCommodityType type) {
    return query()
        .eq(PropsBackpack::getUserId, userId)
        .eq(PropsBackpack::getType, type)
        .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
        .list();
  }

  @Override
  public PropsBackpack getUserNotExpiredUseProps(Long userId, PropsCommodityType type) {
    return query()
        .eq(PropsBackpack::getUserId, userId)
        .eq(PropsBackpack::getType, type)
        .eq(PropsBackpack::getUseProps, Boolean.TRUE)
        .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PropsBackpack getUserProps(Long userId, Long propsId, PropsCommodityType type) {
    return query()
        .eq(PropsBackpack::getUserId, userId)
        .eq(PropsBackpack::getPropsId, propsId)
        .eq(PropsBackpack::getType, type)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public List<PropsBackpack> listNotExpiredByUserIdAndType(Long userId,
      PropsCommodityType type) {
    return query()
        .eq(PropsBackpack::getUserId, userId)
        .eq(PropsBackpack::getType, type)
        .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
        .orderByDesc(PropsBackpack::getId)
        .list();
  }

  @Override
  public void switchUseProps(Long userId, Long propsId, PropsCommodityType type) {
    // 卸下装扮
    unloadUseProps(userId, type);

    // 重选装扮
    update().set(PropsBackpack::getUseProps, Boolean.TRUE)
        .eq(PropsBackpack::getUserId, userId)
        .eq(PropsBackpack::getPropsId, propsId)
        .eq(PropsBackpack::getType, type)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public void unloadUseProps(Long userId, PropsCommodityType type) {
    update().set(PropsBackpack::getUseProps, Boolean.FALSE)
        .eq(PropsBackpack::getUserId, userId)
        .eq(PropsBackpack::getType, type)
        .eq(PropsBackpack::getUseProps, Boolean.TRUE)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public List<PropsBackpack> listNotExpiredUseProps(Long userId) {
    return Optional.ofNullable(query().eq(PropsBackpack::getUserId, userId)
            .eq(PropsBackpack::getUseProps, Boolean.TRUE)
            .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
            .list())
        .orElse(Lists.newArrayList());
  }

  @Override
  public List<PropsBackpack> listNotExpiredUseProps(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Lists.newArrayList();
    }
    return Optional.ofNullable(query()
            .in(userIds.size() > 1, PropsBackpack::getUserId, userIds)
            .eq(Objects.equals(userIds.size(), 1), PropsBackpack::getUserId, userIds.iterator().next())
            .eq(PropsBackpack::getUseProps, Boolean.TRUE)
            .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
            .list())
        .orElse(Lists.newArrayList());
  }

  @Override
  public List<PropsBackpack> listNotExpiredUseProps(Set<Long> userIds,
      List<PropsCommodityType> types) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Lists.newArrayList();
    }
    if (Objects.isNull(types) || types.size() <= 0) {
      return Lists.newArrayList();
    }
    return Optional.ofNullable(query()
            .in(PropsBackpack::getUserId, userIds)
            .in(PropsBackpack::getType, types)
            .eq(PropsBackpack::getUseProps, Boolean.TRUE)
            .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
            .list())
        .orElse(Lists.newArrayList());
  }

  @Override
  public void addPropsBackpack(PropsBackpack backpack) {
    if (Objects.isNull(backpack)) {
      return;
    }

    PropsBackpack propsBackpack = Optional.ofNullable(query()
        .eq(PropsBackpack::getUserId, backpack.getUserId())
        .eq(PropsBackpack::getPropsId, backpack.getPropsId())
        .last(PageConstant.LIMIT_ONE)
        .getOne()).orElse(null);

    // 卸下同类型道具
    if (Objects.equals(backpack.getUseProps(), Boolean.TRUE)) {
      unloadUseProps(backpack.getUserId(), PropsCommodityType.valueOf(backpack.getType()));
    }

    if (Objects.isNull(propsBackpack)) {
      save(backpack);
      return;
    }

    propsBackpack.setUseProps(backpack.getUseProps());
    propsBackpack.setAllowGive(backpack.getAllowGive());
    propsBackpack.setExpireTime(
        getExpireTime(propsBackpack.getExpireTime(), backpack.getExpireTime()));
    updateSelectiveById(propsBackpack);
  }

  private Timestamp getExpireTime(Timestamp historyExpireTime, Timestamp appendExpireTime) {
    if (Objects.isNull(historyExpireTime) || historyExpireTime.before(TimestampUtils.now())) {
      return appendExpireTime;
    }

    if (appendExpireTime.before(TimestampUtils.now())) {
      return historyExpireTime;
    }

    return TimestampUtils.dateTimePlusNanos(historyExpireTime,
        TimestampUtils.between(TimestampUtils.now(), appendExpireTime).toNanos());
  }

  @Override
  public void deleteByPropsId(Long propsId, Long userId) {
    if (Objects.isNull(propsId) || Objects.isNull(userId)) {
      return;
    }
    delete().eq(PropsBackpack::getPropsId, propsId).eq(PropsBackpack::getUserId, userId).execute();
  }

  @Override
  public PropsBackpack getUserPropsByUserIdByPropsId(Long userId, Long propsId) {

    if (Objects.isNull(userId) || Objects.isNull(propsId)) {
      return null;
    }

    return query()
        .eq(PropsBackpack::getPropsId, propsId)
        .eq(PropsBackpack::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public Long getUsePropsId(Long userId, PropsCommodityType type) {

    return Optional.ofNullable(
            query()
                .eq(PropsBackpack::getUserId, userId)
                .eq(PropsBackpack::getUseProps, Boolean.TRUE)
                .eq(PropsBackpack::getType, type.name())
                .gt(PropsBackpack::getExpireTime, TimestampUtils.now())
                .last(PageConstant.LIMIT_ONE)
                .getOne())
        .map(PropsBackpack::getPropsId)
        .orElse(null);
  }

  @Override
  public void reduceDaysAndUnUse(Long userId, Set<Long> propsIds, Integer reduceDays) {

    if (CollectionUtils.isEmpty(propsIds)) {
      return;
    }

    List<PropsBackpack> propsBackpacks = query()
        .eq(PropsBackpack::getUserId, userId)
        .in(PropsBackpack::getPropsId, propsIds)
        .last(PageConstant.formatLimit(propsIds.size()))
        .list();

    if (CollectionUtils.isEmpty(propsBackpacks)) {
      return;
    }

    updateBatchById(
        propsBackpacks.stream().peek(propsBackpack -> {
          propsBackpack.setUseProps(Boolean.FALSE);
          propsBackpack
              .setExpireTime(
                  TimestampUtils.dateTimeMinusDays(propsBackpack.getExpireTime(), reduceDays));
        }).collect(Collectors.toList()));

  }

  @Override
  public Set<Long> getWearMultipleAvatarFrameUserIds() {
    return propsBackpackDAO.getWearMultipleAvatarFrameUserIds();
  }

  @Override
  public void giveAwayProps(Long acceptUserId, Long userId, Long propsId, String type) {
    PropsBackpack acceptBackpack = query()
        .eq(PropsBackpack::getUserId, acceptUserId)
        .eq(PropsBackpack::getPropsId, propsId)
        .eq(PropsBackpack::getType, type)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(acceptBackpack)) {
      update()
          .set(PropsBackpack::getUserId, acceptUserId)
          .set(PropsBackpack::getAllowGive, Boolean.TRUE)
          .eq(PropsBackpack::getUserId, userId)
          .eq(PropsBackpack::getPropsId, propsId)
          .eq(PropsBackpack::getType, type)
          .last(PageConstant.LIMIT_ONE)
          .execute();
    } else {

      PropsBackpack backpack = query()
          .eq(PropsBackpack::getUserId, userId)
          .eq(PropsBackpack::getPropsId, propsId)
          .eq(PropsBackpack::getType, type)
          .last(PageConstant.LIMIT_ONE)
          .getOne();
      Long milliSecond = TimestampUtils.expiredIntervalMillisecond(acceptBackpack.getExpireTime(),
          TimestampUtils.now());
      if (milliSecond <= 0) {
        update().set(PropsBackpack::getExpireTime,
                TimestampUtils.dateTimePlusSeconds(acceptBackpack.getExpireTime(),
                    TimestampUtils.expiredIntervalSecond(TimestampUtils.now(),
                        backpack.getExpireTime())))
            .set(PropsBackpack::getAllowGive, Boolean.TRUE)
            .eq(PropsBackpack::getUserId, acceptUserId)
            .eq(PropsBackpack::getPropsId, propsId)
            .eq(PropsBackpack::getType, type)
            .last(PageConstant.LIMIT_ONE)
            .execute();
      } else {
        update().set(PropsBackpack::getExpireTime, backpack.getExpireTime())
            .set(PropsBackpack::getAllowGive, Boolean.TRUE)
            .eq(PropsBackpack::getUserId, acceptUserId)
            .eq(PropsBackpack::getPropsId, propsId)
            .eq(PropsBackpack::getType, type)
            .last(PageConstant.LIMIT_ONE)
            .execute();
      }

      unloadUseProps(userId, PropsCommodityType.valueOf(type));

      update().set(PropsBackpack::getExpireTime, TimestampUtils.now())
          .set(PropsBackpack::getAllowGive,Boolean.TRUE)
          .eq(PropsBackpack::getUserId, userId)
          .eq(PropsBackpack::getPropsId, propsId)
          .eq(PropsBackpack::getType, type)
          .last(PageConstant.LIMIT_ONE)
          .execute();
    }

  }

  @Override
  public Boolean countUserNobleProps(Long userId) {
    return query()
        .eq(PropsBackpack::getUserId, userId)
        .eq(PropsBackpack::getType, PropsCommodityType.NOBLE_VIP)
        .gt(PropsBackpack::getExpireTime, TimestampUtils.now()).count() >= 2 ? Boolean.TRUE
        : Boolean.FALSE;
  }

}
