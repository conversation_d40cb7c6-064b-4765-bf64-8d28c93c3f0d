package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.BankIdentityInfoDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.BankIdentityInfo;
import com.red.circle.other.infra.database.rds.service.user.user.BankIdentityInfoService;
import com.red.circle.other.inner.model.cmd.user.UserBankIdentityInfoExportQryCmd;
import com.red.circle.other.inner.model.cmd.user.UserBankIdentityInfoQryCmd;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户身份信息 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-06-15 17:13
 */
@Service
@RequiredArgsConstructor
public class BankIdentityInfoServiceImpl extends
    BaseServiceImpl<BankIdentityInfoDAO, BankIdentityInfo> implements BankIdentityInfoService {

  @Override
  public boolean existsPass(Long userId) {
    return Optional.ofNullable(
            query()
                .select(BankIdentityInfo::getIdentityCover)
                .eq(BankIdentityInfo::getUserId, userId)
                .eq(BankIdentityInfo::getStatus, ApprovalStatusEnum.PASS)
                .last(PageConstant.LIMIT_ONE)
                .getOne()
        ).map(bankIdentityInfo -> Objects.nonNull(bankIdentityInfo.getIdentityCover()))
        .orElse(false);
  }

  @Override
  public BankIdentityInfo getLatest(Long userId) {
    return query()
        .eq(BankIdentityInfo::getUserId, userId)
        .orderByDesc(BankIdentityInfo::getId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }


  @Override
  public PageResult<BankIdentityInfo> pageListQuery(UserBankIdentityInfoQryCmd query) {

    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), BankIdentityInfo::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getUserId()), BankIdentityInfo::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotBlank(query.getStatus()), BankIdentityInfo::getStatus,
            query.getStatus())
        .orderByDesc(BankIdentityInfo::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public List<BankIdentityInfo> listExport(UserBankIdentityInfoExportQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getStatus()), BankIdentityInfo::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getStatus()), BankIdentityInfo::getStatus,
            query.getStatus())
        .ge(Objects.nonNull(query.startTimeToLocalDateTime()), BankIdentityInfo::getCreateTime,
            query.startTimeToLocalDateTime())
        .le(Objects.nonNull(query.endTimeToLocalDateTime()), BankIdentityInfo::getCreateTime,
            query.endTimeToLocalDateTime())
        .list();
  }

  @Override
  public boolean updateInfo(BankIdentityInfo userBankIdentityInfo) {
    return update().set(BankIdentityInfo::getStatus, userBankIdentityInfo.getStatus())
        .set(BankIdentityInfo::getAuditDescription, userBankIdentityInfo.getAuditDescription())
        .set(BankIdentityInfo::getUpdateUser, userBankIdentityInfo.getUpdateUser())
        .set(BankIdentityInfo::getUpdateTime, userBankIdentityInfo.getUpdateTime())
        .eq(BankIdentityInfo::getId, userBankIdentityInfo.getId())
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

}
