package com.red.circle.other.infra.database.rds.enums.pet;

import java.util.Objects;

/**
 * 解锁条件类型.
 *
 * <AUTHOR> on 2021/10/19
 */
public enum PetUnlockConditionTypeEnum {

  /**
   * 财富等级.
   */
  WEALTH_LEVEL,

  /**
   * 魅力等级.
   */
  CHARM_LEVEL,

  /**
   * 金币.
   */
  GOLD,

  /**
   * 钻石.
   */
  DIAMOND,

  /**
   * 豆子.
   */
  BEAN;

  public boolean eq(String type) {
    return Objects.equals(type, this.name());
  }
}
