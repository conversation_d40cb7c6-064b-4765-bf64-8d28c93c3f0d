package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRewardGroup;
import com.red.circle.other.inner.model.cmd.material.PropsActivityRewardGroupQryCmd;
import com.red.circle.other.inner.model.dto.material.ActivityGroupKeyValueDTO;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 道具活动配置分组 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
public interface PropsActivityRewardGroupService extends BaseService<PropsActivityRewardGroup> {

  /**
   * 获取一组资源组信息.
   *
   * @param groupIds 分组id
   * @return list
   */
  List<PropsActivityRewardGroup> listByIds(Collection<Long> groupIds);

  /**
   * 获取资源组信息.
   *
   * @param groupId 分组id
   * @return pojo
   */
  PropsActivityRewardGroup getDataById(Long groupId);

  PageResult<PropsActivityRewardGroup> pagePropsActivityRewardGroup(PropsActivityRewardGroupQryCmd query);

  void offShelf(Long id, Boolean offShelf);

  List<ActivityGroupKeyValueDTO> getActivityGroupKeyValueList();
}
