package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.LoginLoggerDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.LoginLogger;
import com.red.circle.other.infra.database.rds.service.user.user.LoginLoggerService;
import com.red.circle.other.inner.model.cmd.user.LoginLogQryCmd;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户登陆日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
@Service
public class LoginLoggerServiceImpl extends BaseServiceImpl<LoginLoggerDAO, LoginLogger> implements
    LoginLoggerService {

  @Override
  public PageResult<LoginLogger> pageLoginLogger(LoginLogQryCmd cmd) {
    return query()
        .eq(Objects.nonNull(cmd.getUserId()), LoginLogger::getUserId, cmd.getUserId())
        .eq(StringUtils.isNotBlank(cmd.getDeviceId()), LoginLogger::getDeviceId, cmd.getDeviceId())
        .orderByDesc(LoginLogger::getCreateTime)
        .page(cmd.getPageQuery());
  }

  @Override
  public LoginLogger getByDeviceId(String deviceId) {
    return query().eq(LoginLogger::getDeviceId,deviceId)
        .eq(LoginLogger::getLoginType,1)
        .last(PageConstant.LIMIT_ONE).getOne();
  }


}
