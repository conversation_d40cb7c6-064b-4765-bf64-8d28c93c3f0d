package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserRelationshipCounter;
import com.red.circle.other.infra.enums.user.user.UserRelationshipCounterEnum;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户关系计算器 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-20
 */
public interface UserRelationshipCounterService extends BaseService<UserRelationshipCounter> {

  /**
   * 累计数量.
   *
   * @param userId 用户id
   * @param type   类型
   */
  void incrQuantity(Long userId, UserRelationshipCounterEnum type);

  /**
   * 累计数量， 一组用户.
   *
   * @param userIds 用户id
   * @param type    类型
   */
  void incrQuantity(Set<Long> userIds, UserRelationshipCounterEnum type);

  /**
   * 递减数量.
   *
   * @param userId 用户id
   * @param type   类型
   */
  void decrQuantity(Long userId, UserRelationshipCounterEnum type);

  /**
   * 递减数量， 不存在不创建.
   */
  void decrQuantityNotCreate(Set<Long> userIds, UserRelationshipCounterEnum type);

  /**
   * 获取订阅数量.
   *
   * @param userId 用户id
   * @param type   类型
   * @return ignore
   */
  Long getQuantity(Long userId, UserRelationshipCounterEnum type);

  /**
   * 获取计数信息.
   *
   * @param userId 用户id
   * @return 集合
   */
  List<UserRelationshipCounter> getRelationshipCounter(Long userId);

  /**
   * 查询用户粉丝数量.
   *
   * @param userIds 用户集合
   * @return ignore
   */
  Map<Long, Long> mapFansNumberByUserId(Set<Long> userIds);
}
