package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.CheckInLog;
import com.red.circle.other.inner.model.cmd.user.UserCheckInLogQryCmd;

/**
 * <p>
 * 用户签到日志 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-08
 */
public interface CheckInLogService extends BaseService<CheckInLog> {

  PageResult<CheckInLog> getCheckInLog(UserCheckInLogQryCmd query);
}
