package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.common.business.dto.cmd.PageUserIdCmd;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysImAccountDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysImAccount;
import com.red.circle.other.infra.database.rds.service.sys.SysImAccountService;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * im登录账号 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Service
@RequiredArgsConstructor
public class SysImAccountServiceImpl extends
    BaseServiceImpl<SysImAccountDAO, SysImAccount> implements SysImAccountService {

  @Override
  public SysImAccount getSysImByUserId(Long userId) {
    return query().eq(SysImAccount::getUserId, userId).last(PageConstant.LIMIT_ONE).getOne();
  }

  @Override
  public PageResult<SysImAccount> pageImAccountVo(PageUserIdCmd cmd) {
    return query()
        .eq(Objects.nonNull(cmd.getUserId()), SysImAccount::getUserId, cmd.getUserId())
        .orderByDesc(SysImAccount::getCreateTime)
        .page(cmd.getPageQuery());
  }


  @Override
  public void deleteImAccount(Long userId) {
    delete().eq(SysImAccount::getUserId, userId).execute();
  }

}
