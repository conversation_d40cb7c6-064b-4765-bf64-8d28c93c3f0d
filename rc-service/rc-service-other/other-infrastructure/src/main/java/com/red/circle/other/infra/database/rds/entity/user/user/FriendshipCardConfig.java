package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户友谊卡片配置.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_friendship_card_config")
public class FriendshipCardConfig extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId("id")
  private Long id;

  /**
   * 归属平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 卡片图片.
   */
  @TableField("card_back")
  private String cardBack;

  /**
   * 卡片类型.
   */
  @TableField("card_type")
  private String cardType;

  /**
   * 0.下架 1.上架.
   */
  @TableField("is_showcase")
  private Boolean showcase;

  /**
   * 卡片金额.
   */
  @TableField("card_money")
  private BigDecimal cardMoney;

}
