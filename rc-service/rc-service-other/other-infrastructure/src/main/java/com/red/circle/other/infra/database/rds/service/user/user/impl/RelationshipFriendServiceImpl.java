package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.RelationshipFriendDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.RelationshipFriend;
import com.red.circle.other.infra.database.rds.service.user.user.RelationshipFriendService;
import com.red.circle.other.inner.model.dto.user.PetRelationshipFriendDTO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户好友关系 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-07
 */
@Service
@RequiredArgsConstructor
public class RelationshipFriendServiceImpl extends
        BaseServiceImpl<RelationshipFriendDAO, RelationshipFriend> implements
        RelationshipFriendService {

    private final RelationshipFriendDAO relationshipFriendDAO;

    @Override
    public List<PetRelationshipFriendDTO> listPetBeanTop(Long userId, Integer topSize) {
        return relationshipFriendDAO.findPetBeanTop(userId, topSize);
    }

    @Override
    public List<RelationshipFriend> flowSize1000(Long userId, Long lastId, Long friendUserId) {
        return query()
                .eq(RelationshipFriend::getUserId, userId)
                .lt(Objects.nonNull(lastId), RelationshipFriend::getId, lastId)
                .eq(Objects.nonNull(friendUserId), RelationshipFriend::getFriendUserId, friendUserId)
                .orderByDesc(RelationshipFriend::getId)
                .last(PageConstant.formatLimit(1000))
                .list();
    }

    @Override
    public Long todayFlowSize(Long userId) {
        // 获取当天的开始时间和结束时间
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);

        // 定义日期时间格式化器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将 LocalDateTime 格式化为字符串
        String startTimeStr = startOfDay.format(formatter);
        String endTimeStr = endOfDay.format(formatter);

        return query()
                .eq(RelationshipFriend::getUserId, userId)
                .ge(RelationshipFriend::getCreateTime, startTimeStr)
                .le(RelationshipFriend::getCreateTime, endTimeStr)
                .orderByDesc(RelationshipFriend::getId)
                .count();
    }

    @Override
    public Boolean checkFriend(Long userId, Long friendUserId) {
        return Optional.ofNullable(
                query()
                        .select(RelationshipFriend::getId)
                        .eq(RelationshipFriend::getUserId, userId)
                        .eq(RelationshipFriend::getFriendUserId, friendUserId)
                        .last(PageConstant.LIMIT_ONE)
                        .getOne()
        ).map(relationshipFriend -> Objects.nonNull(relationshipFriend.getId())).orElse(Boolean.FALSE);
    }

    @Override
    public void removeFriend(Long userId, Long friendUserId) {
        delete()
                .eq(RelationshipFriend::getUserId, userId)
                .eq(RelationshipFriend::getFriendUserId, friendUserId)
                .last(PageConstant.LIMIT_ONE)
                .execute();
        delete()
                .eq(RelationshipFriend::getUserId, friendUserId)
                .eq(RelationshipFriend::getFriendUserId, userId)
                .last(PageConstant.LIMIT_ONE)
                .execute();
    }

    @Override
    public Long getIdByFriendUserId(Long userId, Long friendUserId) {
        return Optional.ofNullable(
                query()
                        .select(RelationshipFriend::getId)
                        .eq(RelationshipFriend::getUserId, userId)
                        .eq(RelationshipFriend::getFriendUserId, friendUserId)
                        .last(PageConstant.LIMIT_ONE)
                        .getOne()
        ).map(RelationshipFriend::getId).orElse(null);
    }


}
