package com.red.circle.other.infra.enums.game;

import java.util.Objects;

/**
 * 转盘游戏状态.
 *
 * <AUTHOR> on 2021/3/19
 */
public enum TurntableGameStatus {

  /**
   * 等待中.
   */
  WAIT,

  /**
   * 进行中.
   */
  PROCESSING,

  /**
   * 游戏结束.
   */
  COMPLETE,

  /**
   * 自动结束.
   */
  AUTO_END;

  public static boolean checkGameClose(String status) {
    return Objects.equals(TurntableGameStatus.COMPLETE.name(), status)
        || Objects.equals(TurntableGameStatus.AUTO_END.name(), status);
  }

  public static boolean isWait(String status) {
    return Objects.equals(TurntableGameStatus.WAIT.name(), status);
  }

}
