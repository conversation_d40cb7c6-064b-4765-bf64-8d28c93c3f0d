package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsStealthBrowse;
import java.sql.Timestamp;

/**
 * <p>
 * 隐身浏览 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-27
 */
public interface PropsStealthBrowseService extends BaseService<PropsStealthBrowse> {

  /**
   * 添加或者修改.
   *
   * @param userId     用户id
   * @param appendDays 追加天数
   * @return true 成功 ，false 失败
   */
  boolean saveOrUpdate(Long userId, Integer appendDays);

  /**
   * 获取用户隐身浏览道具过期时间.
   *
   * @param userId 用户id
   * @return time
   */
  Timestamp getExpireTime(Long userId);

  /**
   * 是否存在未过期隐身浏览道具.
   *
   * @param userId 用户id
   * @return true 存在，false 不存在
   */
  boolean existsNotExpire(Long userId);

}
