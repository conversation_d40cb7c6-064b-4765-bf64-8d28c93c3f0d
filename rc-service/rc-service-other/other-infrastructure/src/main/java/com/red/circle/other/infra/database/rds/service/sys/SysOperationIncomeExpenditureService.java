package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.SysOperationIncomeExpenditure;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 系统操作收支明细 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 * @since pengliang 2021-10-11
 */
@Deprecated
public interface SysOperationIncomeExpenditureService extends
    BaseService<SysOperationIncomeExpenditure> {

  Map<String, String> mapAssociateRemarks(Set<String> associateIds);

}
