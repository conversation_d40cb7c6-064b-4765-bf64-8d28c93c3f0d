package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysCpCabinConfigDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysCpCabinConfig;
import com.red.circle.other.infra.database.rds.service.sys.SysCpCabinConfigService;
import com.red.circle.other.inner.model.cmd.sys.SysCpCabinConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * CP小屋配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-11-13 16:24
 */
@Service
@AllArgsConstructor
public class SysCpCabinConfigServiceImpl extends
    BaseServiceImpl<SysCpCabinConfigDAO, SysCpCabinConfig> implements
    SysCpCabinConfigService {

  @Override
  public PageResult<SysCpCabinConfig> getCpCabin(SysCpCabinConfigQryCmd query) {
    return query().eq(StringUtils.isNotBlank(query.getSysOrigin()),
            SysCpCabinConfig::getSysOrigin, query.getSysOrigin()).orderByDesc(SysCpCabinConfig::getSort)
        .page(query.getPageQuery());
  }


  @Override
  public void removeCpCabin(Long id) {
    removeById(id);
  }

}
