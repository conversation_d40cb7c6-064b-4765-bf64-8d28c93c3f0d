package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteRedPacketRecord;
import com.red.circle.other.inner.model.cmd.user.invite.InviteRedPacketPageQryCmd;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 邀请新用户-红包记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
public interface InviteRedPacketRecordService extends BaseService<InviteRedPacketRecord> {

  InviteRedPacketRecord getUserRedPacketByUndone(Long userId);

  Long getCompleteCount(Long userId);

  Long getCompleteCount(Long userId, Integer dateNumber);

  Long getCompleteCountByIp(String ip,String sysOrigin);

  Long getCompleteCountByImei(String imei,String sysOrigin);

  void incrInviteMemberCount(Long id);

  void incrCurrentAmountById(Long id, BigDecimal amount);

  void updateStatusById(Long id, String status);

  PageResult<InviteRedPacketRecord> pageRecord(InviteRedPacketPageQryCmd cmd);

  InviteRedPacketRecord getViolationOrNotStatus(Long id, boolean status,String sysOrigin);

  /**
   * 修改红包是否违规状态
   * @param id
   * @param status
   */
  void updateViolationOrNotStatus(Long id, boolean status);

  List<InviteRedPacketRecord> getUserIdList(Long userId, Integer topSize,String sysOrigin);

}
