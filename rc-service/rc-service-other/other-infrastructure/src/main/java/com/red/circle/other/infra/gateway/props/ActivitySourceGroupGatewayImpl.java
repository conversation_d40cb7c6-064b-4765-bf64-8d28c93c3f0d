package com.red.circle.other.infra.gateway.props;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.common.business.enums.PropsActivityRewardEnum;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.other.domain.gateway.props.ActivitySourceGroupGateway;
import com.red.circle.other.infra.common.props.PropsResourcesCommon;
import com.red.circle.other.infra.convertor.material.PropsActivityInfraConvertor;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRewardConfig;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRewardGroup;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRuleConfig;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRewardConfigService;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRewardGroupService;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRuleConfigService;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import com.red.circle.other.inner.model.cmd.material.PropsResourcesMergeCmd;
import com.red.circle.other.inner.model.dto.activity.props.ActivityPropsGroup;
import com.red.circle.other.inner.model.dto.activity.props.ActivityPropsRule;
import com.red.circle.other.inner.model.dto.activity.props.ActivityResource;
import com.red.circle.other.inner.model.dto.activity.props.ActivityRewardProps;
import com.red.circle.other.inner.model.dto.material.BadgePictureConfigDTO;
import com.red.circle.other.inner.model.dto.material.EmojiGroupDTO;
import com.red.circle.other.inner.model.dto.material.GiftConfigDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesDTO;
import com.red.circle.other.inner.model.dto.material.PropsResourcesMergeDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.num.ArithmeticUtils;
import com.red.circle.tool.core.parse.DataTypeUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 活动道具配置 实现.
 *
 * <AUTHOR> on 2021/6/21
 */
@Component
@RequiredArgsConstructor
public class ActivitySourceGroupGatewayImpl implements ActivitySourceGroupGateway {

  private final PropsResourcesCommon propsResourcesCommon;
  private final PropsActivityInfraConvertor propsActivityInfraConvertor;
  private final PropsActivityRuleConfigService propsActivityRuleConfigService;
  private final PropsActivityRewardGroupService propsActivityRewardGroupService;
  private final PropsActivityRewardConfigService propsActivityRewardConfigService;

  @Override
  public ActivityPropsRule getByRuleId(Long id) {
    return propsActivityInfraConvertor.toActivityPropsRule(
        propsActivityRuleConfigService.getById(id));
  }

  @Override
  public List<ActivityPropsRule> listRule(SysOriginPlatformEnum sysOrigin,
      PropsActivityTypeEnum activityType) {
    return propsActivityInfraConvertor.toListActivityPropsRule(
        propsActivityRuleConfigService.listRule(sysOrigin, activityType));
  }

  @Override
  public List<ActivityResource> listActivityResource(SysOriginPlatformEnum sysOrigin,
      PropsActivityTypeEnum activityType) {

    List<PropsActivityRuleConfig> ruleConfigs = propsActivityRuleConfigService
        .listRule(sysOrigin, activityType);

    if (CollectionUtils.isEmpty(ruleConfigs)) {
      return CollectionUtils.newArrayList();
    }

    Map<Long, ActivityPropsGroup> activityPropsGroupMap = mapActivityPropsGroup(sysOrigin.name(),
        getGroupIds(ruleConfigs));

    return ruleConfigs.stream().map(ruleConfig -> new ActivityResource()
        .setRule(propsActivityInfraConvertor.toActivityPropsRule(ruleConfig))
        .setPropsGroup(activityPropsGroupMap.get(ruleConfig.getResourceGroupId()))
    ).collect(Collectors.toList());
  }

  @Override
  public List<Long> listActivityResourceGroupIds(SysOriginPlatformEnum sysOrigin,
      PropsActivityTypeEnum activityType) {
    return Optional.ofNullable(propsActivityRuleConfigService
            .listRule(sysOrigin, activityType))
        .map(ruleConfigs -> ruleConfigs.stream()
            .map(PropsActivityRuleConfig::getResourceGroupId)
            .collect(Collectors.toList()))
        .orElse(null);
  }

  @Override
  public ActivityResource getActivityResource(Long ruleId) {
    return Optional.ofNullable(propsActivityRuleConfigService.getById(ruleId))
        .map(ruleConfig -> new ActivityResource()
            .setRule(propsActivityInfraConvertor.toActivityPropsRule(ruleConfig))
            .setPropsGroup(mapActivityPropsGroup(ruleConfig.getSysOrigin(),
                Collections.singleton(ruleConfig.getResourceGroupId()))
                .get(ruleConfig.getResourceGroupId()))
        )
        .orElse(null);
  }

  public Map<Long, ActivityPropsGroup> mapActivityPropsGroup(String sysOrigin,
      Set<Long> groupIds) {

    if (Objects.isNull(sysOrigin) || CollectionUtils.isEmpty(groupIds)) {
      return CollectionUtils.newHashMap();
    }

    List<PropsActivityRewardGroup> rewardGroups = propsActivityRewardGroupService
        .listByIds(groupIds);

    Map<Long, List<PropsActivityRewardConfig>> rewardGroupConfigMap = propsActivityRewardConfigService
        .mapGroupByIds(groupIds);

    PropsResourcesMergeDTO propsResourcesMerge = propsResourcesCommon.mapMergeByIds(
        new PropsResourcesMergeCmd()
            .setSysOrigin(sysOrigin)
            .setPropsIds(getPropsAllIds(rewardGroupConfigMap))
            .setGiftIds(
                getActivityRewardContentToId(rewardGroupConfigMap, PropsActivityRewardEnum.GIFT))
            .setBadgeIds(
                getActivityRewardContentToId(rewardGroupConfigMap, PropsActivityRewardEnum.BADGE))
            .setEmojiIds(
                getActivityRewardContentToId(rewardGroupConfigMap, PropsActivityRewardEnum.EMOJI))
    );

    return rewardGroups.stream()
        .map(rewardGroup -> {
          ActivityPropsGroup activityPropsGroup = propsActivityInfraConvertor
              .toActivityPropsGroup(rewardGroup);

          List<PropsActivityRewardConfig> rewardConfigs = rewardGroupConfigMap
              .get(activityPropsGroup.getId());

          activityPropsGroup.setActivityRewardProps(rewardConfigs.stream()
              .map(rewardConfig -> getActivityRewardProps(propsResourcesMerge, rewardConfig))
              .filter(Objects::nonNull)
              .collect(Collectors.toList()));

          return activityPropsGroup;
        })
        .collect(Collectors.toMap(ActivityPropsGroup::getId, v -> v));
  }

  @Override
  public ActivityPropsGroup getActivityPropsGroup(String sysOrigin, Long groupId) {
    return mapActivityPropsGroup(sysOrigin, Set.of(groupId))
        .get(groupId);
  }

  private ActivityRewardProps getActivityRewardProps(PropsResourcesMergeDTO propsSource,
      PropsActivityRewardConfig rewardConfig) {

    if (PropsActivityRewardEnum.PROPS.eq(rewardConfig.getType())
        || PropsActivityRewardEnum.FRAGMENTS.eq(rewardConfig.getType())
        || PropsActivityRewardEnum.CUSTOMIZE.eq(rewardConfig.getType())) {
      return buildActivityProps(propsSource.getProps(), rewardConfig);
    }

    if (PropsActivityRewardEnum.GIFT.eq(rewardConfig.getType())) {
      return buildActivityGift(propsSource.getGift(), rewardConfig);
    }

    if (PropsActivityRewardEnum.BADGE.eq(rewardConfig.getType())) {
      return buildActivityBadgeConfig(propsSource.getBadge(), rewardConfig);
    }

    if (PropsActivityRewardEnum.SPECIAL_ID.eq(rewardConfig.getType())) {
      return propsActivityInfraConvertor.toActivityRewardProps(rewardConfig);
    }

    if (PropsActivityRewardEnum.EMOJI.eq(rewardConfig.getType())) {
      return buildEmoji(propsSource.getEmoji(), rewardConfig);
    }

    ActivityRewardProps activityRewardProps = buildActivityAmount(rewardConfig);
    if (PropsActivityRewardEnum.GOLD.eq(rewardConfig.getType())){
      activityRewardProps.setCover("https://dev.file.momooline.com/back/money.png");
      activityRewardProps.setQuantity(activityRewardProps.getAmount().intValue());
    }

    return activityRewardProps;
  }

  private ActivityRewardProps buildActivityAmount(
      PropsActivityRewardConfig rewardConfig) {

    ActivityRewardProps activityRewardProps = propsActivityInfraConvertor
        .toActivityRewardProps(rewardConfig);

    activityRewardProps.setQuantity(rewardConfig.getQuantity());
    activityRewardProps.setAmount(new BigDecimal(rewardConfig.getContent()));
    activityRewardProps.setDetailType(rewardConfig.getDetailType());
    return activityRewardProps;
  }

  private ActivityRewardProps buildActivityGift(
      Map<Long, GiftConfigDTO> giftConfigMap, PropsActivityRewardConfig rewardConfig) {

    GiftConfigDTO giftConfig = giftConfigMap.get(DataTypeUtils.toLong(rewardConfig.getContent()));

    ActivityRewardProps activityRewardProps = propsActivityInfraConvertor
        .toActivityRewardProps(rewardConfig);
    activityRewardProps.setCover(giftConfig.getGiftPhoto());
    activityRewardProps.setQuantity(rewardConfig.getQuantity());
    activityRewardProps.setAmount(
        giftConfig.getGiftCandy().multiply(BigDecimal.valueOf(rewardConfig.getQuantity()))
            .setScale(0, RoundingMode.DOWN));
    activityRewardProps.setDetailType(rewardConfig.getDetailType());
    return activityRewardProps;
  }

  private ActivityRewardProps buildEmoji(Map<Long, EmojiGroupDTO> emojiGroupMap,
      PropsActivityRewardConfig rewardConfig) {
    if (CollectionUtils.isEmpty(emojiGroupMap)) {
      return null;
    }

    EmojiGroupDTO group = emojiGroupMap
        .get(DataTypeUtils.toLong(rewardConfig.getContent()));

    // 配置错误
    ResponseAssert.notNull(CommonErrorCode.CONFIGURATION_ERROR, group);
    ActivityRewardProps activityRewardProps = propsActivityInfraConvertor
        .toActivityRewardProps(rewardConfig);
    activityRewardProps.setCover(group.getCover());
    activityRewardProps.setQuantity(rewardConfig.getQuantity());
    activityRewardProps.setAmount(BigDecimal.ZERO);
    activityRewardProps.setDetailType(rewardConfig.getDetailType());
    return activityRewardProps;
  }

  private ActivityRewardProps buildActivityBadgeConfig(
      Map<Long, BadgePictureConfigDTO> badgePictureConfigMap,
      PropsActivityRewardConfig rewardConfig) {
    if (CollectionUtils.isEmpty(badgePictureConfigMap)) {
      return null;
    }
    BadgePictureConfigDTO badgePictureConfig = badgePictureConfigMap
        .get(DataTypeUtils.toLong(rewardConfig.getContent()));

    // 配置错误
    ResponseAssert.notNull(CommonErrorCode.CONFIGURATION_ERROR, badgePictureConfig);
    ActivityRewardProps activityRewardProps = propsActivityInfraConvertor
        .toActivityRewardProps(rewardConfig);
    activityRewardProps.setCover(badgePictureConfig.getSelectUrl());
    activityRewardProps.setQuantity(rewardConfig.getQuantity());
    activityRewardProps.setAmount(BigDecimal.ZERO);
    activityRewardProps.setDetailType(rewardConfig.getDetailType());
    return activityRewardProps;
  }

  private ActivityRewardProps buildActivityProps(
      Map<Long, PropsResourcesDTO> propsSourceRecordMap, PropsActivityRewardConfig rewardConfig) {

    PropsResourcesDTO propsResources =
        propsSourceRecordMap.get(DataTypeUtils.toLong(rewardConfig.getContent()));

    ActivityRewardProps activityRewardProps = propsActivityInfraConvertor
        .toActivityRewardProps(rewardConfig);

    activityRewardProps.setAmount(ArithmeticUtils.toBigDecimal(rewardConfig.getQuantity())
        .multiply(propsResources.getAmount()).setScale(0, RoundingMode.DOWN));
    activityRewardProps.setQuantity(rewardConfig.getQuantity());
    activityRewardProps.setRemark(rewardConfig.getRemark());
    activityRewardProps.setCover(propsResources.getCover());
    activityRewardProps.setDetailType(rewardConfig.getDetailType());
    return activityRewardProps;
  }

  private Set<Long> getPropsAllIds(
      Map<Long, List<PropsActivityRewardConfig>> rewardGroupConfigMap) {
    return getPropsAllIds(
        rewardGroupConfigMap.values().stream().flatMap(Collection::stream).collect(
            Collectors.toList())
    );
  }


  private Set<Long> getPropsAllIds(List<PropsActivityRewardConfig> rewardConfigs) {
    Set<Long> propsIds = getActivityRewardContentToId(rewardConfigs, PropsActivityRewardEnum.PROPS);

    Set<Long> fragmentIds = getActivityRewardContentToId(rewardConfigs,
        PropsActivityRewardEnum.FRAGMENTS);

    Set<Long> customizeIds = getActivityRewardContentToId(rewardConfigs,
        PropsActivityRewardEnum.CUSTOMIZE);

    return Stream.of(propsIds, fragmentIds, customizeIds)
        .filter(CollectionUtils::isNotEmpty)
        .flatMap(Collection::stream)
        .collect(Collectors.toSet());
  }

  private Set<Long> getActivityRewardContentToId(
      Map<Long, List<PropsActivityRewardConfig>> rewardGroupConfigMap,
      PropsActivityRewardEnum type) {
    return rewardGroupConfigMap.values().stream().flatMap(Collection::stream)
        .filter(conf -> type.eq(conf.getType()))
        .map(propsActivityRewardConfig -> DataTypeUtils
            .toLong(propsActivityRewardConfig.getContent()))
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }

  private Set<Long> getActivityRewardContentToId(
      List<PropsActivityRewardConfig> rewardGroupConfigList,
      PropsActivityRewardEnum type) {
    if (CollectionUtils.isEmpty(rewardGroupConfigList)) {
      return CollectionUtils.newHashSet();
    }
    return rewardGroupConfigList.stream()
        .filter(conf -> type.eq(conf.getType()))
        .map(propsActivityRewardConfig -> DataTypeUtils
            .toLong(propsActivityRewardConfig.getContent()))
        .filter(Objects::nonNull)
        .collect(Collectors.toSet());
  }


  private Set<Long> getGroupIds(List<PropsActivityRuleConfig> ruleConfigs) {
    return ruleConfigs.stream()
        .map(PropsActivityRuleConfig::getResourceGroupId)
        .collect(Collectors.toSet());
  }

}
