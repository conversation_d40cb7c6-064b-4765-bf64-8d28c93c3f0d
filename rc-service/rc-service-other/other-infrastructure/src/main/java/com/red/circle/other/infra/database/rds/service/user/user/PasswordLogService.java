package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.PasswordLog;
import com.red.circle.other.inner.model.cmd.user.UserPasswordLogQryCmd;

/**
 * <p>
 * 用户密码日志 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-07 12:10
 */
public interface PasswordLogService extends BaseService<PasswordLog> {

  PageResult<PasswordLog> pageListQuery(UserPasswordLogQryCmd query);
}
