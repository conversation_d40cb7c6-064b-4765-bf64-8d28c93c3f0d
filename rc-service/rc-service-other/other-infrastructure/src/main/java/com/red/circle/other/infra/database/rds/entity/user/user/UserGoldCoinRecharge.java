package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户充值详情表
 * <AUTHOR> on 2024/3/8
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_gold_coin_recharge")
public class UserGoldCoinRecharge extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 标识.
   */
  @TableId(value = "id", type = IdType.INPUT)
  private Long id;

  /**
   * 来源平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 充值用户.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 金币来源 1：商店充值 2：币商充值 3：H5充值.
   */
  @TableField("gold_coin_source")
  private Integer goldCoinSource;

  /**
   * 充值金额
   */
  @TableField("recharge_amount")
  private BigDecimal rechargeAmount;
}
