package com.red.circle.other.infra.database.rds.service.pet;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.pet.PetFeedingRecord;
import com.red.circle.other.inner.model.cmd.pet.PetFeedingRecordCmd;
import com.red.circle.other.inner.model.dto.pet.PetFeedingRecordDTO;

import java.util.List;

/**
 * <p>
 * 宠物喂食记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface PetFeedingRecordService extends BaseService<PetFeedingRecord> {

  /**
   * 获取用户喂养记录.
   *
   * @param userId 用户id
   * @param lastId 最后一条记录id
   * @return list
   */
  List<PetFeedingRecord> flow(Long userId, Long lastId);


  /**
   * 获取喂养分页列表.
   *
   * @param  cmd 查询
   * @return list
   */
  PageResult<PetFeedingRecordDTO> feedingPage(PetFeedingRecordCmd cmd);


}
