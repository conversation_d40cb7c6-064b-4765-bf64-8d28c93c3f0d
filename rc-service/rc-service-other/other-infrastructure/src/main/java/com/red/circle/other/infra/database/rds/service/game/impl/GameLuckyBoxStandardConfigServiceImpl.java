package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxStandardConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxStandardConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxStandardConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckBoxStandardConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖规格配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxStandardConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxStandardConfigDAO, GameLuckyBoxStandardConfig> implements
    GameLuckyBoxStandardConfigService {

  @Override
  public List<GameLuckyBoxStandardConfig> listBySysOrigin(String sysOrigin) {
    return query().eq(GameLuckyBoxStandardConfig::getSysOrigin, sysOrigin)
        .eq(GameLuckyBoxStandardConfig::getClose, Boolean.FALSE).list();
  }

  @Override
  public PageResult<GameLuckyBoxStandardConfig> getStandardConfig(
      GameLuckBoxStandardConfigQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameLuckyBoxStandardConfig::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getLotteryType()),
            GameLuckyBoxStandardConfig::getLotteryType, query.getLotteryType())
        .eq(Objects.nonNull(query.getClosed()), GameLuckyBoxStandardConfig::getClose,
            query.getClosed())
        .orderByDesc(GameLuckyBoxStandardConfig::getCreateTime)
        .page(query.getPageQuery());
  }


  @Override
  public void switchStatus(Long id, Boolean status) {
    update().set(GameLuckyBoxStandardConfig::getClose, status)
        .eq(GameLuckyBoxStandardConfig::getId, id).execute();
  }
}
