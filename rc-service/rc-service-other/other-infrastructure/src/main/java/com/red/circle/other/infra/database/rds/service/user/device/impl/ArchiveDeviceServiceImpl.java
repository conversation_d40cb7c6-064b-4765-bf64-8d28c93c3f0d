package com.red.circle.other.infra.database.rds.service.user.device.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.ResponseErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.device.ArchiveDeviceDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.ArchiveDevice;
import com.red.circle.other.infra.database.rds.service.user.device.ArchiveDeviceService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.inner.model.cmd.user.device.ArchiveDeviceQryCmd;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 封禁设备 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
@Service
public class ArchiveDeviceServiceImpl extends
    BaseServiceImpl<ArchiveDeviceDAO, ArchiveDevice> implements ArchiveDeviceService {

  @Override
  public boolean existsDevice(String deviceNo, String sysOrigin) {
    if (StringUtils.isBlank(deviceNo) || StringUtils.isBlank(sysOrigin)) {
      return Boolean.FALSE;
    }
    return Optional.ofNullable(query()
            .select(ArchiveDevice::getId)
            .eq(ArchiveDevice::getDeviceNo, deviceNo)
            .eq(ArchiveDevice::getSysOrigin, sysOrigin)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(archiveDevice -> Objects.nonNull(archiveDevice.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public void addDeviceNo(String deviceNo, String sysOrigin) {
    if (StringUtils.isNotBlank(deviceNo) && StringUtils.isNotBlank(sysOrigin)
        && Boolean.TRUE.equals(!exists(deviceNo, sysOrigin))) {
      save(new ArchiveDevice()
          .setSysOrigin(sysOrigin)
          .setDeviceNo(deviceNo)
      );
    }
  }

  @Override
  public PageResult<ArchiveDevice> pageList(ArchiveDeviceQryCmd query) {

    return query()
        .eq(StringUtils.isNotBlank(query.getDeviceNo()), ArchiveDevice::getDeviceNo,
            query.getDeviceNo())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), ArchiveDevice::getSysOrigin,
            query.getSysOrigin())
        .orderByDesc(ArchiveDevice::getId)
        .page(query.getPageQuery());
  }

  @Override
  public void addBatchDeviceNo(Set<String> deviceNos, String sysOrigin) {
    if (CollectionUtils.isEmpty(deviceNos) || StringUtils.isBlank(sysOrigin)) {
      return;
    }
    saveBatch(
        deviceNos.stream().map(deviceNo -> {
              ArchiveDevice archiveDevice = new ArchiveDevice()
                  .setDeviceNo(deviceNo);
              archiveDevice.setSysOrigin(sysOrigin);
              archiveDevice.setCreateTime(TimestampUtils.now());
              archiveDevice.setUpdateTime(TimestampUtils.now());
              archiveDevice.setCreateUser(0L);
              archiveDevice.setUpdateUser(0L);
              return archiveDevice;
            })
            .collect(Collectors.toList())
    );
  }

  @Override
  public void removeDeviceNo(String deviceNo, String sysOrigin) {
    delete()
        .eq(ArchiveDevice::getDeviceNo, deviceNo)
        .eq(ArchiveDevice::getSysOrigin, sysOrigin)
        .execute();
  }

  @Override
  public void delByDeviceNo(Long id) {
    if (Objects.isNull(id)) {
      ResponseAssert.failure(ResponseErrorCode.REQUEST_PARAMETER_ERROR);
    }
    delete()
        .eq(ArchiveDevice::getId, id)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  private Boolean exists(String deviceNo, String sysOrigin) {
    return Optional.ofNullable(
            query().select(ArchiveDevice::getId)
                .eq(ArchiveDevice::getDeviceNo, deviceNo)
                .eq(ArchiveDevice::getSysOrigin, sysOrigin)
                .last(PageConstant.LIMIT_ONE)
                .getOne()
        ).map(sysArchiveDevice -> Objects.nonNull(sysArchiveDevice.getId()))
        .orElse(Boolean.FALSE);
  }
}
