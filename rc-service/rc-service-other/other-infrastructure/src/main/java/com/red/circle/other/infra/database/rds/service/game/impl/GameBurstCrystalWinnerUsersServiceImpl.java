package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameBurstCrystalWinnerUsersDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameBurstCrystalWinnerUsers;
import com.red.circle.other.infra.database.rds.service.game.GameBurstCrystalWinnerUsersService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 爆水晶游戏获奖名单 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-01
 */
@Service
public class GameBurstCrystalWinnerUsersServiceImpl extends
    BaseServiceImpl<GameBurstCrystalWinnerUsersDAO, GameBurstCrystalWinnerUsers> implements
    GameBurstCrystalWinnerUsersService {

  @Override
  public Map<Long, List<GameBurstCrystalWinnerUsers>> mapGroupByGameId(Set<Long> gameIds) {
    return CollectionUtils.isEmpty(gameIds)
        ? Maps.newHashMap()
        : Optional.ofNullable(query().in(GameBurstCrystalWinnerUsers::getGameId, gameIds)
                .orderByAsc(GameBurstCrystalWinnerUsers::getRank).list())
            .map(gameBurstCrystalWinnerUsers -> gameBurstCrystalWinnerUsers.stream().collect(
                Collectors.groupingBy(GameBurstCrystalWinnerUsers::getGameId)))
            .orElseGet(Maps::newHashMap);
  }
}
