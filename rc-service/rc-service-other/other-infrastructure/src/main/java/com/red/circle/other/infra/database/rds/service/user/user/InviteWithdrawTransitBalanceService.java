package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteWithdrawTransitBalance;
import com.red.circle.other.inner.model.cmd.user.invite.InviteBalancePageQryCmd;
import java.math.BigDecimal;

/**
 * <p>
 * 邀请新用户-提现中转账户 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
public interface InviteWithdrawTransitBalanceService extends
    BaseService<InviteWithdrawTransitBalance> {

  boolean decr(Long id, String sysOrigin, BigDecimal expenditure);

  boolean incr(Long id, String sysOrigin, BigDecimal income);

  PageResult<InviteWithdrawTransitBalance> pageWithdrawTransitBalance(InviteBalancePageQryCmd cmd);

}
