package com.red.circle.other.infra.database.rds.enums.h5;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum MemberActiveEnum {
    TASK_1(1, 1902191349119520770L, 1, "裂变活动-注册奖励"),
    TASK_2(2, 1902191554019659778L, 1, "裂变活动-上麦10分钟奖励"),
    TASK_3(3, 1902191918441762817L, 1, "裂变活动-首充奖励"),

    TASK_4(4, 1902192037237035009L, 2, "裂变活动-邀请30人奖励"),
    TASK_5(5, 1902192115381112834L, 2, "裂变活动-邀请10人奖励"),
    TASK_6(6, 1902192217889902593L, 2, "裂变活动-邀请5人奖励");

    private final int taskId;
    private final long rewardId;
    private final int taskType;
    private final String name;

    MemberActiveEnum(int taskId, long rewardId, int taskType, String name) {
        this.taskId = taskId;
        this.rewardId = rewardId;
        this.taskType = taskType;
        this.name = name;
    }

    public static List<MemberActiveEnum> getTasksByType(int type) {
        List<MemberActiveEnum> tasks = new ArrayList<>();
        for (MemberActiveEnum task : MemberActiveEnum.values()) {
            if (task.getTaskType() == type) {
                tasks.add(task);
            }
        }
        return tasks;
    }
//    public static List<MemberActiveEnum> getTasks(String name) {
//        List<MemberActiveEnum> tasks = new ArrayList<>();
//        for (MemberActiveEnum task : MemberActiveEnum.values()) {
//            if (task.name().equals(name)) {
//                tasks.add(task);
//            }
//        }
//        return tasks;
//    }
}
