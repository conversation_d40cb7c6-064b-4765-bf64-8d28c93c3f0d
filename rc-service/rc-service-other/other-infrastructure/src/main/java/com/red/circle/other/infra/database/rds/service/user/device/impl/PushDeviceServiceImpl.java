package com.red.circle.other.infra.database.rds.service.user.device.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.device.PushDeviceDAO;
import com.red.circle.other.infra.database.rds.entity.user.device.PushDevice;
import com.red.circle.other.infra.database.rds.service.user.device.PushDeviceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 推送服务 实现.
 *
 * <AUTHOR> on 2023/5/31
 */
@Service
@RequiredArgsConstructor
public class PushDeviceServiceImpl extends BaseServiceImpl<PushDeviceDAO, PushDevice> implements
    PushDeviceService {

  @Override
  public void saveUnique(PushDevice pushDevice) {
    delete()
        .eq(PushDevice::getSysOrigin, pushDevice.getSysOrigin())
        .eq(PushDevice::getDeviceNo, pushDevice.getDeviceNo())
        .execute();
    save(pushDevice);
  }

}
