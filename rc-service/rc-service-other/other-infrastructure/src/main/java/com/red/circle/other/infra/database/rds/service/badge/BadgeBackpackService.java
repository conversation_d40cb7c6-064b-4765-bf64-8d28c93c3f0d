package com.red.circle.other.infra.database.rds.service.badge;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.badge.BadgeBackpack;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户所得徽章-背包 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-12
 */
public interface BadgeBackpackService extends BaseService<BadgeBackpack> {

  /**
   * 删除指定成员的指定徽章.
   */
  void deleteBadge(Collection<Long> userIds, Collection<Long> badgeIds);

  /**
   * 修改使用的徽章.
   *
   * @param userId   用户id
   * @param badgeIds 徽章id
   */
  void updateUseBadge(Long userId, Collection<Long> badgeIds);

  /**
   * 卸下用户全部徽章.
   *
   * @param userId 用户id
   */
  void updateUseOffByUserId(Long userId);

  /**
   * 获取正在使用的徽章id.
   *
   * @param userId 用户id
   * @return list
   */
  List<BadgeBackpack> listUseBadgeByUserId(Long userId);

  /**
   * 获取用户徽章背包.
   *
   * @param userId 用户id
   * @return ignore
   */
  List<BadgeBackpack> listByBadgeBackpack(Long userId);

  /**
   * 获取已有的徽章id.
   *
   * @param userId   用户id
   * @param badgeIds 徽章id
   * @return ignore
   */
  List<Long> listExistBadgeIds(Long userId, List<Long> badgeIds);

  /**
   * 用户徽章映射.
   */
  Map<Long, BadgeBackpack> mapBadge(Long userId, Set<Long> badgeIds);

  /**
   * 获取一组用户正在使用徽章.
   */
  List<BadgeBackpack> listUseNotExpireTimeByUserIds(Set<Long> userIds);

  /**
   * 获取一组指定的徽章信息.
   */
  List<BadgeBackpack> listUserNotExpireTimeByBadgeIds(Long userId, Collection<Long> badgeIds);

  /**
   * 激活-永久徽章.
   *
   * @param userId  用户id
   * @param badgeId 徽章id
   * @return true 激活成功 ,false 激活失败
   */
  boolean activationPermanent(Long userId, Long badgeId);

  /**
   * 激活-临时徽章.
   *
   * @param userId  用户id
   * @param badgeId 徽章id
   * @param days    天数
   * @return true 激活成功 ,false 激活失败
   */
  boolean activationTemporary(Long userId, Long badgeId, Integer days);

  /**
   * 移除用户徽章.
   *
   * @param userId   用户id
   * @param badgeIds 徽章id
   */
  void deleteBadges(Long userId, Set<Long> badgeIds);

  /**
   * 删除用户徽章
   *
   * @param badgesId 徽章ID
   * @param userId   用户ID
   */
  void deleteUserBadge(Long userId, Long badgesId);

  /**
   * 根据主键标识卸下佩戴的徽章
   *
   * @param ids 主键ID
   */
  void updateOffByIds(Set<Long> ids);

  /**
   * 获得用户徽章.
   *
   * @param userId  用户id
   * @param badgeId 徽章类型
   * @return ignore
   */
  BadgeBackpack getByUserIdByBadgeId(Long userId, Long badgeId);

  /**
   * 激活-永久徽章并佩戴.
   *
   * @param userId  用户id
   * @param badgeId 徽章id
   * @return true 激活成功 ,false 激活失败
   */
  boolean activationPermanentAndUse(Long userId, Long badgeId);

  /**
   * 激活-临时徽章.
   *
   * @param userId  用户id
   * @param badgeId 徽章id
   * @param days    天数
   * @return true 激活成功 ,false 激活失败
   */
  boolean activationTemporaryAndUse(Long userId, Long badgeId, Integer days);

  /**
   * 将一组用户徽章到期时间减少指定天数，并且将其卸下.
   *
   * @param userId     用户.
   * @param badgeIds   徽章ids
   * @param reduceDays 将有效时间减少*天.
   */
  void reduceDaysAndUnUse(Long userId, Set<Long> badgeIds, Integer reduceDays);

  /**
   * 根据主键标识卸下佩戴的徽章
   *
   * @param ids 主键ID
   */
  void removeWearByIds(Set<Long> ids);

  List<BadgeBackpack> listBadge(Long userId);

  /**
   * 获得用户佩戴的徽章
   *
   * @param userId 用户
   * @return 徽章
   */
  List<BadgeBackpack> listWearByUserId(Long userId);

}
