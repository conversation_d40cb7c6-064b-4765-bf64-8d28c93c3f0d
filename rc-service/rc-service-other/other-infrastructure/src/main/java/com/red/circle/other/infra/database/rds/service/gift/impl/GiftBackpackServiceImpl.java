package com.red.circle.other.infra.database.rds.service.gift.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.gift.GiftBackpackDAO;
import com.red.circle.other.infra.database.rds.entity.gift.GiftBackpack;
import com.red.circle.other.infra.database.rds.service.gift.GiftBackpackService;
import com.red.circle.other.inner.model.dto.game.GameLuckyBoxTicketCountInfoDTO;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户礼物背包 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-11
 */
@Service
public class GiftBackpackServiceImpl extends
    BaseServiceImpl<GiftBackpackDAO, GiftBackpack> implements GiftBackpackService {

  @Override
  public void deleteByIds(List<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return;
    }
    delete().in(GiftBackpack::getId, ids).execute();
  }

  @Override
  public List<GiftBackpack> listUserGiftBackPack(Long userId) {
    return query()
        .eq(GiftBackpack::getUserId, userId)
        .apply("obtain_quantity>consume_quantity")
        .orderByDesc(GiftBackpack::getId)
        .list();
  }

  @Override
  public GiftBackpack incrGift(Long userId, Long giftId, Integer quantity) {
    GiftBackpack giftBackpack = getGift(userId, giftId);

    if (Objects.isNull(giftBackpack)) {
      GiftBackpack newGift = new GiftBackpack()
          .setUserId(userId)
          .setGiftId(giftId)
          .setObtainQuantity(quantity)
          .setConsumeQuantity(0);
      super.save(newGift);
      return newGift;
    }

    update().set(GiftBackpack::getUpdateTime, TimestampUtils.now())
        .setSql("obtain_quantity=obtain_quantity+" + quantity)
        .eq(GiftBackpack::getGiftId, giftId)
        .eq(GiftBackpack::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .execute();
    giftBackpack.setObtainQuantity(giftBackpack.getObtainQuantity() + quantity);
    return giftBackpack;
  }

  private GiftBackpack getGift(Long userId, Long giftId) {
    return query().eq(GiftBackpack::getUserId, userId)
        .eq(GiftBackpack::getGiftId, giftId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public boolean consumerGifts(Long id, Long giftId, Long userId, Integer quantity) {
    return update().set(GiftBackpack::getUpdateTime, TimestampUtils.now())
        .setSql("consume_quantity=consume_quantity+" + quantity)
        .apply("obtain_quantity>=consume_quantity+" + quantity)
        .eq(GiftBackpack::getUserId, userId)
        .eq(GiftBackpack::getGiftId, giftId)
        .eq(GiftBackpack::getId, id)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public GiftBackpack getUserGift(Long userId, Long giftId) {
    return getGift(userId, giftId);
  }

  @Override
  public void deductGift(Long userId, Long giftId, Integer quantity) {

    if (Objects.isNull(quantity)) {
      return;
    }

    GiftBackpack giftBackpack = getGift(userId, giftId);
    if (Objects.isNull(giftBackpack)) {
      return;
    }

    if (giftBackpack.calculationQuantity() <= 0) {
      return;
    }

    if (giftBackpack.calculationQuantity() - quantity > 0) {

      update()
          .set(GiftBackpack::getUpdateTime, TimestampUtils.now())
          .setSql("obtain_quantity=obtain_quantity-" + quantity)
          .eq(GiftBackpack::getId, giftBackpack.getId())
          .last(PageConstant.LIMIT_ONE)
          .execute();
      return;
    }

    update()
        .set(GiftBackpack::getUpdateTime, TimestampUtils.now())
        .set(GiftBackpack::getObtainQuantity, 0)
        .eq(GiftBackpack::getId, giftBackpack.getId())
        .last(PageConstant.LIMIT_ONE)
        .execute();

  }


  @Override
  public GameLuckyBoxTicketCountInfoDTO ticketCount(Long giftId, Long userId) {
    return baseDAO.ticketCount(giftId, userId);
  }

}
