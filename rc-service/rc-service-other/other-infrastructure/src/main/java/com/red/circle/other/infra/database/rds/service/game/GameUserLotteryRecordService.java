package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameUserLotteryRecord;
import com.red.circle.other.inner.model.cmd.game.GameEggQryCmd;
import java.util.List;

/**
 * <p>
 * 用户游戏抽奖记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface GameUserLotteryRecordService extends BaseService<GameUserLotteryRecord> {

  /**
   * 分页
   *
   * @param userId   用户id
   * @param isSingle 是否为单次抽奖
   * @param gameType 游戏类型
   */
  List<GameUserLotteryRecord> listByUserId(Long userId, Boolean isSingle, String gameType);

  /**
   * 最新24小时内榜单
   */
  List<GameUserLotteryRecord> listTop20(String sysOrigin, String gameType);

  PageResult<GameUserLotteryRecord> pageRecord(GameEggQryCmd query);
}
