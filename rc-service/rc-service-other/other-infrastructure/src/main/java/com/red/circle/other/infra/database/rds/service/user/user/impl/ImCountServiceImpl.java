package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.ImCountDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.ImCount;
import com.red.circle.other.infra.database.rds.service.user.user.ImCountService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户im聊天统计 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
@AllArgsConstructor
@Service
public class ImCountServiceImpl extends BaseServiceImpl<ImCountDAO, ImCount> implements
    ImCountService {

  @Override
  public ImCount getByUserIds(Long userId, Long acceptUserId, String originName) {
    return query().eq(ImCount::getUserId, userId).eq(ImCount::getAcceptUserId, acceptUserId)
        .eq(ImCount::getSysOrigin, originName).getOne();
  }

  @Override
  public void updateByIds(Long userId, Long acceptUserId, String originName) {
    update().set(ImCount::getLightLove, Boolean.TRUE)
        .eq(ImCount::getUserId, userId).eq(ImCount::getAcceptUserId, acceptUserId)
        .eq(ImCount::getSysOrigin, originName).execute();
  }
}
