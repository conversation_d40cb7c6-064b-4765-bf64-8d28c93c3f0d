package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.CpCabinConfig;
import java.util.List;

/**
 * <p>
 * CP小屋配置 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-11-20 10:18
 */
public interface CpCabinConfigService extends BaseService<CpCabinConfig> {

  List<CpCabinConfig> getCpCabinConfig(String sysOriginName);
}
