package com.red.circle.other.infra.database.rds.service.live;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.live.RoomSubscriptionCount;

/**
 * <p>
 * 房间订阅数 服务类.
 * </p>
 *
 * <AUTHOR> on 2021-06-02
 */
public interface RoomSubscriptionCountService extends BaseService<RoomSubscriptionCount> {

  /**
   * 给房间订阅数+1或-1
   *
   * @param roomId 房间ID
   * @param isAdd  关注反之取消
   * @return 关注或取消成功
   */
  Boolean saveRoomSubscriptionCount(Long roomId, Boolean isAdd);

}
