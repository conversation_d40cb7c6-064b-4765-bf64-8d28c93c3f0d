package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityRewardSendList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 活动奖励发送清单以及容错撤回奖励 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
public interface ActivityRewardSendListService extends BaseService<ActivityRewardSendList> {

  /**
   * 获得奖励用户记录
   *
   * @param sendRewardTime YYYYMMDD 支持最新一次的数据处理 activity_reward_send_list.send_time.
   * @return 奖励用户记录
   */
  List<ActivityRewardSendList> listUserReward(Integer sendRewardTime);

  /**
   * 修改状态为已撤销
   *
   * @param ids id
   */
  void updateRevokeByIds(Set<Long> ids);


}
