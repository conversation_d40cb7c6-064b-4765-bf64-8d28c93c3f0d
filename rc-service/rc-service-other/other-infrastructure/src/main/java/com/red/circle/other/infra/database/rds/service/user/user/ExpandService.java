package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserExpand;
import com.red.circle.other.inner.model.dto.user.ActiveUserCountryCodeDTO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户扩展信息 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
public interface ExpandService extends BaseService<UserExpand> {

  /**
   * 不存在创建.
   *
   * @param userExpand ignore
   */
  void notExistsCreate(UserExpand userExpand);

  /**
   * 初始化扩展信息.
   *
   * @param userId   用户id
   * @param language 语言
   */
  void init(Long userId, String language);

  /**
   * 获取用户使用语言，默认Locale.US.
   *
   * @param userId 用户id
   * @return 使用的语言
   */
  Locale getLanguage(Long userId);

  /**
   * 获取用户使用语言字符，默认es.
   *
   * @param userId 用户id
   * @return 使用的语言
   */
  String getLanguageStr(Long userId);

  /**
   * 更新最近活跃时间.
   *
   * @param userId 用户id
   */
  void updateLastActiveTime(Long userId);

  /**
   * 修改语言.
   */
  void updateLastActiveTimeAndLange(Long userId, String language);

  /**
   * 获取扩展信息映射.
   *
   * @param userIds 用户id
   * @return 映射信息
   */
  Map<Long, UserExpand> mapExpand(Set<Long> userIds);

  /**
   * 获取扩展信息.
   *
   * @param userIds 用户id
   * @return 扩展信息
   */
  List<UserExpand> listUserExpand(Set<Long> userIds);

  /**
   * 获取解封次数.
   *
   * @param userId 用户id
   * @return 数量
   */
  Integer getUnlockArchiveSize(Long userId);

  /**
   * 累计解封次数.
   *
   * @param userId 用户id
   */
  void incrUnlockArchiveSize(Long userId);

  /**
   * 修改购买状态.
   *
   * @param userId 用户id
   */
  void updatePurchasingToTrue(Long userId);

  /**
   * 是否购买过内购.
   *
   * @param userId ignore
   * @return ignore
   */
  boolean checkPurchasing(Long userId);

  /**
   * 获取用户语言集合映射.
   *
   * @param userIds 用户id集合
   * @return ignore
   */
  Map<Long, String> mapUserLanguageStr(Set<Long> userIds);

  /**
   * 最新两小时在线用户分布国家.
   *
   * @param lastActiveTime ignore
   * @return list
   */
  List<ActiveUserCountryCodeDTO> findLatestActiveUserCountryCode(LocalDateTime lastActiveTime);
}
