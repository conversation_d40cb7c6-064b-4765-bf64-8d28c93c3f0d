package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicContent;
import com.red.circle.other.inner.model.cmd.dynamic.DelDynamicContentCmd;
import com.red.circle.other.inner.model.cmd.dynamic.DynamicContentQryCmd;
import com.red.circle.other.inner.model.dto.dynamic.PageDynamicContentDTO;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 动态-朋友圈内容表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface DynamicContentService extends BaseService<DynamicContent> {

  /**
   * 根据id集合返回动态内容数据
   *
   * @param ids id集合
   * @return 动态内容数据
   */
  Map<Long, DynamicContent> mapByIds(Collection<Long> ids);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   */
  List<DynamicContent> pageByTimeDesc(Integer pageNumber, String region, String sysOrigin);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   * @param userId     创建人
   */
  List<DynamicContent> pageByTimeDesc(Integer pageNumber, Long userId);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   */
  List<DynamicContent> pageByTimeDesc(Long tagId, Integer pageNumber);

  /**
   * 动态内容
   *
   * @param ids id集合
   */
  List<DynamicContent> listByIds(Set<Long> ids);

  /**
   * 获取用户最新发布的一条动态id
   *
   * @param userId 用户id
   * @return 动态ID
   */
  Long getLatestIdByUserId(Long userId);

  /**
   * 获取用户最新发布的一条动态
   *
   * @param userId 用户id
   * @return 动态ID
   */
  DynamicContent getLatestByUserId(Long userId);

  DynamicContent getById(Long id);

  /**
   * 删除
   */
  void deleteById(Long id);

  /**
   * 批量删除
   */
  void deleteByIds(DelDynamicContentCmd cmd);

  /**
   * 根据id获得创建人id
   *
   * @param id 动态id
   * @return 创建人
   */
  Long getCreateUserById(Long id);

  /**
   * 是否存在未被删除的动态
   *
   * @param id 动态id
   * @return true存在
   */
  Boolean isExist(Long id);

  PageResult<PageDynamicContentDTO> page(DynamicContentQryCmd query);
}
