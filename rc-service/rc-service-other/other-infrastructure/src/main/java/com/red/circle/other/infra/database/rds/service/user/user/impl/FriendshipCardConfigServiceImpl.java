package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.FriendshipCardConfigDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.FriendshipCardConfig;
import com.red.circle.other.infra.database.rds.service.user.user.FriendshipCardConfigService;
import com.red.circle.other.inner.model.cmd.user.SysUserFriendshipCardConfigQryCmd;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户友谊卡片配置 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
@Service
@RequiredArgsConstructor
public class FriendshipCardConfigServiceImpl extends
    BaseServiceImpl<FriendshipCardConfigDAO, FriendshipCardConfig> implements
    FriendshipCardConfigService {

  @Override
  public List<FriendshipCardConfig> listConfig(String sysOrigin) {
    return query()
        .eq(FriendshipCardConfig::getSysOrigin, sysOrigin)
        .eq(FriendshipCardConfig::getShowcase, Boolean.TRUE)
        .last(PageConstant.formatLimit(100))
        .list();
  }


  @Override
  public FriendshipCardConfig getConfigByType(String sysOrigin, String type) {
    return query()
        .eq(FriendshipCardConfig::getSysOrigin, sysOrigin)
        .eq(FriendshipCardConfig::getCardType, type)
        .eq(FriendshipCardConfig::getShowcase, Boolean.TRUE)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

  }

  @Override
  public PageResult<FriendshipCardConfig> pageConfig(
      SysUserFriendshipCardConfigQryCmd query) {

    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), FriendshipCardConfig::getSysOrigin,
            query.getSysOrigin())
        .orderByDesc(FriendshipCardConfig::getUpdateTime)
        .page(query.getPageQuery());
  }
}
