package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 告白记录.
 * </p>
 *
 * <AUTHOR> on 2023-11-15 17:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_confession_record")
public class UserConfessionRecord extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键id.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 告白机会id.
   */
  @TableField("confession_chance_id")
  private Long confessionChanceId;

  /**
   * 系统平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 告白对象用户id.
   */
  @TableField("confession_user_id")
  private Long confessionUserId;

  /**
   * 区域.
   */
  @TableField("region_code")
  private String regionCode;

  /**
   * 内容.
   */
  @TableField("content")
  private String content;

  /**
   * 状态.
   */
  @TableField("status")
  private String status;


}
