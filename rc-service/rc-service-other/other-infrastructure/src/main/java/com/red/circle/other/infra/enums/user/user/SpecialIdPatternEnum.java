package com.red.circle.other.infra.enums.user.user;

import lombok.Getter;
import java.util.regex.Pattern;

@Getter
public enum SpecialIdPatternEnum {
    // LV.≥90
    LEVEL_90_AAAA("四位相同数字", "^(\\d)\\1{3}$", 90),

    // LV.≥80
    LEVEL_80_AAAAA("五位相同数字", "^(\\d)\\1{4}$", 80),

    // LV.≥70
    LEVEL_70_AAAB("AAAB模式", "^(\\d)\\1{2}(?!\\1)\\d$", 70),
    LEVEL_70_ABBBB("ABBBB模式", "^(\\d)(?!\\1)(\\d)\\2{3}$", 70),
    LEVEL_70_AAABA("AAABA模式", "^(\\d)\\1{2}(?!\\1)\\d\\1$", 70),
    LEVEL_70_AABAA("AABAA模式", "^(\\d)\\1(?!\\1)\\d\\1{2}$", 70),
    LEVEL_70_AABBBB("AABBBB模式", "^(\\d)\\1(?!\\1)(\\d)\\2{3}$", 70),

    // LV.≥60
    LEVEL_60_AABBB("AABBB模式", "^(\\d)\\1(?!\\1)(\\d)\\2{2}$", 60),
    LEVEL_60_AAAAAA("六位相同数字", "^(\\d)\\1{5}$", 60),
    LEVEL_60_AAAAAB("AAAAAB模式", "^(\\d)\\1{4}(?!\\1)\\d$", 60),

    // LV.≥50
    LEVEL_50_AABBC("AABBC模式", "^(\\d)\\1(?!\\1)(\\d)\\2(?!\\1|\\2)\\d$", 50),
    LEVEL_50_AAABC("AAABC模式", "^(\\d)\\1{2}(?!\\1)(\\d)(?!\\1|\\2)\\d$", 50),
    LEVEL_50_ABABAB("交替模式", "^(\\d)(?!\\1)(\\d)\\1\\2\\1\\2$", 50),
    LEVEL_50_AABBBC("AABBBC模式", "^(\\d)\\1(?!\\1)(\\d)\\2{2}(?!\\1|\\2)\\d$", 50),
    LEVEL_50_ABBBBBB("七位B重复", "^(\\d)(?!\\1)(\\d)\\2{5}$", 50),

    // LV.≥45
    LEVEL_45_AABBA("AABBA模式", "^(\\d)\\1(?!\\1)(\\d)\\2\\1$", 45),
    LEVEL_45_AABBBBB("七位B重复", "^(\\d)\\1(?!\\1)(\\d)\\2{4}$", 45),
    // LV.≥45
    LEVEL_45_AAAAABB("AAAAABB模式", "^(\\d)\\1{4}(\\d)\\2$", 45),
    LEVEL_45_AAAAABA("AAAAABA模式", "^(\\d)\\1{4}(\\d)\\1$", 45),
    // LV.≥40
    LEVEL_40_AAAABA("AAAABA模式", "^(\\d)\\1{3}(\\d)\\1$", 40),
    LEVEL_40_ABCCCC("ABCCCC模式", "^(\\d)(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\3{3}$", 40),
    LEVEL_40_ABBBBA("ABBBBA模式", "^(\\d)(?!\\1)(\\d)\\2{3}\\1$", 40),
    LEVEL_40_AAAABCC("七位模式", "^(\\d)\\1{3}(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\3$", 40),
    // 修改 AAAABBB (原 AAAACCC)
    LEVEL_40_AAAABBB("七位模式", "^(\\d)\\1{3}(?!\\1)(\\d)\\2{2}$", 40),
    
    // 修改 AAABBBB (原 AAACCCC)
    LEVEL_40_AAABBBB("七位模式", "^(\\d)\\1{2}(?!\\1)(\\d)\\2{3}$", 40),
    
    // 修改 ABDCCA -> ABBCCA
    LEVEL_35_ABBCCA("特殊模式", "^(\\d)(?!\\1)(\\d)\\2{2}\\1$", 35),

    // LV.≥35
    LEVEL_35_AAAABC("AAAABC模式", "^(\\d)\\1{3}(?!\\1)(\\d)(?!\\1|\\2)\\d$", 35),
    LEVEL_35_AABBCC("AABBCC模式", "^(\\d)\\1(?!\\1)(\\d)\\2(?!\\1|\\2)(\\d)\\3$", 35),
    // ABCDDA 模式 (如：123441)
    LEVEL_35_ABCDDA("特殊模式", "^(\\d)(?!\\1)(\\d)(?!\\1|\\2)(\\d)(?!\\1|\\2|\\3)(\\d)\\4\\1$", 35),
    
    LEVEL_35_ABCABC("重复模式", "^(\\d)(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\1\\2\\3$", 35),
    LEVEL_35_AABBCCC("七位模式", "^(\\d)\\1(?!\\1)(\\d)\\2(?!\\1|\\2)(\\d)\\3{2}$", 35),

    // LV.≥30
    LEVEL_30_AAABBC("AAABBC模式", "^(\\d)\\1{2}(?!\\1)(\\d)\\2(?!\\1|\\2)\\d$", 30),
    // AAABCC 模式：6位数字，前3位相同(AAA)，第4位不同(B)，最后2位相同(CC)且不等于前面的数字
    
    LEVEL_30_AAABCC("AAABCC模式", "^(\\d)\\1{2}(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\3$", 30),
    // LEVEL_30_AAAABCC("七位模式", "^(\\d)\\1{3}(?!\\1)(\\d)(?!\\1|\\2)(\\d)(?!\\1|\\2|\\3)\\d$", 30),
    // ABCCCAA 模式 (如：1233311)
    LEVEL_30_ABCCCAA("七位模式", "^(\\d)(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\3{2}\\1{2}$", 30),
    // AAAACBD 模式 (如：1111234)
    LEVEL_30_AAAACBD("七位模式", "^(\\d)\\1{3}(?!\\1)(\\d)(?!\\1|\\2)(\\d)(?!\\1|\\2|\\3)(\\d)$", 30),

    // LV.≥25
    LEVEL_25_ABABCC("ABABCC模式", "^(\\d)(?!\\1)(\\d)\\1\\2(?!\\1|\\2)(\\d)\\3$", 25),
    LEVEL_25_AABCCC("AABCCC模式", "^(\\d)\\1(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\3{2}$", 25),
    LEVEL_25_AAABCD("AAABCD模式", "^(\\d)\\1{2}(?!\\1)(\\d)(?!\\1|\\2)(\\d)(?!\\1|\\2|\\3)\\d$", 25),
    // ABBBAC 模式：如 122213
    LEVEL_25_ABBBAC("ABBBAC模式", "^(\\d)(?!\\1)(\\d)\\2{2}\\1(?!\\1|\\2)\\d$", 25),
    LEVEL_25_ABABABC("七位模式", "^(\\d)(?!\\1)(\\d)\\1\\2\\1\\2(?!\\1|\\2)\\d$", 25),
    LEVEL_25_ABCABCA("七位模式", "^(\\d)(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\1\\2\\3\\1$", 25),
    // ABCCCAB 模式：如 123312
    LEVEL_25_ABCCCAB("七位模式", "^(\\d)(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\3{2}\\1\\2$", 25),
    LEVEL_25_AAAABCA("七位模式", "^(\\d)\\1{3}(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\1$", 25),

    // LV.≥20
    LEVEL_20_ABBCCA("ABBCCA模式", "^(\\d)(?!\\1)(\\d)\\2(?!\\1|\\2)(\\d)\\3\\1$", 20),
    // 修改 AABBAC：确保 C 不等于 A 和 B
    // AABBAC 模式：如 112213
    LEVEL_20_AABBAC("AABBAC模式", "^(\\d)\\1(?!\\1)(\\d)\\2\\1(?!\\1|\\2)\\d$", 20),
    LEVEL_20_AAABCDE("七位模式", "^(\\d)\\1{2}(?!\\1)(\\d)(?!\\1|\\2)(\\d)(?!\\1|\\2|\\3)(\\d)(?!\\1|\\2|\\3|\\4)\\d$", 20),

    // ABBBACB 模式：如 1222132
    LEVEL_20_ABBBACB("七位模式", "^(\\d)(?!\\1)(\\d)\\2{2}\\1(?!\\1|\\2)(\\d)\\2$", 20),
    
    LEVEL_20_AABCBBC("七位模式", "^(\\d)\\1(?!\\1)(\\d)(?!\\1|\\2)(\\d)\\2{2}\\3$", 20);

    private final String desc;
    private final String pattern;
    private final Pattern compiledPattern;
    private final int minLevel;

    SpecialIdPatternEnum(String desc, String pattern, int minLevel) {
        this.desc = desc;
        this.pattern = pattern;
        this.compiledPattern = Pattern.compile(pattern);
        this.minLevel = minLevel;
    }

    public boolean matches(String account) {
        return compiledPattern.matcher(account).matches();
    }

    public static boolean isValidForLevel(String account, int userLevel) {
        for (SpecialIdPatternEnum pattern : values()) {
            if (pattern.getMinLevel() <= userLevel && pattern.matches(account)) {
                return true;
            }
        }
        return false;
    }

    public static void main(String[] args) {
        // 测试数据
        String[][] testCases = {
            // 格式：{测试数字, 期望匹配的模式名, 用户等级}
            {"1111", "LEVEL_90_AAAA", "90"},
            {"11111", "LEVEL_80_AAAAA", "80"},
            {"1112", "LEVEL_70_AAAB", "70"},
            {"12222", "LEVEL_70_ABBBB", "70"},
            {"11121", "LEVEL_70_AAABA", "70"},
            {"11211", "LEVEL_70_AABAA", "70"},
            {"112222", "LEVEL_70_AABBBB", "70"},
            {"11222", "LEVEL_60_AABBB", "60"},
            {"111111", "LEVEL_60_AAAAAA", "60"},
            {"111112", "LEVEL_60_AAAAAB", "60"},
            {"11223", "LEVEL_50_AABBC", "50"},
            {"11123", "LEVEL_50_AAABC", "50"},
            {"121212", "LEVEL_50_ABABAB", "50"},
            {"112223", "LEVEL_50_AABBBC", "50"},
            {"1222222", "LEVEL_50_ABBBBBB", "50"},
            {"11221", "LEVEL_45_AABBA", "45"},
            {"1122222", "LEVEL_45_AABBBBB", "45"},
            {"1111122", "LEVEL_45_AAAAABB", "45"},
            {"1111121", "LEVEL_45_AAAAABA", "45"},
            {"111121", "LEVEL_40_AAAABA", "40"},
            {"123333", "LEVEL_40_ABCCCC", "40"},
            {"122221", "LEVEL_40_ABBBBA", "40"},
            {"1111233", "LEVEL_40_AAAABCC", "40"},
            {"1111222", "LEVEL_40_AAAABBB", "40"},
            {"1112222", "LEVEL_40_AAABBBB", "40"},
            {"111123", "LEVEL_35_AAAABC", "35"},
            {"112233", "LEVEL_35_AABBCC", "35"},
            {"123441", "LEVEL_35_ABCDDA", "35"},
            {"123123", "LEVEL_35_ABCABC", "35"},
            {"1122333", "LEVEL_35_AABBCCC", "35"},
            {"111223", "LEVEL_30_AAABBC", "30"},
            {"111233", "LEVEL_30_AAABCC", "30"},

            {"1233311", "LEVEL_30_ABCCCAA", "30"},
            {"1111234", "LEVEL_30_AAAACBD", "30"},
            {"121233", "LEVEL_25_ABABCC", "25"},
            {"112333", "LEVEL_25_AABCCC", "25"},
            {"111234", "LEVEL_25_AAABCD", "25"},
            {"122213", "LEVEL_25_ABBBAC", "25"},
            {"1212123", "LEVEL_25_ABABABC", "25"},
            {"1231231", "LEVEL_25_ABCABCA", "25"},
            {"1233312", "LEVEL_25_ABCCCAB", "25"},
            {"1111231", "LEVEL_25_AAAABCA", "25"},
            {"122331", "LEVEL_20_ABBCCA", "20"},
            {"112213", "LEVEL_20_AABBAC", "20"},
            {"1112345", "LEVEL_20_AAABCDE", "20"},
            {"1222132", "LEVEL_20_ABBBACB", "20"},
            {"1123223", "LEVEL_20_AABCBBC", "20"}
        };

        System.out.println("开始测试所有正则表达式模式...\n");
        
        for (String[] testCase : testCases) {
            String number = testCase[0];
            String expectedPattern = testCase[1];
            int userLevel = Integer.parseInt(testCase[2]);
            
            boolean matched = false;
            String actualPattern = "";
            
            for (SpecialIdPatternEnum pattern : SpecialIdPatternEnum.values()) {
                if (pattern.matches(number)) {
                    matched = true;
                    actualPattern = pattern.name();
                    break;
                }
            }
            
            System.out.printf("测试数字: %s\n", number);
            System.out.printf("期望模式: %s\n", expectedPattern);
            System.out.printf("实际模式: %s\n", actualPattern);
            
            if (!matched || !actualPattern.equals(expectedPattern)) {
                System.out.println("\n测试失败！");
                System.out.println("------------------------");
                System.out.printf("失败详情:\n");
                System.out.printf("- 输入数字: %s\n", number);
                System.out.printf("- 期望模式: %s\n", expectedPattern);
                System.out.printf("- 实际模式: %s\n", actualPattern);
                if (!matched) {
                    System.out.println("- 原因: 没有匹配到任何模式");
                } else {
                    System.out.println("- 原因: 匹配到错误的模式");
                }
                return; // 遇到失败立即停止
            }
            
            System.out.printf("测试结果: 通过\n");
            System.out.printf("用户等级检查: %s\n", isValidForLevel(number, userLevel) ? "通过" : "失败");
            System.out.println("------------------------\n");
        }
        
        System.out.println("所有测试用例通过！");
    }
}
