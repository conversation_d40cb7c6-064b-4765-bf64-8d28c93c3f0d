package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.framework.web.constant.SystemDefaultConstant;
import com.red.circle.other.infra.database.rds.dao.sys.VersionUpdateDescriptionDAO;
import com.red.circle.other.infra.database.rds.entity.sys.VersionUpdateDescription;
import com.red.circle.other.infra.database.rds.service.sys.VersionUpdateDescriptionService;
import com.red.circle.other.inner.asserts.ImErrorCode;
import com.red.circle.other.inner.model.cmd.sys.PageVersionDescQryCmd;
import com.red.circle.other.inner.model.cmd.sys.SysVersionUpdateDescriptionCmd;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * app版本更新描述 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-04
 */
@Service
public class VersionUpdateDescriptionServiceImpl extends
    BaseServiceImpl<VersionUpdateDescriptionDAO, VersionUpdateDescription> implements
    VersionUpdateDescriptionService {

  @Override
  public String getUpdateDescription(Long versionId, String language) {

    VersionUpdateDescription description = query()
        .eq(VersionUpdateDescription::getVersionId, versionId)
        .eq(VersionUpdateDescription::getLanguage, language)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(description)) {
      return Optional.ofNullable(query()
              .eq(VersionUpdateDescription::getVersionId, versionId)
              .eq(VersionUpdateDescription::getLanguage, SystemDefaultConstant.DEFAULT_LANG_STR)
              .last(PageConstant.LIMIT_ONE)
              .getOne()).map(VersionUpdateDescription::getUpdateDescribe)
          .orElse(null);
    }

    return description.getUpdateDescribe();
  }

  @Override
  public PageResult<VersionUpdateDescription> pageDescription(PageVersionDescQryCmd cmd) {

    return query()
        .eq(VersionUpdateDescription::getVersionId, cmd.getVersionId())
        .orderByDesc(VersionUpdateDescription::getCreateTime)
        .page(cmd.getPageQuery());
  }

  @Override
  public boolean add(SysVersionUpdateDescriptionCmd param) {

    VersionUpdateDescription description = getDescription(param);

    ResponseAssert.isNull(ImErrorCode.SAME_LANGUAGE, description);
    return save(new VersionUpdateDescription()
        .setUpdateDescribe(param.getUpdateDescribe())
        .setUpdateWorshipDescribe(param.getUpdateWorshipDescribe())
        .setVersionId(param.getVersionId())
        .setLanguage(param.getLanguage())
    );
  }

  @Override
  public boolean update(SysVersionUpdateDescriptionCmd param) {

    VersionUpdateDescription description = getById(param.getId());
    ResponseAssert.notNull(CommonErrorCode.NOT_FOUND_RECORD_INFO, description);

    description.setUpdateDescribe(param.getUpdateDescribe());
    description.setUpdateWorshipDescribe(param.getUpdateWorshipDescribe());
    description.setLanguage(param.getLanguage());
    return updateSelectiveById(description);

  }

  @Override
  public VersionUpdateDescription getDescription(SysVersionUpdateDescriptionCmd param) {
    return query()
        .eq(VersionUpdateDescription::getVersionId, param.getVersionId())
        .eq(VersionUpdateDescription::getLanguage, param.getLanguage())
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public boolean deleteById(Long id) {
    return delete().eq(VersionUpdateDescription::getId, id).last(PageConstant.LIMIT_ONE).execute();
  }

  @Override
  public String getUpdateWorshipDescription(Long versionId, String language) {
    VersionUpdateDescription description = query()
        .eq(VersionUpdateDescription::getVersionId, versionId)
        .eq(VersionUpdateDescription::getLanguage, language)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(description)) {
      return Optional.ofNullable(query()
              .eq(VersionUpdateDescription::getVersionId, versionId)
              .eq(VersionUpdateDescription::getLanguage, SystemDefaultConstant.DEFAULT_LANG_STR)
              .last(PageConstant.LIMIT_ONE)
              .getOne()).map(VersionUpdateDescription::getUpdateWorshipDescribe)
          .orElse(null);
    }

    return description.getUpdateWorshipDescribe();
  }
}
