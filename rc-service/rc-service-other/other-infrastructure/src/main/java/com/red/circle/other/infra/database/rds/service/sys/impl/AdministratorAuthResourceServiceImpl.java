package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.AdministratorAuthResourceDAO;
import com.red.circle.other.infra.database.rds.entity.sys.AdministratorAuthResource;
import com.red.circle.other.infra.database.rds.service.sys.AdministratorAuthResourceService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * APP权限菜单表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
public class AdministratorAuthResourceServiceImpl extends
    BaseServiceImpl<AdministratorAuthResourceDAO, AdministratorAuthResource> implements
    AdministratorAuthResourceService {


  @Override
  public List<AdministratorAuthResource> listUserAuthResource(List<Long> resourceIds) {
    return CollectionUtils.isEmpty(resourceIds) ? null : query()
        .in(AdministratorAuthResource::getId, resourceIds)
        .list();
  }

  @Override
  public List<AdministratorAuthResource> getSysAdministratorAuthResource() {
    List<AdministratorAuthResource> sysAdministratorAuthResources = query().list();
    if (CollectionUtils.isEmpty(sysAdministratorAuthResources)) {
      return Lists.newArrayList();
    }
    return sysAdministratorAuthResources;
  }

  @Override
  public boolean updateSysAdministratorAuthResource(
      AdministratorAuthResource authResource) {
    return update()
        .set(AdministratorAuthResource::getResourceName,
            authResource.getResourceName())
        .set(AdministratorAuthResource::getAuth, authResource.getAuth())
        .set(AdministratorAuthResource::getGroupName,
            authResource.getGroupName())
        .eq(AdministratorAuthResource::getId, authResource.getId())
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

}
