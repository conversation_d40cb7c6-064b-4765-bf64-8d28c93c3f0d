package com.red.circle.other.infra.database.rds.service.game;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxUserOpenedAward;
import java.util.Map;


/**
 * <p>
 * 用户已领取的抽奖奖励 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-09-05 12:09
 */
public interface GameLuckyBoxUserOpenedAwardService extends
    BaseService<GameLuckyBoxUserOpenedAward> {

  void deleteByUserId(Long userId);

  Map<Long, GameLuckyBoxUserOpenedAward> getByUserId(Long cmdOriginUserId);
}
