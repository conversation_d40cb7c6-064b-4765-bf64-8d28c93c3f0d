package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 邀请新用户-助力记录.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_red_packet_help")
public class InviteRedPacketHelp extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * ID.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 红包记录id.
   */
  @TableField("red_packet_id")
  private Long redPacketId;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 邀请人id.
   */
  @TableField("invite_user_id")
  private Long inviteUserId;

  /**
   * 用户类型(新用户,回归用户).
   */
  @TableField("user_type")
  private String userType;

  /**
   * 邀请人设备号.
   */
  @TableField("invite_imei")
  private String inviteImei;

  /**
   * 邀请人IP.
   */
  @TableField("invite_ip")
  private String inviteIp;

  /**
   * 助力人设备号.
   */
  @TableField("imei")
  private String imei;

  /**
   * 助力人IP.
   */
  @TableField("ip")
  private String ip;


}
