package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.common.business.core.SysConfigEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.EnumConfig;
import com.red.circle.other.inner.enums.config.EnumConfigKey;
import com.red.circle.other.inner.model.cmd.sys.SysEnumConfigQryCmd;
import com.red.circle.other.inner.model.dto.sys.SysEnumConfigSortDTO;
import java.util.List;

/**
 * <p>
 * 系统枚举配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
public interface EnumConfigService extends BaseService<EnumConfig> {

  /**
   * 获取允许返回app配置
   *
   * @return ignore
   */
  List<EnumConfig> listReturnAppConfig(String sysOrigin);

  /**
   * 系统枚举参数配置
   *
   * @param key 枚举类型
   * @return 配置信息
   */
  String getValue(EnumConfigKey key, String sysOrigin);

  /**
   * 获取系统配置枚举参数
   *
   * @param key 名称
   * @return 配置信息
   */
  EnumConfig getByEnumConfig(EnumConfigKey key, String sysOrigin);

  /**
   * 获取系统配置枚举参数
   *
   * @param sysConfigEnum 名称
   * @return 配置信息
   */
  EnumConfig getByEnumConfig(SysConfigEnum sysConfigEnum, String sysOrigin);

  /**
   * 获取系统配置枚举参数
   *
   * @param key 名称
   * @return 配置信息
   */
  EnumConfig getByEnumConfig(String key, String sysOrigin);

  /**
   * 分页获取系统配置枚举参数
   *
   * @param query
   * @return 配置信息
   */
  PageResult<EnumConfig> pageSysEnumConfig(SysEnumConfigQryCmd query);

  /**
   * 获取允许返回app配置
   *
   * @return ignore
   */
  List<EnumConfig> sysEnumConfigListByGroupName(String groupName);


  /**
   * 获取枚举配置值-全局
   *
   * @param violationPicture ignore
   * @return ignore
   */
  String getViolationAvatar(EnumConfigKey violationPicture);

  /**
   * 删除常量枚举
   *
   * @param name key
   */
  void deleteBySysOriginAndName(String name, String sysOrigin);

  /**
   * 修改排序.
   */
  void updateSort(List<SysEnumConfigSortDTO> params);

}
