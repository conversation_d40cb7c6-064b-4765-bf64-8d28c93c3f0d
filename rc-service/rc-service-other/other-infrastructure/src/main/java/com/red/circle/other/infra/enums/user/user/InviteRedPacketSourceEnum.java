package com.red.circle.other.infra.enums.user.user;

/**
 * <p>
 * 红包邀请活动-来源.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 17:21
 */
public enum InviteRedPacketSourceEnum {

  /**
   * 用户邀请.
   */
  USER_INVITATION("用户邀请"),

  /**
   * 房间.
   */
  ROOM("房间"),

  /**
   * 首页.
   */
  HOME("首页"),

  /**
   * 主播代理中心.
   */
  TEAM("主播代理中心"),

  /**
   * BD中心.
   */
  BD("BD中心"),

  /**
   * 每日任务.
   */
  TASK("每日任务"),

  /**
   * 礼物.
   */
  GIFT("礼物"),

  /**
   * 游戏.
   */
  GAME("游戏"),

  /**
   * 我的.
   */
  ME("我的"),

  /**
   * 飘窗
   */
  BROADCAST("飘窗");

  private final String desc;

  InviteRedPacketSourceEnum(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }
}
