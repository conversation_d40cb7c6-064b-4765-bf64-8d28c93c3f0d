package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxUserAwardDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxUserAward;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxUserAwardService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户抽奖次数 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxUserAwardServiceImpl extends
    BaseServiceImpl<GameLuckyBoxUserAwardDAO, GameLuckyBoxUserAward> implements
    GameLuckyBoxUserAwardService {

  @Override
  public GameLuckyBoxUserAward getByUserId(Long userId) {
    return query().eq(GameLuckyBoxUserAward::getUserId, userId).last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public void updateByUserId(GameLuckyBoxUserAward userAward) {
    update().set(GameLuckyBoxUserAward::getAwardCount, userAward.getAwardCount())
        .eq(GameLuckyBoxUserAward::getUserId, userAward.getUserId()).execute();
  }
}
