package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.common.business.core.enums.ApprovalStatusEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.UserConfessionRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserConfessionRecord;
import com.red.circle.other.infra.database.rds.service.user.user.UserConfessionRecordService;
import com.red.circle.other.inner.model.cmd.user.UserConfessionRecordQryCmd;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 告白记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-11-15 17:23
 */
@AllArgsConstructor
@Service
public class UserConfessionRecordServiceImpl extends
    BaseServiceImpl<UserConfessionRecordDAO, UserConfessionRecord> implements
    UserConfessionRecordService {

  @Override
  public PageResult<UserConfessionRecord> pageListQuery(UserConfessionRecordQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), UserConfessionRecord::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getUserId()), UserConfessionRecord::getUserId,
            query.getUserId())
        .eq(StringUtils.isNotBlank(query.getStatus()), UserConfessionRecord::getStatus,
            query.getStatus())
        .orderByDesc(UserConfessionRecord::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public List<UserConfessionRecord> listConfessionRecordFloat(String regionCode) {

    return query().eq(UserConfessionRecord::getRegionCode, regionCode)
        .ne(UserConfessionRecord::getStatus, ApprovalStatusEnum.NOT_PASS)
        .orderByDesc(UserConfessionRecord::getCreateTime)
        .last(PageConstant.DEFAULT_LIMIT).list();
  }

  @Override
  public List<UserConfessionRecord> getUserConfessionRecord(String regionCode, Boolean self,
      Long userId, Long lastId) {
    return query()
        .eq(UserConfessionRecord::getRegionCode, regionCode)
        .eq(self, UserConfessionRecord::getUserId, userId)
        .ne(UserConfessionRecord::getStatus, ApprovalStatusEnum.NOT_PASS)
        .lt(Objects.nonNull(lastId), UserConfessionRecord::getId, lastId)
        .orderByDesc(UserConfessionRecord::getId)
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }

}
