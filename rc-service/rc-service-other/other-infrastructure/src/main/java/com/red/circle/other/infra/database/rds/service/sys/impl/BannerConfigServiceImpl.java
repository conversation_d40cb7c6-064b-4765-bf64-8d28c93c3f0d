package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.BannerConfigDAO;
import com.red.circle.other.infra.database.rds.entity.sys.BannerConfig;
import com.red.circle.other.infra.database.rds.service.sys.BannerConfigService;
import com.red.circle.other.inner.model.cmd.sys.SysBannerConfigQryCmd;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * banner配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-26
 */
@Service
public class BannerConfigServiceImpl extends
    BaseServiceImpl<BannerConfigDAO, BannerConfig> implements BannerConfigService {

  @Override
  public List<BannerConfig> listEffective(String sysOrigin) {
    return query()
        .gt(BannerConfig::getExpiredTime, LocalDateTime.now())
        .eq(BannerConfig::getShowcase, Boolean.TRUE)
        .eq(BannerConfig::getSysOrigin, sysOrigin)
        .orderByDesc(BannerConfig::getSort)
        .last(PageConstant.MAX_LIMIT).list();
  }

  @Override
  public PageResult<BannerConfig> pageBanner(SysBannerConfigQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), BannerConfig::getSysOrigin, query.getSysOrigin())
        .eq(Objects.equals(query.getShowcase(), 1), BannerConfig::getShowcase, Boolean.TRUE)
        .gt(Objects.equals(query.getShowcase(), 1), BannerConfig::getExpiredTime, TimestampUtils.now())
        .eq(Objects.equals(query.getShowcase(), 2), BannerConfig::getShowcase, Boolean.FALSE)
        .le(Objects.equals(query.getShowcase(), 3), BannerConfig::getExpiredTime, TimestampUtils.now())
        .like(StringUtils.isNotBlank(query.getRegion()), BannerConfig::getRegions, query.getRegion())
        .orderByDesc(BannerConfig::getSort)
        .page(query.getPageQuery());
  }

  @Override
  public Boolean updateInfo(BannerConfig bannerConfig) {
    return update()
        .set(BannerConfig::getCover, bannerConfig.getCover())
        .set(BannerConfig::getSmallCover, bannerConfig.getSmallCover())
        .set(BannerConfig::getAlertCover, bannerConfig.getAlertCover())
        .set(BannerConfig::getDisplayPosition, bannerConfig.getDisplayPosition())
        .set(BannerConfig::getType, bannerConfig.getType())
        .set(BannerConfig::getSysOrigin, bannerConfig.getSysOrigin())
        .set(BannerConfig::getContent, bannerConfig.getContent())
        .set(BannerConfig::getParams, bannerConfig.getParams())
        .set(BannerConfig::getShowcase, bannerConfig.getShowcase())
        .set(BannerConfig::getExpiredTime, bannerConfig.getExpiredTime())
        .set(BannerConfig::getDepict, bannerConfig.getDepict())
        .set(BannerConfig::getStartTime, bannerConfig.getStartTime())
        .set(BannerConfig::getSort, bannerConfig.getSort())
        .set(BannerConfig::getRegions, bannerConfig.getRegions())
        .eq(BannerConfig::getId, bannerConfig.getId())
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

}
