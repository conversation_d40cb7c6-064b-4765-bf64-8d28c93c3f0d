package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.StartPagePlanDAO;
import com.red.circle.other.infra.database.rds.entity.sys.StartPagePlan;
import com.red.circle.other.infra.database.rds.service.sys.StartPagePlanService;
import com.red.circle.other.inner.model.cmd.sys.SysStartPagePlanQryCmd;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 启动页展示图片 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-08
 */
@Service
public class StartPagePlanServiceImpl extends
    BaseServiceImpl<StartPagePlanDAO, StartPagePlan> implements StartPagePlanService {


  @Override
  public List<StartPagePlan> listBySysOrigin(String sysOrigin) {
    return query()
        .eq(StartPagePlan::getSysOrigin, sysOrigin)
        .gt(StartPagePlan::getExpireTime, TimestampUtils.now())
        .last(PageConstant.DEFAULT_LIMIT)
        .list();
  }


  @Override
  public PageResult<StartPagePlan> pageStartPage(SysStartPagePlanQryCmd query) {

    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), StartPagePlan::getSysOrigin,
            query.getSysOrigin())
        .lt(Objects.equals(query.getShowcase(), 0), StartPagePlan::getExpireTime,
            TimestampUtils.now())
        .gt(Objects.equals(query.getShowcase(), 1), StartPagePlan::getExpireTime,
            TimestampUtils.now())
        .eq(StringUtils.isNotBlank(query.getType()), StartPagePlan::getType,
            query.getType())
        .orderByDesc(StartPagePlan::getId)
        .page(query.getPageQuery());

  }

}
