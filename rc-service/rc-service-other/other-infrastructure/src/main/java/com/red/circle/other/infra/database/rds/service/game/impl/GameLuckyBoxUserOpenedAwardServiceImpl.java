package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxUserOpenedAwardDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxUserOpenedAward;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxUserOpenedAwardService;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户已领取的抽奖奖励 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-09-05 12:09
 */
@Service
public class GameLuckyBoxUserOpenedAwardServiceImpl extends
    BaseServiceImpl<GameLuckyBoxUserOpenedAwardDAO, GameLuckyBoxUserOpenedAward> implements
    GameLuckyBoxUserOpenedAwardService {

  @Override
  public void deleteByUserId(Long userId) {
    delete().eq(GameLuckyBoxUserOpenedAward::getUserId, userId).execute();
  }

  @Override
  public Map<Long, GameLuckyBoxUserOpenedAward> getByUserId(Long userId) {
    return Optional.ofNullable(
            query().eq(GameLuckyBoxUserOpenedAward::getUserId, userId).list()
        ).map(openedAwards -> openedAwards.stream()
            .collect(
                Collectors.toMap(GameLuckyBoxUserOpenedAward::getAwardDetailsId, Function.identity(),
                    (v0, v1) -> v0)))
        .orElse(Maps.newHashMap());
  }
}
