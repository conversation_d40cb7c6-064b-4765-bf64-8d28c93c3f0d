package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * CP祝福礼物记录.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("user_cp_bless_record")
public class CpBlessRecord extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键id.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 接收用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 发送用户id.
   */
  @TableField("send_user_id")
  private Long sendUserId;

  /**
   * cp值id.
   */
  @TableField("cp_val_id")
  private Long cpValId;

  /**
   * 祝福礼物封面.
   */
  @TableField("gift_cover")
  private String giftCover;

  /**
   * 祝福礼物数量.
   */
  @TableField("gift_count")
  private Long giftCount;

  /**
   * 0.未读,1.已读.
   */
  @TableField("is_reading")
  private Boolean reading;


}
