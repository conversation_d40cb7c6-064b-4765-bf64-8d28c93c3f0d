package com.red.circle.other.infra.database.rds.service.gift;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.gift.GiftPackConfig;
import com.red.circle.other.inner.model.cmd.sys.GiftPackConfigQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 礼包配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-15
 */
public interface GiftPackConfigService extends BaseService<GiftPackConfig> {


  /**
   * 根据礼包管理编号分组.
   *
   * @param giftPackIds ignore
   * @return ignore
   */
  Map<Long, List<GiftPackConfig>> mapGroupByGiftPackIds(Set<Long> giftPackIds);

  /**
   * 获取礼包内容映射.
   *
   * @param giftPackIds ignore
   * @return ignore
   */
  Map<Long, List<GiftPackConfig>> mapGiftPackIds(Set<Long> giftPackIds);

  /**
   * 获取礼包内容.
   *
   * @param giftPackId 礼包产品id
   * @return ignore
   */
  List<GiftPackConfig> listByGiftPackId(Long giftPackId);

  /**
   * 获取礼包内容.
   *
   * @param giftPackId 礼包产品id
   * @return ignore
   */
  GiftPackConfig getGoldByPackId(Long giftPackId);

  List<GiftPackConfig> getGiftPackConfigInfo(GiftPackConfigQryCmd query);
}
