package com.red.circle.other.infra.database.rds.service.family.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.family.FamilyRewardReceiveRecordDAO;
import com.red.circle.other.infra.database.rds.entity.family.FamilyRewardReceiveRecord;
import com.red.circle.other.infra.database.rds.service.family.FamilyRewardReceiveRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
@Service
public class FamilyRewardReceiveRecordServiceImpl extends
    BaseServiceImpl<FamilyRewardReceiveRecordDAO, FamilyRewardReceiveRecord> implements
    FamilyRewardReceiveRecordService {

}
