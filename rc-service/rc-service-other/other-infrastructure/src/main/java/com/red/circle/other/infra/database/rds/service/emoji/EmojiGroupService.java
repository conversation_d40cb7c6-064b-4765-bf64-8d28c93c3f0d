package com.red.circle.other.infra.database.rds.service.emoji;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.emoji.EmojiGroup;
import com.red.circle.other.inner.model.cmd.sys.SysEmojiGroupQryCmd;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 系统表情包分组 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-25
 */
public interface EmojiGroupService extends BaseService<EmojiGroup> {

  /**
   * 获取指定一组表情信息.
   *
   * @param ids id集合
   * @return map
   */
  Map<Long, EmojiGroup> mapByIds(Set<Long> ids);

  /**
   * 根据编码获取分组id.
   *
   * @param sysOrigin 系统
   * @param groupCode 编码
   * @return id
   */
  Long getSysOriginGroupIdByCode(String sysOrigin, String groupCode);

  /**
   * 根据编码获取分组.
   *
   * @param sysOrigin 系统
   * @param groupCode 编码
   * @return obj
   */
  EmojiGroup getSysOriginByCode(String sysOrigin, String groupCode);

  /**
   * 获取所有上架的表情包.
   *
   * @param sysOrigin 系统
   * @return list
   */
  List<EmojiGroup> listAll(String sysOrigin);

  /**
   * 获取表情分组.
   *
   * @param query 查询条件
   * @return page
   */
  PageResult<EmojiGroup> pageEmojiGroup(SysEmojiGroupQryCmd query);

  /**
   * 获取分组列表.
   *
   * @param sysOrigin 归属系统
   * @return list
   */
  List<EmojiGroup> groupListBySysOrigin(String sysOrigin);

  /**
   * 切换上下架状态.
   *
   * @param id     id
   * @param status 状态
   */
  void switchGroupShelfStatus(Long id, Boolean status);
}
