package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.InviteRedPacketRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteRedPacketRecord;
import com.red.circle.other.infra.database.rds.service.user.user.InviteRedPacketRecordService;
import com.red.circle.other.infra.enums.user.user.InviteRedPacketStatusEnum;
import com.red.circle.other.inner.model.cmd.user.invite.InviteRedPacketPageQryCmd;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 邀请新用户-红包记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@AllArgsConstructor
@Service
public class InviteRedPacketRecordServiceImpl extends
    BaseServiceImpl<InviteRedPacketRecordDAO, InviteRedPacketRecord> implements
    InviteRedPacketRecordService {

  @Override
  public InviteRedPacketRecord getUserRedPacketByUndone(Long userId) {

    return query()
        .eq(InviteRedPacketRecord::getUserId, userId)
        .eq(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.UNDONE.name())
        .gt(InviteRedPacketRecord::getExpiresTime, TimestampUtils.now())
        .last(PageConstant.LIMIT_ONE)
        .orderByDesc(InviteRedPacketRecord::getCreateTime)
        .getOne();
  }

  @Override
  public Long getCompleteCount(Long userId) {

    return query()
        .eq(InviteRedPacketRecord::getUserId, userId)
        .in(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.COMPLETE.name(),
            InviteRedPacketStatusEnum.BEFORE_COMPLETE.name())
        .last(PageConstant.formatLimit(200))
        .count();
  }

  @Override
  public Long getCompleteCount(Long userId, Integer dateNumber) {

    return query()
        .eq(InviteRedPacketRecord::getUserId, userId)
        .eq(InviteRedPacketRecord::getDateNumber, dateNumber)
        .in(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.COMPLETE.name(),
            InviteRedPacketStatusEnum.BEFORE_COMPLETE.name())
        .last(PageConstant.formatLimit(200))
        .count();
  }

  @Override
  public Long getCompleteCountByIp(String ip, String sysOrigin) {

    return query()
            .eq(InviteRedPacketRecord::getIp, ip)
            .eq(InviteRedPacketRecord::getSysOrigin, sysOrigin)
            .in(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.COMPLETE.name(),
                    InviteRedPacketStatusEnum.BEFORE_COMPLETE.name())
            .last(PageConstant.formatLimit(200))
            .count();
  }

  @Override
  public Long getCompleteCountByImei(String imei, String sysOrigin) {

    return query()
            .eq(InviteRedPacketRecord::getImei, imei)
            .eq(InviteRedPacketRecord::getSysOrigin, sysOrigin)
            .in(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.COMPLETE.name(),
                    InviteRedPacketStatusEnum.BEFORE_COMPLETE.name())
            .last(PageConstant.formatLimit(200))
            .count();
  }

  @Override
  public void incrInviteMemberCount(Long id) {

    update().setSql("invite_member_count=invite_member_count+1")
        .eq(InviteRedPacketRecord::getId, id)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public void incrCurrentAmountById(Long id, BigDecimal amount) {

    update().setSql("current_amount=current_amount+" + amount)
        .eq(InviteRedPacketRecord::getId, id)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public void updateStatusById(Long id, String status) {

    update()
        .set(InviteRedPacketRecord::getStatus, status)
        .eq(InviteRedPacketRecord::getId, id)
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public PageResult<InviteRedPacketRecord> pageRecord(InviteRedPacketPageQryCmd cmd) {

    return query()
        .eq(Objects.nonNull(cmd.getUserId()), InviteRedPacketRecord::getUserId,
            cmd.getUserId())
        .eq(Objects.nonNull(cmd.getInviteUserId()), InviteRedPacketRecord::getInviteUserId,
            cmd.getInviteUserId())
        .eq(StringUtils.isNotBlank(cmd.getSysOrigin()),
            InviteRedPacketRecord::getSysOrigin, cmd.getSysOrigin())
        .eq(Objects.nonNull(cmd.getId()), InviteRedPacketRecord::getId,
            cmd.getId())
        .eq(StringUtils.isNotBlank(cmd.getStatus()), InviteRedPacketRecord::getStatus,
            cmd.getStatus())
        .eq(StringUtils.isNotBlank(cmd.getIp()), InviteRedPacketRecord::getIp,
            cmd.getIp())
        .eq(StringUtils.isNotBlank(cmd.getImei()), InviteRedPacketRecord::getImei,
            cmd.getImei())
        .eq(StringUtils.isNotBlank(cmd.getRedPacketSource()),
            InviteRedPacketRecord::getRedPacketSource,
            cmd.getRedPacketSource())
        .ge(Objects.nonNull(cmd.getStartTime()), InviteRedPacketRecord::getCreateTime,
            cmd.getStartTime())
        .le(Objects.nonNull(cmd.getEndTime()), InviteRedPacketRecord::getCreateTime,
            cmd.getEndTime())
        .orderByDesc(InviteRedPacketRecord::getCreateTime)
        .page(cmd.getPageQuery());
  }

  @Override
  public InviteRedPacketRecord getViolationOrNotStatus(Long id, boolean status,String sysOrigin) {
    return query()
            .eq(InviteRedPacketRecord::getUserId, id)
            .eq(InviteRedPacketRecord::getSysOrigin,sysOrigin)
            .eq(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.UNDONE.name())
            .eq(InviteRedPacketRecord::getViolationOrNot,status)
            .gt(InviteRedPacketRecord::getExpiresTime, TimestampUtils.now())
            .last(PageConstant.LIMIT_ONE)
            .orderByDesc(InviteRedPacketRecord::getCreateTime)
            .getOne();
  }

  @Override
  public void updateViolationOrNotStatus(Long id, boolean status) {
    update()
            .set(InviteRedPacketRecord::getViolationOrNot, status)
            .set(InviteRedPacketRecord::getUpdateTime, TimestampUtils.now())
            .eq(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.UNDONE.name())
            .and(where -> where.eq(InviteRedPacketRecord::getUserId,id)
                    .or().eq(InviteRedPacketRecord::getInviteUserId, id))
            .execute();
  }

  @Override
  public List<InviteRedPacketRecord> getUserIdList(Long userId, Integer topSize,String sysOrigin){
    return query()
            .eq(Objects.nonNull(userId), InviteRedPacketRecord::getUserId, userId)
            .eq(InviteRedPacketRecord::getSysOrigin,sysOrigin)
            .in(InviteRedPacketRecord::getStatus, InviteRedPacketStatusEnum.COMPLETE.name(),
                    InviteRedPacketStatusEnum.BEFORE_COMPLETE.name())
            .orderByDesc(InviteRedPacketRecord::getCreateTime)
            .last(PageConstant.formatLimit(topSize))
            .list();
  }
}
