package com.red.circle.other.infra.utils;

import lombok.Getter;

/**
 * 格式化类型.
 *
 * <AUTHOR> on 2024/06/25 14:35
 */
@Getter
public enum DateFormatEnum {

  yy("yy"),
  MM("MM"),
  dd("dd"),
  yyyy_MM_dd("yyyy-MM-dd"),
  yyyyMMdd("yyyyMMdd"),
  yyMMdd("yyMMdd"),
  yyyy_point_MM_point_dd("yyyy.MM.dd"),
  MM_point_dd("MM.dd"),
  yyyy_MM_dd_HH_mm_ss("yyyy-MM-dd HH:mm:ss"),
  yyyy_MM_dd_HH_mm_ss_S("yyyy-MM-dd HH:mm:ss.S"),
  yyyy_MM_dd_HH_mm("yyyy-MM-dd HH:mm"),
  HH_mm_ss("HH:mm:ss"),
  hh_MM("HH:mm"),
  yyyy_MM("yyyyMM"),
  MM_SLASH_YYYY("MM/yyyy"),
  @Deprecated
  yyyy_MM_tmp("MM/yyyy"),
  DD_MM("dd/MM");

  private final String pattern;

  DateFormatEnum(String pattern) {
    this.pattern = pattern;
  }

}
