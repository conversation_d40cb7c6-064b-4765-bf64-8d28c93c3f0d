package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameRoomPkIntegralRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkIntegralRecord;
import com.red.circle.other.infra.database.rds.service.game.GameRoomPkIntegralRecordService;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间PK用户贡献记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
@Service
@RequiredArgsConstructor
public class GameRoomPkIntegralRecordServiceImpl extends
    BaseServiceImpl<GameRoomPkIntegralRecordDAO, GameRoomPkIntegralRecord> implements
    GameRoomPkIntegralRecordService {

  private final GameRoomPkIntegralRecordDAO gameRoomPkIntegralRecordDAO;

  @Override
  public Long getTotalIntegralByPkId(Long pkId) {

    return Optional.ofNullable(gameRoomPkIntegralRecordDAO.getTotalIntegralByPkId(pkId)).orElse(0L);
  }

  @Override
  public List<GameRoomPkIntegralRecord> getTotalIntegralAndUserIdByPkId(Long pkId) {

    return gameRoomPkIntegralRecordDAO.getTotalIntegralAndUserIdByPkId(pkId);
  }

  @Override
  public void saveIntegralToFansByPkId(Long pkId) {

    gameRoomPkIntegralRecordDAO.saveIntegralToFansByPkId(pkId);
  }
}
