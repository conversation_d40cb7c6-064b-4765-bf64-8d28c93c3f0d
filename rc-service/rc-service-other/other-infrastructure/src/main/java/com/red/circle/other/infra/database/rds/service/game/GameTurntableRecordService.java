package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameTurntableRecord;

/**
 * <p>
 * 转盘游戏记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-20
 */
public interface GameTurntableRecordService extends BaseService<GameTurntableRecord> {

  /**
   * 获取游戏记录.
   *
   * @param gameId 游戏id
   * @return ignore
   */
  GameTurntableRecord getByGameId(Long gameId);
}
