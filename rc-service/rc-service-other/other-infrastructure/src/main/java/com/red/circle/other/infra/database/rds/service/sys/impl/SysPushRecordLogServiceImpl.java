package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysPushRecordLogDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysPushRecordLog;
import com.red.circle.other.infra.database.rds.service.sys.SysPushRecordLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 推送记录日志 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-09
 */
@Service
public class SysPushRecordLogServiceImpl extends
    BaseServiceImpl<SysPushRecordLogDAO, SysPushRecordLog> implements SysPushRecordLogService {

}
