package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameUserLotteryDetailsRecord;
import com.red.circle.other.inner.model.dto.game.GameUserLotteryDetailsRecordDTO;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户游戏抽奖明细记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-15
 */
public interface GameUserLotteryDetailsRecordService extends
    BaseService<GameUserLotteryDetailsRecord> {

  Map<Long, List<GameUserLotteryDetailsRecordDTO>> mapByLotteryIds(Set<Long> lotteryIds);


}
