package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户注销web页面申请.
 * </p>
 *
 * <AUTHOR> on 2023-09-18 14:46
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_logout_apply")
public class LogoutApply extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户账号.
   */
  @TableField("user_account")
  private String userAccount;

  /**
   * 用户名称.
   */
  @TableField("user_name")
  private String userName;

  /**
   * 上传凭证.
   */
  @TableField("upload_cover")
  private String uploadCover;

  /**
   * 类型:注销账号/删除数据.
   */
  @TableField("logout_type")
  private String logoutType;


}
