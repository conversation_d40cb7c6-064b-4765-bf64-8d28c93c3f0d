package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.CpBlessRecord;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * CP祝福礼物记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
public interface CpBlessRecordService extends BaseService<CpBlessRecord> {

  List<CpBlessRecord> listByUserId(Long userId);

  void updateByUserId(Long userId);

  /**
   * 给这对cp最近送过礼物的6个人（不重复，不包含CP本人）的祝福礼物记录
   */
  List<CpBlessRecord> getBlessRecord(Long userId, Set<Long> notSendUserIds);

  /**
   * cp小屋祝福列表分页列表
   */
  List<CpBlessRecord> pageList(Long userId, Long lastId);

  /**
   * 删除祝福礼物记录
   */
  void deleteByCpValId(Long cpValId);
}
