package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsRedPacketSkinDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsRedPacketSkin;
import com.red.circle.other.infra.database.rds.service.props.PropsRedPacketSkinService;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 红包皮肤图片表 - 道具.
 * </p>
 *
 * <AUTHOR> on 2023-11-08 09:40
 */
@Service
public class PropsRedPacketSkinServiceImpl extends
    BaseServiceImpl<PropsRedPacketSkinDAO, PropsRedPacketSkin> implements
    PropsRedPacketSkinService {

  @Override
  public List<PropsRedPacketSkin> mapByIds(Set<Long> ids) {
    return query().in(PropsRedPacketSkin::getId, ids).last(PageConstant.formatLimit(ids.size()))
        .list();
  }

}
