package com.red.circle.other.infra.database.rds.service.live;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.live.RoomSubscription;
import java.util.List;

/**
 * <p>
 * 房间订阅 服务类.
 * </p>
 *
 * <AUTHOR> on  2020-12-01
 */
public interface RoomSubscriptionService extends BaseService<RoomSubscription> {

  /**
   * 验证是否已关注.
   *
   * @param userId 用户id
   * @param roomId 房间id
   */
  boolean validFollow(Long userId, Long roomId);


  /**
   * 删除订阅关系.
   *
   * @param userId 用户id
   * @param roomId 房间id
   */
  void remove(Long userId, Long roomId);

  /**
   * 新增订阅关系.
   *
   * @param userId 用户id
   * @param roomId 房间id
   */
  void add(Long userId, Long roomId);

  /**
   * 分页获取我的订阅.
   *
   * @param userId 用户id
   * @param lastId 最后的ID
   * @return ignore
   */
  List<RoomSubscription> listRoomIdByUserIdFlow(Long userId, Long lastId);

}
