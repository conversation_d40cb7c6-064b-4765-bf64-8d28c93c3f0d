package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.LocalDateUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.CpRelationshipDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.CpRelationship;
import com.red.circle.other.infra.database.rds.service.user.user.CpRelationshipService;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户cp关系 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-13
 */
@Service
@RequiredArgsConstructor
public class CpRelationshipServiceImpl extends
    BaseServiceImpl<CpRelationshipDAO, CpRelationship> implements CpRelationshipService {

  private final CpRelationshipDAO cpRelationshipDAO;

  @Override
  public List<CpRelationship> listLatestCp(String sysOrigin, Integer size) {
    return cpRelationshipDAO.listLatestCp(sysOrigin, size);
  }

  @Override
  public List<CpRelationship> listLatestCp(String sysOrigin, Set<Long> excludeUserId,
      Integer size) {
    return cpRelationshipDAO.listLatestCpExcludeUserId(sysOrigin, excludeUserId, size);
  }

  @Override
  public boolean existsCp(Long userId) {
    return Optional.ofNullable(
        query().select(CpRelationship::getId).eq(CpRelationship::getUserId, userId)
            .last(PageConstant.LIMIT_ONE).getOne()
    ).map(cpRelationship -> Objects.nonNull(cpRelationship.getId())).orElse(Boolean.FALSE);
  }

  @Override
  public boolean existsCp(List<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Boolean.FALSE;
    }

    return CollectionUtils
        .isNotEmpty(query().select(CpRelationship::getId).in(CpRelationship::getUserId, userIds)
            .last(PageConstant.formatLimit(userIds.size())).list());
  }

  @Override
  public Long getCpUserId(Long userId) {
    return Optional.ofNullable(
        query().select(CpRelationship::getCpUserId).eq(CpRelationship::getUserId, userId)
            .last(PageConstant.LIMIT_ONE).getOne()
    ).map(CpRelationship::getCpUserId).orElse(null);
  }

  @Override
  public CpRelationship getByUserId(Long userId) {
    return Optional.ofNullable(query()
        .eq(CpRelationship::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne()
    ).orElse(null);
  }

  @Override
  public CpRelationship getByCpUserId(Long cpUserId) {
    return Optional.ofNullable(query()
        .eq(CpRelationship::getCpUserId, cpUserId)
        .last(PageConstant.LIMIT_ONE)
        .getOne()
    ).orElse(null);
  }

  @Override
  public Long getCpValByUserId(Long userId) {
    return Optional.ofNullable(query()
        .select(CpRelationship::getCpValId)
        .eq(CpRelationship::getUserId, userId)
        .last(PageConstant.LIMIT_ONE)
        .getOne()
    ).map(CpRelationship::getCpValId).orElse(null);
  }

  @Override
  public void deleteCpRelationship(Long userId) {
    delete().eq(CpRelationship::getCpUserId, userId).last(PageConstant.LIMIT_ONE).execute();
    delete().eq(CpRelationship::getUserId, userId).last(PageConstant.LIMIT_ONE).execute();
  }

  @Override
  public void addCp(Long sendApplyUserId, Long acceptApplyUserId, Long bindCpValId,
      String sysOrigin) {
    CpRelationship sendUser = createCpRelationshipBean(sendApplyUserId, acceptApplyUserId,
        bindCpValId, 0, sysOrigin);
    CpRelationship cpUser = createCpRelationshipBean(acceptApplyUserId, sendApplyUserId,
        bindCpValId, 1, sysOrigin);
    saveBatch(Arrays.asList(sendUser, cpUser));
  }

  @Override
  public void updateAccountByUserId(Long userId, BigDecimal account) {
    update().set(CpRelationship::getAmount, account)
        .eq(CpRelationship::getUserId, userId)
        .execute();

  }

  @Override
  public CpRelationship getByUserIdOrCpUserId(Long userId) {
    return Optional.ofNullable(query()
        .and(where -> where.eq(CpRelationship::getUserId, userId)
            .or().eq(CpRelationship::getCpUserId, userId))
        .last(PageConstant.LIMIT_ONE)
        .getOne()
    ).orElse(null);
  }

  private CpRelationship createCpRelationshipBean(Long userId, Long cpUserId, Long bindCpValId,
      Integer initiate, String sysOrigin) {
    CpRelationship cpUser = new CpRelationship()
        .setUserId(userId)
        .setCpUserId(cpUserId)
        .setCpValId(bindCpValId)
        .setInitiate(initiate)
        .setSysOrigin(sysOrigin)
        .setAmount(BigDecimal.ZERO);
    cpUser.setCreateTime(LocalDateUtils.nowTimestamp());
    cpUser.setUpdateTime(LocalDateUtils.nowTimestamp());
    return cpUser;
  }


}
