package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.AccountAuth;

/**
 * <p>
 * 用户账号认证 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-09-13 11:43
 */
public interface AccountAuthService extends BaseService<AccountAuth> {

  void saveAccountAuth(AccountAuth accountAuth);

  AccountAuth getByPassword(String pwd, Long cmdOriginUserId);

  boolean deleteById(Long id);

  AccountAuth getByUserId(Long cmdOriginUserId);

  void deleteByUserId(Long userId);
}
