package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserInterview;
import java.util.List;

/**
 * <p>
 * 用户访问列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-09
 */
public interface UserInterviewService extends BaseService<UserInterview> {

  UserInterview checkExist(Long userId, Long interviewId);

  List<UserInterview> listByUserId(Long userId, Long lastId);

  List<UserInterview> pageByUserId(Long userId, Integer pageNumber);

}
