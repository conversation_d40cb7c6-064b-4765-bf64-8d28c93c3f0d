package com.red.circle.other.infra.database.rds.service.gift.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.gift.GiftBackpackLimitDAO;
import com.red.circle.other.infra.database.rds.entity.gift.GiftBackpackLimit;
import com.red.circle.other.infra.database.rds.service.gift.GiftBackpackLimitService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户礼物背包-限制 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-14
 */
@Service
public class GiftBackpackLimitServiceImpl extends
    BaseServiceImpl<GiftBackpackLimitDAO, GiftBackpackLimit> implements GiftBackpackLimitService {

  @Override
  public List<Long> getBackIdsByGiftTab(Set<Long> userIds, String giftTab) {
    return Optional.ofNullable(
        query()
            .select(GiftBackpackLimit::getBackId)
            .in(GiftBackpackLimit::getUserId, userIds)
            .eq(GiftBackpackLimit::getGiftTab, giftTab)
            .list()
    ).map(
        giftBackpackLimits -> giftBackpackLimits.stream().map(GiftBackpackLimit::getBackId).collect(
            Collectors.toList())).orElse(null);
  }

  @Override
  public void deleteByGiftTab(Set<Long> userIds, String giftTab) {
    delete()
        .in(GiftBackpackLimit::getUserId, userIds)
        .eq(GiftBackpackLimit::getGiftTab, giftTab)
        .execute();
  }

  @Override
  public Map<Long, GiftBackpackLimit> mapByBackIds(Long userId, Set<Long> backIds) {
    return Optional.ofNullable(
            query()
                .eq(GiftBackpackLimit::getUserId, userId)
                .in(GiftBackpackLimit::getBackId, backIds)
                .list())
        .map(giftBackpackLimits -> giftBackpackLimits.stream().collect(
            Collectors.toMap(GiftBackpackLimit::getBackId, v -> v)))
        .orElseGet(CollectionUtils::newHashMap);
  }

  @Override
  public GiftBackpackLimit getByUserIdByBackId(Long userId, Long backId) {

    return query()
        .eq(GiftBackpackLimit::getUserId, userId)
        .eq(GiftBackpackLimit::getBackId, backId)
        .getOne();
  }
}
