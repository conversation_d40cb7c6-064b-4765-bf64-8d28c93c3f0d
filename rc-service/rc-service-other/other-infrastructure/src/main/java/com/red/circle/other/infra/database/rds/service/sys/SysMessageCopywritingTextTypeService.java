package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.dto.MessageCopywritingDTO;
import com.red.circle.other.infra.database.rds.entity.sys.SysMessageCopywritingTextType;
import com.red.circle.other.inner.model.cmd.sys.SysMessageCopywritingTypeQryCmd;

/**
 * <p>
 * 推送文案类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
public interface SysMessageCopywritingTextTypeService extends
    BaseService<SysMessageCopywritingTextType> {


  /**
   * 获取模板信息.
   *
   * @param pushTemplate 模板
   * @param lang         语言
   * @return dto
   */
  MessageCopywritingDTO getPushCopywriting(String pushTemplate, String lang);

  /**
   * 随即获取打招呼文案.
   *
   * @param lang 语言
   * @return ignore
   */
  MessageCopywritingDTO getGreetRandomText(String lang);

  PageResult<SysMessageCopywritingTextType> pageCopywritingTextType(
      SysMessageCopywritingTypeQryCmd query);

  void deleteById(Long id);
}
