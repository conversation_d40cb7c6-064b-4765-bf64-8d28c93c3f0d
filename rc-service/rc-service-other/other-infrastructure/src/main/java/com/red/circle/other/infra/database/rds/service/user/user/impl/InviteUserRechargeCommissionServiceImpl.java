package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.InviteUserRechargeCommissionDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteUserRechargeCommission;
import com.red.circle.other.infra.database.rds.service.user.user.InviteUserRechargeCommissionService;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 邀请用户充值佣金 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Service
public class InviteUserRechargeCommissionServiceImpl extends
    BaseServiceImpl<InviteUserRechargeCommissionDAO, InviteUserRechargeCommission> implements
    InviteUserRechargeCommissionService {

  @Override
  public void add(String sysOrigin, Long userId, Long inviteUserId, BigDecimal commission) {

    InviteUserRechargeCommission inviteUser = new InviteUserRechargeCommission()
        .setId(IdWorkerUtils.getId())
        .setSysOrigin(sysOrigin)
        .setUserId(userId)
        .setInviteUserId(inviteUserId)
        .setCommission(commission);
    inviteUser.setCreateUser(inviteUserId);
    inviteUser.setCreateTime(TimestampUtils.now());
    save(inviteUser);
  }

  @Override
  public List<InviteUserRechargeCommission> pageByCommissionDesc(Integer pageNumber,
      Long inviteUserId) {

    if (Objects.isNull(pageNumber) || pageNumber < 1) {
      pageNumber = 1;
    }

    return query()
        .eq(InviteUserRechargeCommission::getInviteUserId, inviteUserId)
        .orderByDesc(InviteUserRechargeCommission::getCreateTime)
        .last(getPageSql(pageNumber))
        .list();
  }

  private String getPageSql(Integer pageNumber) {

    return String.format("LIMIT %s, %s", (pageNumber - 1) * PageConstant.DEFAULT_LIMIT_SIZE,
        PageConstant.DEFAULT_LIMIT_SIZE);
  }
}
