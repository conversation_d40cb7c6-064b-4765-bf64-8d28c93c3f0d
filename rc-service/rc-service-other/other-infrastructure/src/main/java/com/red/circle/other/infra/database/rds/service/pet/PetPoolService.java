package com.red.circle.other.infra.database.rds.service.pet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.pet.PetPool;

/**
 * <p>
 * 宠物池 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface PetPoolService extends BaseService<PetPool> {

  /**
   * 宠物分页列表.
   *
   * @param page      分页条件
   * @param sysOrigin 平台
   * @return page
   */
  IPage<PetPool> pagePet(IPage<PetPool> page, SysOriginPlatformEnum sysOrigin);

  /**
   * 获取第一只宠物.
   *
   * @param sysOrigin 平台
   * @return id
   */
  Long getFirstPetId(SysOriginPlatformEnum sysOrigin);
}
