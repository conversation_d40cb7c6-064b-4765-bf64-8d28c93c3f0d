package com.red.circle.other.infra.database.rds.service.game;

/**
 * 缓存砸金蛋.
 *
 * <AUTHOR> on 2021/11/16
 */
public interface GameEggCacheService {

  /**
   * 获取今天中奖前20用户.
   *
   * @param sysOrigin 平台
   * @return ignore
   */
  String getThisDaysTop20(String sysOrigin);

  /**
   * 缓存今天中奖前20用户.
   *
   * @param sysOrigin 平台
   * @param data      数据
   */
  void cacheThisDaysTop20(String sysOrigin, String data);

  /**
   * 获取平台本月奖金池.
   */
  Long getThisMonthPrizePool(String sysOrigin);

  /**
   * 累计奖金池-本月.
   */
  void incrThisMonthPrizePool(String sysOrigin, Long amount);

  /**
   * 累减奖金池-本月.
   */
  void decrThisMonthPrizePool(String sysOrigin, Long amount);

  /**
   * 抽取概率-获取.
   */
  String getExtractRatio(String sysOrigin, String defaultRatio);

  /**
   * 抽取概率-设置.
   */
  void setExtractRatio(String sysOrigin, String ratio);

  /**
   * 获取缓存消耗品库存.
   */
  Long getConsumeStockQuantity(Long id);

  /**
   * 设置消耗库存.
   */
  void setConsumeStockQuantity(Long id, Long quantity);

  /**
   * 扣除库存.
   */
  void decrConsumeStockQuantity(Long id, Long quantity);

  /**
   * 移除库存.
   */
  void removeConsumeStockQuantity(Long id);

}
