package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.RunningWaterGiveAwayUserPropsAdmin;

/**
 * <p>
 * 管理员赠送道具 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
public interface RunningWaterGiveAwayUserPropsAdminService extends
    BaseService<RunningWaterGiveAwayUserPropsAdmin> {

  /**
   * 用户是否已经发送过奖励.
   *
   * @param acceptUserId 接收用户
   * @return true 存在 false 不存在
   */
  boolean existsByAcceptUserId(String acceptUserId);
}
