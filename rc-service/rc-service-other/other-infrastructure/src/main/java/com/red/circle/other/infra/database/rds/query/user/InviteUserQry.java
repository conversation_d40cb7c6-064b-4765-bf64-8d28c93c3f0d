package com.red.circle.other.infra.database.rds.query.user;

import java.io.Serial;
import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

/**
 * <AUTHOR> on 2020/05/21 20:10
 */
@Data
public class InviteUserQry implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 归属系统.
   */
  private String sysOrigin;

  /**
   * 邀请用户id.
   */
  private Long userId;

  /**
   * 被邀请用户id.
   */
  private Long inviteUserId;

  /**
   * 国家code.
   */
  private String countryCode;

  /**
   * 开始时间.
   */
  private Timestamp startTime;

  /**
   * 结束时间.
   */
  private Timestamp endTime;

}
