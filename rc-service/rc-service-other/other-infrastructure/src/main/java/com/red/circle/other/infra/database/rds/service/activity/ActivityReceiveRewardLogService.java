package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityReceiveRewardLog;
import com.red.circle.other.inner.enums.activity.PropsActivityTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 周星奖励日志 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-23
 */
public interface ActivityReceiveRewardLogService extends BaseService<ActivityReceiveRewardLog> {

  /**
   * 本周是否存在领取记录.
   *
   * @param userId       用户id
   * @param activityType 活动类型
   * @return true 存在，false 不存在
   */
  @Deprecated
  boolean existsThisWeek(Long userId, PropsActivityTypeEnum activityType);

  /**
   * 本周是否存在领取记录.
   *
   * @param userId       用户id
   * @param receiveId    领取记录
   * @param activityType 活动类型
   * @return true 存在，false 不存在
   */
  boolean existsThisWeek(Long userId, Long receiveId, PropsActivityTypeEnum activityType);

  /**
   * 本月是否存在领取记录.
   *
   * @param userId       用户id
   * @param receiveId    领奖类型id
   * @param activityType 活动类型
   * @return true 存在，false 不存在
   */
  boolean existsThisMonth(Long userId, Long receiveId, PropsActivityTypeEnum activityType);

  /**
   * 今日是否存在领取记录.
   *
   * @param userId       用户id
   * @param receiveId    领奖类型id
   * @param activityType 活动类型
   * @return true 存在，false 不存在
   */
  boolean existsThisNow(Long userId, Long receiveId, PropsActivityTypeEnum activityType);

  /**
   * 是否存在领取记录.
   *
   * @param userId       用户id
   * @param receiveId    领奖类型id
   * @param activityType 活动类型
   * @return true 存在，false 不存在
   */
  boolean exists(Long userId, Long receiveId, PropsActivityTypeEnum activityType);

  /**
   * 获得已领取记录的领取id.
   *
   * @param userId       用户id
   * @param activityType 活动类型
   * @return list
   */
  List<Long> getReceivedIds(Long userId, PropsActivityTypeEnum activityType);

  /**
   * 获取一组指定的映射记录.
   *
   * @param userId       用户id
   * @param activityType 活动类型
   * @param ids          ids集合
   * @return map
   */
  Map<Long, ActivityReceiveRewardLog> mapByReceiveId(Long userId,
      PropsActivityTypeEnum activityType, Set<Long> ids);

  /**
   * 获取本月，一组指定的映射记录.
   *
   * @param userId       用户id
   * @param activityType 活动类型
   * @param ids          ids集合
   * @return map
   */
  Map<Long, ActivityReceiveRewardLog> mapThisMonthByReceiveId(Long userId,
      PropsActivityTypeEnum activityType, Set<Long> ids);

}
