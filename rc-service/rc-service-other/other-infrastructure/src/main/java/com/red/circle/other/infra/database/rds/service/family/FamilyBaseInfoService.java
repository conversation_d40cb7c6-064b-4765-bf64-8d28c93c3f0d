package com.red.circle.other.infra.database.rds.service.family;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.family.FamilyBaseInfo;
import com.red.circle.other.inner.model.cmd.famliy.FamilyBaseInfoQryCmd;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工会基础信息表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-20
 */
public interface FamilyBaseInfoService extends BaseService<FamilyBaseInfo> {

  Long maxAccount();

  FamilyBaseInfo getBaseInfoById(Long id);

  void update(FamilyBaseInfo baseInfo);

  Map<Long, FamilyBaseInfo> mapBaseInfo(Collection<Long> ids);
  Map<Long, FamilyBaseInfo> mapByUserIds(Collection<Long> ids);

  List<FamilyBaseInfo> listBySysOrigin(String sysOrigin);

  PageResult<FamilyBaseInfo> pageData(FamilyBaseInfoQryCmd queryWhere);

  void delFamily(Long familyId);

  List<FamilyBaseInfo> listByIds(List<Long> familyIds);
}
