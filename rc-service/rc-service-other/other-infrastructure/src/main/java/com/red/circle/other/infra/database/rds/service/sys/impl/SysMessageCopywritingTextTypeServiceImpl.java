package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysMessageCopywritingTextTypeDAO;
import com.red.circle.other.infra.database.rds.dto.MessageCopywritingDTO;
import com.red.circle.other.infra.database.rds.entity.sys.SysMessageCopywritingTextType;
import com.red.circle.other.infra.database.rds.service.sys.SysMessageCopywritingTextTypeService;
import com.red.circle.other.inner.model.cmd.sys.SysMessageCopywritingTypeQryCmd;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 推送文案类型表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@RequiredArgsConstructor
@Service
public class SysMessageCopywritingTextTypeServiceImpl extends
    BaseServiceImpl<SysMessageCopywritingTextTypeDAO, SysMessageCopywritingTextType> implements
    SysMessageCopywritingTextTypeService {

  private final SysMessageCopywritingTextTypeDAO sysMessageCopywritingTextTypeDAO;

  @Override
  public MessageCopywritingDTO getPushCopywriting(String pushTemplate, String lang) {
    return sysMessageCopywritingTextTypeDAO.findMsgTypePushCopywriting(pushTemplate, lang);
  }

  @Override
  public MessageCopywritingDTO getGreetRandomText(String lang) {
    return sysMessageCopywritingTextTypeDAO.findGreetRandomText(lang);
  }

  @Override
  public PageResult<SysMessageCopywritingTextType> pageCopywritingTextType(
      SysMessageCopywritingTypeQryCmd query) {
    return query()
        .eq(Objects.nonNull(query.getMsgType()), SysMessageCopywritingTextType::getMsgType,
            query.getMsgType())
        .eq(Objects.nonNull(query.getBusinessScene()),
            SysMessageCopywritingTextType::getBusinessScene, query.getBusinessScene())
        .ge(Objects.nonNull(query.getStartCreateDate()),
            SysMessageCopywritingTextType::getCreateTime, query.getStartCreateDate())
        .le(Objects.nonNull(query.getEndCreateDate()), SysMessageCopywritingTextType::getCreateTime,
            query.getEndCreateDate())
        .orderByDesc(SysMessageCopywritingTextType::getId)
        .page(query.getPageQuery());
  }

  @Override
  public void deleteById(Long id) {
    delete().eq(SysMessageCopywritingTextType::getId, id).execute();
  }

}
