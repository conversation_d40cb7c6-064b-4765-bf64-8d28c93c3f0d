package com.red.circle.other.infra.database.rds.service.emoji.impl;

import com.google.common.collect.Lists;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.emoji.EmojiConfigDAO;
import com.red.circle.other.infra.database.rds.entity.emoji.EmojiConfig;
import com.red.circle.other.infra.database.rds.service.emoji.EmojiConfigService;
import com.red.circle.other.inner.model.cmd.sys.SysEmojiConfigQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 表情管理 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Service
public class EmojiConfigServiceImpl extends BaseServiceImpl<EmojiConfigDAO, EmojiConfig> implements
    EmojiConfigService {

  @Override
  public List<EmojiConfig> listEmojiHistoryVersion(String sysOrigin) {

    if (StringUtils.isBlank(sysOrigin)) {
      return CollectionUtils.newArrayList();
    }

    return Optional.ofNullable(query()
        .eq(EmojiConfig::getGroupId, 0)
        .eq(EmojiConfig::getSysOrigin, sysOrigin)
        .eq(EmojiConfig::getShelfStatus, Boolean.TRUE)
        .orderByAsc(EmojiConfig::getSort)
        .last(PageConstant.MAX_LIMIT)
        .list()).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public List<EmojiConfig> listEmojiByGroupId(Long groupId) {
    return query()
        .eq(EmojiConfig::getGroupId, groupId)
        .eq(EmojiConfig::getShelfStatus, Boolean.TRUE)
        .orderByAsc(EmojiConfig::getSort)
        .last(PageConstant.MAX_LIMIT)
        .list();
  }

  @Override
  public Map<Long, List<EmojiConfig>> mapByGroupIds(Set<Long> groupIds) {
    if (CollectionUtils.isEmpty(groupIds)) {
      return CollectionUtils.newHashMap();
    }
    return Optional.ofNullable(query()
            .in(EmojiConfig::getGroupId, groupIds)
            .eq(EmojiConfig::getShelfStatus, Boolean.TRUE)
            .orderByAsc(EmojiConfig::getSort)
            .list())
        .map(emojiConfigs -> emojiConfigs.stream()
            .collect(Collectors.groupingBy(EmojiConfig::getGroupId)))
        .orElseGet(CollectionUtils::newHashMap);
  }

  @Override
  public List<EmojiConfig> listByIds(Set<Long> ids) {

    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newArrayList();
    }

    return Optional.ofNullable(
        query()
            .in(EmojiConfig::getId, ids)
            .last(PageConstant.formatLimit(ids.size()))
            .list()
    ).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public PageResult<EmojiConfig> pageEmojiConfig(SysEmojiConfigQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), EmojiConfig::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getGroupId()), EmojiConfig::getGroupId,
            query.getGroupId())
        .eq(Objects.nonNull(query.getShelfStatus()), EmojiConfig::getShelfStatus,
            query.getShelfStatus())
        .eq(StringUtils.isNotBlank(query.getType()), EmojiConfig::getType,
            query.getType())
        .orderByAsc(EmojiConfig::getSort)
        .page(query.getPageQuery());
  }


  @Override
  public List<EmojiConfig> list(String sysOrigin) {

    return Optional.ofNullable(getList(SysOriginPlatformEnum.valueOf(sysOrigin)))
        .orElse(Lists.newArrayList());
  }

  private List<EmojiConfig> getList(SysOriginPlatformEnum origin) {

    return query()
        .eq(EmojiConfig::getShelfStatus, Boolean.TRUE)
        .eq(EmojiConfig::getSysOrigin, origin.name())
        .orderByDesc(EmojiConfig::getCreateTime)
        .list();
  }

}
