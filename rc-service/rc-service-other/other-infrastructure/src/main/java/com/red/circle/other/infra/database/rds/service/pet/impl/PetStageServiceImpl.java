package com.red.circle.other.infra.database.rds.service.pet.impl;

import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.pet.PetStageDAO;
import com.red.circle.other.infra.database.rds.entity.pet.PetStage;
import com.red.circle.other.infra.database.rds.service.pet.PetStageService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 宠物阶段 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PetStageServiceImpl extends BaseServiceImpl<PetStageDAO, PetStage> implements
    PetStageService {

  @Override
  public Map<Long, List<PetStage>> mapByPetIds(Set<Long> petIds) {
    if (CollectionUtils.isEmpty(petIds)) {
      return Maps.newHashMap();
    }
    return Optional.ofNullable(
            query()
                .in(PetStage::getPetId, petIds)
                .list())
        .map(petStages -> petStages.stream().collect(Collectors.groupingBy(PetStage::getPetId)))
        .orElseGet(Maps::newHashMap);
  }

  @Override
  public List<PetStage> listByPetIds(Long petId) {
    return query()
        .eq(PetStage::getPetId, petId)
        .orderByAsc(PetStage::getLevel)
        .list();
  }

  @Override
  public PetStage getByState(Long petId, String stage) {
    return query()
        .eq(PetStage::getPetId, petId)
        .eq(PetStage::getStage, stage)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PetStage getFirst(Long petId) {
    return query()
        .eq(PetStage::getPetId, petId)
        .orderByAsc(PetStage::getLevel)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PetStage getNextPetStage(Long petId, Integer level) {
    return query()
        .eq(PetStage::getPetId, petId)
        .gt(PetStage::getLevel, level)
        .orderByAsc(PetStage::getLevel)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }
}
