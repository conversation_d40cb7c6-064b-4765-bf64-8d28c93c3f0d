package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.ApprovalUserAccountStatusLog;
import com.red.circle.other.inner.model.cmd.user.UserAccountStatusLogQryCmd;
import java.util.List;

/**
 * <p>
 * 审批账户历史记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-16
 */
public interface ApprovalUserAccountStatusLogService extends
    BaseService<ApprovalUserAccountStatusLog> {

  /**
   * 统计当天日期冻结数量
   *
   * @param userId 用户id
   * @return count
   */
  long countNowFreezeSize(Long userId);

  /**
   * 保存日志
   */
  void saveLog(ApprovalUserAccountStatusLog approvalUserAccountStatusLog);

  /**
   * 分页列表.
   */
  PageResult<ApprovalUserAccountStatusLog> pgeApprovalUserAccountStatusLog(
      UserAccountStatusLogQryCmd cmd);

  /**
   * 最近的审批列表.
   */
  List<ApprovalUserAccountStatusLog> lastApprovalUserAccountStatusLog(Long beApprovalUser,
      Integer size);
}
