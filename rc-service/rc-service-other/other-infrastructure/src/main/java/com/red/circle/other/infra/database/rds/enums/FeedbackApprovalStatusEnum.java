package com.red.circle.other.infra.database.rds.enums;

import com.red.circle.tool.core.text.StringUtils;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2019/11/9 14:33
 */
public enum FeedbackApprovalStatusEnum {
  /****/
  UNPROCESSED(0, "未处理"),
  PROCESSED(1, "已处理"),
  WAITING_FOR_PROCESSING(2, "待处理");

  private int value;
  private String des;

  FeedbackApprovalStatusEnum(int value, String des) {
    this.value = value;
    this.des = des;
  }

  public static String getDes(int value) {
    return Stream.of(FeedbackApprovalStatusEnum.values())
        .filter(feedbackApprovalStatusEnum -> Objects
            .equals(feedbackApprovalStatusEnum.getValue(), value))
        .findFirst()
        .map(FeedbackApprovalStatusEnum::getDes)
        .orElse(StringUtils.UNKNOWN);
  }

  public int getValue() {
    return value;
  }

  public String getDes() {
    return des;
  }
}
