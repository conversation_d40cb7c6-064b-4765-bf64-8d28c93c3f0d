package com.red.circle.other.infra.database.rds.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 启动页展示图片.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_start_page_plan")
public class StartPagePlan extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 类型.
   */
  @TableField("type")
  private String type;

  /**
   * 封面.
   */
  @TableField("cover")
  private String cover;

  /**
   * 过期时间.
   */
  @TableField("expire_time")
  private Timestamp expireTime;

}
