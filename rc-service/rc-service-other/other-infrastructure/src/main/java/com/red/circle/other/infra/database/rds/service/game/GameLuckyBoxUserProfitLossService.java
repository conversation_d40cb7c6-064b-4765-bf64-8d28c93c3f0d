package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxUserProfitLoss;
import java.math.BigDecimal;

/**
 * <p>
 * Lucky Box用户盈利亏损记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-13
 */
public interface GameLuckyBoxUserProfitLossService extends BaseService<GameLuckyBoxUserProfitLoss> {

  BigDecimal getProfitLossAmountByUserId(Long userId);

  void save(Long userId, BigDecimal amount, SysOriginPlatformEnum sysOrigin);

}
