package com.red.circle.other.infra.database.rds.service.gift;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.gift.GiftPack;
import com.red.circle.other.inner.model.cmd.sys.GiftPackQryCmd;
import com.red.circle.other.inner.model.dto.sys.SysGiftPackDTO;


/**
 * <p>
 * 礼包 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-10
 */
public interface GiftPackService extends BaseService<GiftPack> {

  PageResult<GiftPack> getGiftPackInfo(GiftPackQryCmd query);

  void addGiftPack(SysGiftPackDTO sysGiftPackDTO);

  void updateGiftPack(SysGiftPackDTO sysGiftPackDTO);

  void deleteGiftPack(Long id);

}
