package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * CP小屋配置.
 * </p>
 *
 * <AUTHOR> on 2023-11-20 10:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_cp_cabin_config")
public class CpCabinConfig extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 归属系统.
   */
  @TableField("name")
  private String name;

  /**
   * 解锁小屋CP值.
   */
  @TableField("cabin_unlock_value")
  private Long cabinUnlockValue;

  /**
   * 归属系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 小屋图标.
   */
  @TableField("cover")
  private String cover;

  /**
   * 小屋图标加锁.
   */
  @TableField("lock_cover")
  private String lockCover;

  /**
   * 个人资料页图.
   */
  @TableField("person_cover")
  private String personCover;

  /**
   * 个人卡片图.
   */
  @TableField("card_cover")
  private String cardCover;

  /**
   * 空间背景图.
   */
  @TableField("background_cover")
  private String backgroundCover;

  /**
   * 资源.
   */
  @TableField("source_url")
  private String sourceUrl;

  /**
   * 主页小闪光资源.
   */
  @TableField("source_mini_url")
  private String sourceMiniUrl;

  /**
   * 区域.
   */
  @TableField("regions")
  private String regions;

  /**
   * 排序.
   */
  @TableField("sort")
  private Long sort;


}
