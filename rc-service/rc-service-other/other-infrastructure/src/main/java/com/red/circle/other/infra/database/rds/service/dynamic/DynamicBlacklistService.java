package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicBlacklist;
import com.red.circle.other.inner.model.cmd.dynamic.DynamicBlacklistCmd;
import com.red.circle.other.inner.model.cmd.dynamic.DynamicBlacklistQryCmd;

/**
 * <p>
 * 动态-黑名单 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-30
 */
public interface DynamicBlacklistService extends BaseService<DynamicBlacklist> {

  /**
   * 用户是否存在黑名单中
   *
   * @param userId 用户
   * @return true存在
   */
  boolean exist(Long userId);

  void deleteByUserId(Long userId);

  void add(DynamicBlacklistCmd cmd);

  PageResult<DynamicBlacklist> pageDynamicBlacklist(DynamicBlacklistQryCmd query);
}
