package com.red.circle.other.infra.database.rds.service.activity;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityFriendshipCardChampion;

/**
 * <p>
 * 用户友谊关系卡 - 活动冠军记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface ActivityFriendshipCardChampionService extends
    BaseService<ActivityFriendshipCardChampion> {

  void save(Long userId);

  Integer getChampionFrequencyByUserId(Long userId);

}
