package com.red.circle.other.infra.database.rds.service.props.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsSourceRecordDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsSourceRecord;
import com.red.circle.other.infra.database.rds.service.props.PropsSourceRecordService;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import com.red.circle.tool.core.collection.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

/**
 * <p>
 * 道具资源信息 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-29
 */
@Service
public class PropsSourceRecordServiceImpl extends
        BaseServiceImpl<PropsSourceRecordDAO, PropsSourceRecord> implements PropsSourceRecordService {

    @Override
    public PropsSourceRecord getCheapAvatarFrame() {
        return Optional.ofNullable(query()
                        .eq(PropsSourceRecord::getDel, Boolean.FALSE)
                        .in(PropsSourceRecord::getType, PropsCommodityType.AVATAR_FRAME)
                        .orderByAsc(PropsSourceRecord::getAmount)
                        .last(PageConstant.LIMIT_ONE)
                        .getOne())
                .orElse(null);
    }

    @Override
    public Map<Long, PropsSourceRecord> mapByIds(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return CollectionUtils.newHashMap();
        }
        return Optional.ofNullable(query().in(PropsSourceRecord::getId, ids).list())
                .map(propsSourceRecords -> propsSourceRecords.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(PropsSourceRecord::getId, v -> v)))
                .orElse(CollectionUtils.newHashMap());
    }

    @Override
    public List<PropsSourceRecord> listByIds(List<Long> ids) {
        return query().in(PropsSourceRecord::getId, ids).list();
    }

    @Override
    public PropsSourceRecord getPropsById(Long id) {
        return query().eq(PropsSourceRecord::getId, id)
                .last(PageConstant.LIMIT_ONE)
                .getOne();
    }


    @Override
    public List<PropsSourceRecord> listSysOrigin(String sysOrigin, String type) {
        return Optional.ofNullable(
                query().eq(PropsSourceRecord::getType, type)
                        .eq(PropsSourceRecord::getSysOrigin, sysOrigin)
                        .eq(PropsSourceRecord::getDel, Boolean.FALSE)
                        .orderByDesc(PropsSourceRecord::getCreateTime)
                        .list()).orElse(Lists.newArrayList());
    }

    @Override
    public List<PropsSourceRecord> listSysOrigin(String sysOrigin, String type,
                                                 Set<Long> excludePropsIds) {
        return query()
                .notIn(CollectionUtils.isNotEmpty(excludePropsIds), PropsSourceRecord::getId,
                        excludePropsIds)
                .eq(PropsSourceRecord::getType, type)
                .eq(PropsSourceRecord::getSysOrigin, sysOrigin)
                .eq(PropsSourceRecord::getDel, Boolean.FALSE)
                .list();
    }

    @Override
    public void offShelf(Long id, Boolean offShelf) {
        update().set(PropsSourceRecord::getDel, offShelf)
                .eq(PropsSourceRecord::getId, id).execute();
    }

    @Override
    public List<PropsSourceRecord> adminFreePropsList() {
        return Optional.ofNullable(
                query()
                        .eq(PropsSourceRecord::getAdminFree, Boolean.TRUE)
                        .eq(PropsSourceRecord::getDel, Boolean.FALSE)
                        .orderByDesc(PropsSourceRecord::getCreateTime)
                        .list()).orElse(Lists.newArrayList());

    }

}
