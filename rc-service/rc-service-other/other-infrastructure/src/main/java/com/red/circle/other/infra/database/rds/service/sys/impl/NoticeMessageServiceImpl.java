package com.red.circle.other.infra.database.rds.service.sys.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.NoticeMessageDAO;
import com.red.circle.other.infra.database.rds.entity.sys.NoticeMessage;
import com.red.circle.other.infra.database.rds.service.sys.NoticeMessageService;
import com.red.circle.other.inner.model.cmd.sys.SysNoticeMessageQryCmd;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统公告通知消息 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
@Service
@RequiredArgsConstructor
public class NoticeMessageServiceImpl extends
    BaseServiceImpl<NoticeMessageDAO, NoticeMessage> implements NoticeMessageService {

  @Override
  public List<NoticeMessage> listLastOneMonth(String sysOrigin) {
    return query()
        .eq(NoticeMessage::getSysOrigin, sysOrigin)
        .orderByDesc(NoticeMessage::getCreateTime)
        .last(PageConstant.MAX_LIMIT)
        .list();
  }

  @Override
  public PageResult<NoticeMessage> getNoticeMessage(SysNoticeMessageQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), NoticeMessage::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getShelfStatus()), NoticeMessage::getShelfStatus,
            query.getShelfStatus())
        .eq(Objects.nonNull(query.getAnnouncement()), NoticeMessage::getAnnouncement,
            query.getAnnouncement())
        .ge(Objects.nonNull(query.getStartCreateDate()), NoticeMessage::getCreateTime,
            query.getStartCreateDate())
        .le(Objects.nonNull(query.getEndCreateDate()), NoticeMessage::getCreateTime,
            query.getEndCreateDate())
        .orderByDesc(NoticeMessage::getAnnouncementTime, NoticeMessage::getShelfStatus,
            NoticeMessage::getCreateTime)
        .page(query.getPageQuery());
  }
}
