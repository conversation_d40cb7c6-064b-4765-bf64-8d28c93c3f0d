package com.red.circle.other.infra.database.rds.service.activity.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.ActivityHallFameDAO;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityHallFame;
import com.red.circle.other.infra.database.rds.service.activity.ActivityHallFameService;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动名人堂 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Service
public class ActivityHallFameServiceImpl extends
    BaseServiceImpl<ActivityHallFameDAO, ActivityHallFame> implements ActivityHallFameService {

  @Override
  public List<ActivityHallFame> listBySysOrigin(String sysOrigin) {

    return query().eq(ActivityHallFame::getSysOrigin, sysOrigin)
        .eq(ActivityHallFame::getDel, Boolean.FALSE)
        .orderByDesc(ActivityHallFame::getSort, ActivityHallFame::getCreateTime)
        .last(PageConstant.formatLimit(100))
        .list();
  }
}
