package com.red.circle.other.infra.gateway.user;

import com.red.circle.framework.web.util.ValidatorUtils;
import com.red.circle.other.domain.gateway.user.ability.UserRelationsCalculatorGateway;
import com.red.circle.other.domain.model.user.ability.UserRelationsCalculator;
import com.red.circle.other.infra.database.rds.service.user.user.UserRelationshipCounterService;
import com.red.circle.other.infra.database.rds.service.user.user.UserSubscriptionService;
import com.red.circle.other.infra.enums.user.user.UserRelationshipCounterEnum;
import com.red.circle.tool.core.json.JacksonUtils;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用户关系计算 实现.
 *
 * <AUTHOR> on 2023/12/17
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserRelationsCalculatorGatewayImpl implements UserRelationsCalculatorGateway {

  private final UserSubscriptionService userSubscriptionService;
  private final UserRelationshipCounterService userRelationshipCounterService;

  @Override
  public void process(UserRelationsCalculator calculator) {
    if (!ValidatorUtils.validateFastPass(calculator)) {
      log.error("param error:{}", JacksonUtils.toJson(calculator));
      return;
    }

    // 订阅
    if (calculator.checkSubscription()) {
      processSubscription(calculator);
      return;
    }

    // 访客
    if (calculator.checkInterview()) {
      processInterview(calculator);
      return;
    }

    // 朋友
    if (calculator.checkFriend()) {
      processFriend(calculator);
      return;
    }

    // 其他
    processOther(calculator);
  }

  private void processFriend(UserRelationsCalculator cmd) {

    if (cmd.checkIncrement()) {
      userRelationshipCounterService.incrQuantity(Set.of(
          cmd.getUserId(), cmd.getSubUserId()
      ), UserRelationshipCounterEnum.FRIEND);
      return;
    }

    userRelationshipCounterService.decrQuantityNotCreate(Set.of(
        cmd.getUserId(), cmd.getSubUserId()
    ), UserRelationshipCounterEnum.FRIEND);
  }

  private void processInterview(UserRelationsCalculator param) {
    if (param.checkIncrement()) {
      userRelationshipCounterService.incrQuantity(param.getSubUserId(),
          UserRelationshipCounterEnum.valueOf(param.getType().name()));
    }
  }

  private void processOther(UserRelationsCalculator param) {
    UserRelationshipCounterEnum type = UserRelationshipCounterEnum.valueOf(param.getType().name());

    if (param.checkIncrement()) {
      userRelationshipCounterService.incrQuantity(param.getUserId(), type);
      userRelationshipCounterService.incrQuantity(param.getSubUserId(), type);
      return;
    }

    userRelationshipCounterService.decrQuantity(param.getUserId(), type);
    userRelationshipCounterService.decrQuantity(param.getSubUserId(), type);
  }

  private void processSubscription(UserRelationsCalculator param) {
    if (param.checkIncrement()) {
      // 绑定互相订阅状态、添加关注数量、添加粉丝数量
      userSubscriptionService.updateMutualRelations(param.getUserId(), param.getSubUserId(),
          checkSubscription(param.getSubUserId(), param.getUserId()));
      userRelationshipCounterService
          .incrQuantity(param.getUserId(), UserRelationshipCounterEnum.SUBSCRIPTION);
      userRelationshipCounterService
          .incrQuantity(param.getSubUserId(), UserRelationshipCounterEnum.FANS);
      return;
    }

    // 移除互相订阅状态，移除关注数量，移除粉丝数量
    userSubscriptionService
        .updateMutualRelations(param.getUserId(), param.getSubUserId(), Boolean.FALSE);
    userRelationshipCounterService
        .decrQuantity(param.getUserId(), UserRelationshipCounterEnum.SUBSCRIPTION);
    userRelationshipCounterService
        .decrQuantity(param.getSubUserId(), UserRelationshipCounterEnum.FANS);
  }

  private boolean checkSubscription(Long userId, Long subUserId) {
    return userSubscriptionService.checkSubscription(userId, subUserId);
  }

}
