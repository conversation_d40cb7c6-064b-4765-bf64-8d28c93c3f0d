package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户赠送友谊关系卡记录.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_give_friendship_card_apply")
public class GiveFriendshipCardApply extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 发送用户.
   */
  @TableField("send_user_id")
  private Long sendUserId;

  /**
   * 接收用户.
   */
  @TableField("accept_user_id")
  private Long acceptUserId;

  /**
   * 卡片类型.
   */
  @TableField("card_type")
  private String cardType;

  /**
   * 卡片金币.
   */
  @TableField("card_gold")
  private BigDecimal cardGold;

  /**
   * 申请文案.
   */
  @TableField("give_text")
  private String giveText;

  /**
   * 状态 'WAIT','AGREE','REFUSE','REIMBURSE'.
   */
  @TableField("status")
  private String status;

}
