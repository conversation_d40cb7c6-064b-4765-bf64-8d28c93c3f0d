package com.red.circle.other.infra.database.rds.service.dynamic;


import com.red.circle.common.business.enums.DynamicMessageEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.dynamic.DynamicMessage;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 动态-我的消息 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-11
 */
public interface DynamicMessageService extends BaseService<DynamicMessage> {

  void saveBatch(String content, Long contentId, Set<Long> toUserIds, String type);

  void save(String content, Long contentId, Long toUserId, DynamicMessageEnum type);

  /**
   * 根据id集合返回
   *
   * @return 消息数量
   */
  Map<Long, List<DynamicMessage>> mapByUserIdsByContentId(Collection<Long> toUserIds);

  /**
   * 接收人消息数量
   *
   * @param toUserId 接收人id
   * @return 数量
   */
  Integer getCountByToUserId(Long toUserId);

  /**
   * 获得分页数据
   *
   * @param pageNumber 第几页
   * @param toUserId   被评价人或点赞ID
   */
  List<DynamicMessage> pageByTimeDesc(Integer pageNumber, Long toUserId);

  /**
   * 我的消息列表统计
   *
   * @param toUserId 被评价人或点赞ID
   * @return 数量
   */
  Long countDynamicMessage(Long toUserId);

}
