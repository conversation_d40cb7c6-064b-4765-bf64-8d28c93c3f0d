package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.common.business.enums.ViolationEnum;
import com.red.circle.framework.core.dto.PageCommand;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.PhotoWall;
import com.red.circle.other.inner.model.cmd.user.PhotoWallApprovalTableQryCmd;
import com.red.circle.other.inner.model.dto.user.UserPhotoWallDTO;
import java.util.List;

/**
 * <p>
 * 用户照片墙 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-08
 */
public interface UserPhotoWallService extends BaseService<PhotoWall> {

  void deleteById(Long id);

  PageResult<PhotoWall> pageUserPhotoWall(PageCommand command);

  List<PhotoWall> listUserPhotoWall(Long userId);

  PhotoWall userPhotoWall(Long id);

  List<PhotoWall> listUserPhotoWallNormal(Long userId);

  PageResult<UserPhotoWallDTO> pageUserDetailsPhotoWall(
      PhotoWallApprovalTableQryCmd cmd);

  /**
   * 修改违规状态
   *
   * @param ids           资源id集合
   * @param violationEnum 违规状态
   * @param approvalUser  审批人
   * @return 是否修改成功
   */
  boolean updateViolationStatus(List<Long> ids, ViolationEnum violationEnum, Long approvalUser);
}
