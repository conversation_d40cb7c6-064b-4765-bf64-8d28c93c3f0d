package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.RoomThemeUserCustomizeDAO;
import com.red.circle.other.infra.database.rds.entity.props.RoomThemeUserCustomize;
import com.red.circle.other.infra.database.rds.service.props.RoomThemeUserCustomizeService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户房间主题 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-22
 */
@Service
@RequiredArgsConstructor
public class RoomThemeUserCustomizeServiceImpl extends
    BaseServiceImpl<RoomThemeUserCustomizeDAO, RoomThemeUserCustomize> implements
    RoomThemeUserCustomizeService {


  @Override
  public Map<Long, RoomThemeUserCustomize> mapByIds(Set<Long> ids) {
    if (CollectionUtils.isEmpty(ids)) {
      return CollectionUtils.newHashMap();
    }
    return Optional.ofNullable(
            query()
                .in(RoomThemeUserCustomize::getId, ids)
                .orderByAsc(RoomThemeUserCustomize::getId)
                .list()
        ).map(themes -> themes.stream()
            .collect(Collectors.toMap(RoomThemeUserCustomize::getId, v -> v)))
        .orElse(CollectionUtils.newHashMap());
  }

  @Override
  public RoomThemeUserCustomize getLatestCustomize(Long userId) {
    return Optional.ofNullable(
        query().in(RoomThemeUserCustomize::getUserId, userId)
            .orderByDesc(RoomThemeUserCustomize::getId)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).orElse(null);
  }
}
