package com.red.circle.other.infra.database.rds.service.user.device;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.ArchiveDevice;
import com.red.circle.other.inner.model.cmd.user.device.ArchiveDeviceQryCmd;
import java.util.Set;

/**
 * <p>
 * 封禁设备 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-29
 */
public interface ArchiveDeviceService extends BaseService<ArchiveDevice> {

  /**
   * 是否存在设备.
   *
   * @param deviceNo 设备号
   * @return true 存在，false 不存在
   */
  boolean existsDevice(String deviceNo, String sysOrigin);

  /**
   * 添加设备.
   *
   * @param deviceNo 设备.
   */
  void addDeviceNo(String deviceNo, String sysOrigin);

  /**
   * 分页查询设备
   */
  PageResult<ArchiveDevice> pageList(ArchiveDeviceQryCmd query);

  /**
   * 添加一批设备.
   */
  void addBatchDeviceNo(Set<String> deviceNos, String sysOrigin);

  /**
   * 移除封禁设备.
   *
   * @param deviceNo 设备.
   */
  void removeDeviceNo(String deviceNo, String sysOrigin);

  /**
   * 删除设备
   */
  void delByDeviceNo(Long id);
}
