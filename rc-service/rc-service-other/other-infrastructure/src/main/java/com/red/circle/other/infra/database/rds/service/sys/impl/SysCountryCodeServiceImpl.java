package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysCountryCodeDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysCountryCode;
import com.red.circle.other.infra.database.rds.service.sys.SysCountryCodeService;
import com.red.circle.other.inner.model.cmd.sys.SysCountryCodePageQryCmd;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.num.NumConstant;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-02-24
 */
@RequiredArgsConstructor
@Service
public class SysCountryCodeServiceImpl extends
    BaseServiceImpl<SysCountryCodeDAO, SysCountryCode> implements SysCountryCodeService {

  @Override
  public SysCountryCode getByCode(String code) {
    if (StringUtils.isBlank(code)) {
      return null;
    }

    int len = code.length();
    boolean isTwo = Objects.equals(len, NumConstant.TWO);
    String upperCaseCode = code.toUpperCase();
    return query()
        .eq(isTwo, SysCountryCode::getAlphaTwo, upperCaseCode)
        .eq(!isTwo, SysCountryCode::getAlphaThree, upperCaseCode)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public List<SysCountryCode> listOpenCountry() {
    return query()
        .eq(SysCountryCode::getOpen, Boolean.TRUE)
        .orderByDesc(SysCountryCode::getTop, SysCountryCode::getSort)
        .list();
  }

  @Override
  public List<SysCountryCode> listOpenCountryTop(Integer size) {
    return query()
        .eq(SysCountryCode::getOpen, Boolean.TRUE)
        .eq(SysCountryCode::getTop, Boolean.TRUE)
        .last(PageConstant.formatLimit(size))
        .orderByDesc(SysCountryCode::getSort)
        .list();
  }

  @Override
  public boolean checkOpenPhonePrefix(Integer phonePrefix) {
    return Optional.ofNullable(
            query()
                .select(SysCountryCode::getId)
                .eq(SysCountryCode::getOpen, Boolean.TRUE)
                .eq(SysCountryCode::getPhonePrefix, phonePrefix)
                .last(PageConstant.LIMIT_ONE)
                .getOne()
        ).map(sysCountryCode -> Objects.nonNull(sysCountryCode.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public Map<Long, SysCountryCode> mapByIdes(Set<Long> countryIds) {
    Map<Long, SysCountryCode> result;
    if (CollectionUtils.isEmpty(countryIds)) {
      result = CollectionUtils.newHashMap();
    } else {
      result = Optional.ofNullable(query()
              .in(SysCountryCode::getId, countryIds)
              .list())
          .map(sysCountryCodes -> sysCountryCodes.stream()
              .collect(Collectors.toMap(SysCountryCode::getId, v -> v)))
          .orElseGet(CollectionUtils::newHashMap);
    }
    return result;
  }


  @Override
  public List<SysCountryCode> listContent() {
    return query().orderByDesc(SysCountryCode::getTop, SysCountryCode::getOpen).list();
  }

  @Override
  public PageResult<SysCountryCode> sysCountryCodePage(SysCountryCodePageQryCmd query) {

    return query()
        .like(StringUtils.isNotBlank(query.getCountryName()), SysCountryCode::getCountryName,
            query.getCountryName())
        .like(StringUtils.isNotBlank(query.getAliasName()), SysCountryCode::getAliasName,
            query.getAliasName())
        .orderByDesc(SysCountryCode::getOpen)
        .page(query.getPageQuery());
  }

  @Override
  public void updateCountryCode(SysCountryCode sysCountryCode) {
    ResponseAssert.isTrue(CommonErrorCode.UPDATE_FAILURE,
        updateSelectiveById(sysCountryCode));
  }
}
