package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.UserRelationshipCounterDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserRelationshipCounter;
import com.red.circle.other.infra.database.rds.service.user.user.UserRelationshipCounterService;
import com.red.circle.other.infra.enums.user.user.UserRelationshipCounterEnum;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户关系计算器 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-20
 */
@Service
public class UserRelationshipCounterServiceImpl extends
    BaseServiceImpl<UserRelationshipCounterDAO, UserRelationshipCounter> implements
    UserRelationshipCounterService {

  @Override
  public void incrQuantity(Long userId, UserRelationshipCounterEnum type) {
    if (existsType(userId, type)) {
      update().setSql("quantity=quantity+1")
          .eq(UserRelationshipCounter::getType, type)
          .eq(UserRelationshipCounter::getUserId, userId)
          .execute();
      return;
    }
    create(userId, type);
  }

  @Override
  public void incrQuantity(Set<Long> userIds, UserRelationshipCounterEnum type) {
    if (CollectionUtils.isEmpty(userIds)) {
      return;
    }
    for (Long userId : userIds) {
      incrQuantity(userId, type);
    }
  }

  @Override
  public void decrQuantity(Long userId, UserRelationshipCounterEnum type) {
    update().setSql("quantity=quantity-1")
        .eq(UserRelationshipCounter::getType, type)
        .eq(UserRelationshipCounter::getUserId, userId)
        .gt(UserRelationshipCounter::getQuantity, 0)
        .execute();
  }

  @Override
  public void decrQuantityNotCreate(Set<Long> userIds, UserRelationshipCounterEnum type) {
    update().setSql("quantity=quantity-1")
        .eq(UserRelationshipCounter::getType, type)
        .in(UserRelationshipCounter::getUserId, userIds)
        .gt(UserRelationshipCounter::getQuantity, 0)
        .execute();
  }

  @Override
  public Long getQuantity(Long userId, UserRelationshipCounterEnum type) {
    return Optional.ofNullable(
        query().select(UserRelationshipCounter::getQuantity)
            .eq(UserRelationshipCounter::getUserId, userId)
            .eq(UserRelationshipCounter::getType, type)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).map(UserRelationshipCounter::getQuantity).orElse(0L);
  }

  @Override
  public List<UserRelationshipCounter> getRelationshipCounter(Long userId) {
    return query().eq(UserRelationshipCounter::getUserId, userId).list();
  }

  private boolean existsType(Long userId, UserRelationshipCounterEnum type) {
    return Optional.ofNullable(
            query().select(UserRelationshipCounter::getId)
                .eq(UserRelationshipCounter::getUserId, userId)
                .eq(UserRelationshipCounter::getType, type)
                .last(PageConstant.LIMIT_ONE)
                .getOne()
        ).map(userRelationshipCounter -> Objects.nonNull(userRelationshipCounter.getId()))
        .orElse(Boolean.FALSE);
  }

  private void create(Long userId, UserRelationshipCounterEnum relationEnum) {
    save(new UserRelationshipCounter()
        .setUserId(userId)
        .setType(relationEnum.name())
        .setQuantity(1L)
    );
  }


  @Override
  public Map<Long, Long> mapFansNumberByUserId(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return Maps.newHashMap();
    }
    return Optional.ofNullable(query()
            .select(UserRelationshipCounter::getUserId, UserRelationshipCounter::getQuantity)
            .in(UserRelationshipCounter::getUserId, userIds)
            .eq(UserRelationshipCounter::getType, UserRelationshipCounterEnum.FANS)
            .list())
        .map(userRelationshipCounters -> userRelationshipCounters.stream().collect(
            Collectors
                .toMap(UserRelationshipCounter::getUserId, UserRelationshipCounter::getQuantity)))
        .orElse(Maps.newHashMap());
  }
}
