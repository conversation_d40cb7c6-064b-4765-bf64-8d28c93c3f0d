package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.google.common.collect.Sets;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.InviteRedPacketHelpDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteRedPacketHelp;
import com.red.circle.other.infra.database.rds.service.user.user.InviteRedPacketHelpService;
import com.red.circle.other.inner.model.cmd.user.invite.InviteRedPacketHelpLogPageQryCmd;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 邀请新用户-助力记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
@AllArgsConstructor
@Service
public class InviteRedPacketHelpServiceImpl extends
    BaseServiceImpl<InviteRedPacketHelpDAO, InviteRedPacketHelp> implements
    InviteRedPacketHelpService {

  @Override
  public List<InviteRedPacketHelp> pageHelp(Long lastId, Long inviteUserId, Long redPacketId) {

    return query()
        .eq(InviteRedPacketHelp::getInviteUserId, inviteUserId)
        .eq(InviteRedPacketHelp::getRedPacketId, redPacketId)
        .lt(Objects.nonNull(lastId), InviteRedPacketHelp::getId, lastId)
        .orderByDesc(InviteRedPacketHelp::getId)
        .last(PageConstant.formatLimit(20))
        .list();
  }

  @Override
  public PageResult<InviteRedPacketHelp> pageHelp(InviteRedPacketHelpLogPageQryCmd cmd) {

    return query()
        .eq(Objects.nonNull(cmd.getUserId()), InviteRedPacketHelp::getUserId,
            cmd.getUserId())
        .eq(Objects.nonNull(cmd.getInviteUserId()), InviteRedPacketHelp::getInviteUserId,
            cmd.getInviteUserId())
        .eq(StringUtils.isNotBlank(cmd.getSysOrigin()),
            InviteRedPacketHelp::getSysOrigin, cmd.getSysOrigin())
        .eq(Objects.nonNull(cmd.getRedPacketId()), InviteRedPacketHelp::getRedPacketId,
            cmd.getRedPacketId())
        .eq(StringUtils.isNotBlank(cmd.getUserType()), InviteRedPacketHelp::getUserType,
            cmd.getUserType())
        .eq(StringUtils.isNotBlank(cmd.getInviteIp()), InviteRedPacketHelp::getInviteIp,
            cmd.getInviteIp())
        .eq(StringUtils.isNotBlank(cmd.getIp()), InviteRedPacketHelp::getIp, cmd.getIp())
        .eq(StringUtils.isNotBlank(cmd.getInviteImei()), InviteRedPacketHelp::getInviteImei,
            cmd.getInviteImei())
        .eq(StringUtils.isNotBlank(cmd.getImei()), InviteRedPacketHelp::getImei, cmd.getImei())
        .ge(Objects.nonNull(cmd.getStartTime()), InviteRedPacketHelp::getCreateTime,
            cmd.getStartTime())
        .le(Objects.nonNull(cmd.getEndTime()), InviteRedPacketHelp::getCreateTime,
            cmd.getEndTime())
        .orderByDesc(InviteRedPacketHelp::getCreateTime)
        .page(cmd.getPageQuery());
  }

  @Override
  public Set<Long> getInviteUserIdList(Long userId){

    return Optional.ofNullable(
            query().select(InviteRedPacketHelp::getUserId)
                    .eq(InviteRedPacketHelp::getInviteUserId, userId)
                    .list()
    ).map(actualEquities -> actualEquities.stream().map(InviteRedPacketHelp::getUserId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet())).orElse(Sets.newHashSet());

  }
}
