package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.inner.asserts.ImErrorCode;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.convertor.user.UserProfileInfraConvertor;
import com.red.circle.other.infra.database.rds.dao.user.user.UserBeautifulNumberDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserBeautifulNumber;
import com.red.circle.other.infra.database.rds.service.user.user.UserBeautifulNumberService;
import com.red.circle.other.inner.model.cmd.user.BeautifulNumberQryCmd;
import com.red.circle.other.inner.model.cmd.user.UserBeautifulNumberCmd;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户靓号 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-18
 */
@Service
@RequiredArgsConstructor
public class UserBeautifulNumberServiceImpl extends
    BaseServiceImpl<UserBeautifulNumberDAO, UserBeautifulNumber> implements
    UserBeautifulNumberService {

  private final UserProfileInfraConvertor userProfileInfraConvertor;

  @Override
  public boolean exists(Long account) {
    return Optional.ofNullable(
            query().select(UserBeautifulNumber::getId).last(PageConstant.LIMIT_ONE).getOne())
        .map(number -> Objects.nonNull(number.getId()))
        .orElse(Boolean.FALSE);
  }

  @Override
  public void updateBeautifulNumber(UserBeautifulNumberCmd cmd) {

    UserBeautifulNumber userBeautifulNumber = userProfileInfraConvertor.toUserBeautifulNumberCmd(
        cmd);
    if (CollectionUtils.isNotEmpty(cmd.getTabs())) {
      userBeautifulNumber.setTabName(cmd.getTabs().get(0).getTabName());
      userBeautifulNumber.setTypeName(cmd.getTabs().get(0).getTypeName());
    }
    ResponseAssert.isTrue(CommonErrorCode.UPDATE_FAILURE, updateInfo(userBeautifulNumber));
  }


  private boolean updateInfo(UserBeautifulNumber userBeautifulNumber) {
    return update()
        .set(UserBeautifulNumber::getStatus, userBeautifulNumber.getStatus())
        .set(UserBeautifulNumber::getTabName, userBeautifulNumber.getTabName())
        .set(UserBeautifulNumber::getTypeName, userBeautifulNumber.getTypeName())
        .set(UserBeautifulNumber::getGold, userBeautifulNumber.getGold())
        .set(UserBeautifulNumber::getWorth, userBeautifulNumber.getWorth())
        .eq(UserBeautifulNumber::getId, userBeautifulNumber.getId())
        .last(PageConstant.LIMIT_ONE)
        .execute();
  }

  @Override
  public void addBeautifulNumber(UserBeautifulNumberCmd dto) {
    ResponseAssert.failure(ImErrorCode.USER_ALREADY_EXISTS);
    ResponseAssert.isNull(CommonErrorCode.INFORMATION_EXISTS,
        query().eq(UserBeautifulNumber::getUserAccount, dto.getUserAccount()).getOne());
    UserBeautifulNumber userBeautifulNumber = userProfileInfraConvertor.toUserBeautifulNumberCmd(
        dto);
    if (CollectionUtils.isNotEmpty(dto.getTabs())) {
      userBeautifulNumber.setTabName(dto.getTabs().get(0).getTabName());
      userBeautifulNumber.setTypeName(dto.getTabs().get(0).getTypeName());
    }
    ResponseAssert.isTrue(CommonErrorCode.SAVE_FAILURE, save(userBeautifulNumber));
  }


  @Override
  public PageResult<UserBeautifulNumber> getBeautifulNumber(BeautifulNumberQryCmd query) {
    PageResult<UserBeautifulNumber> pageResult = query()
        .eq(Objects.nonNull(query.getUserAccount()), UserBeautifulNumber::getUserAccount,
            query.getUserAccount())
        .eq(StringUtils.isNotBlank(query.getStatus()), UserBeautifulNumber::getStatus,
            query.getStatus())
        .eq(StringUtils.isNotBlank(query.getTabName()), UserBeautifulNumber::getTabName,
            query.getTabName())
        .eq(StringUtils.isNotBlank(query.getTypeName()), UserBeautifulNumber::getTypeName,
            query.getTypeName())
        .orderByDesc(UserBeautifulNumber::getCreateTime)
        .page(query.getPageQuery());

    return pageResult;

  }
}
