package com.red.circle.other.infra.database.rds.service.live.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.live.RoomSubscriptionCountDAO;
import com.red.circle.other.infra.database.rds.entity.live.RoomSubscriptionCount;
import com.red.circle.other.infra.database.rds.service.live.RoomSubscriptionCountService;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间订阅数 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-02
 */
@Service
public class RoomSubscriptionCountServiceImpl extends
    BaseServiceImpl<RoomSubscriptionCountDAO, RoomSubscriptionCount> implements
    RoomSubscriptionCountService {

  @Override
  public Boolean saveRoomSubscriptionCount(Long roomId, Boolean isAdd) {
    RoomSubscriptionCount roomSubscription = query().eq(RoomSubscriptionCount::getRoomId, roomId)
        .last(PageConstant.LIMIT_ONE).getOne();
    if (Objects.isNull(roomSubscription)) {
      return save(new RoomSubscriptionCount()
          .setRoomId(roomId)
          .setSubscriptionCount(isAdd ? 1 : 0)
      );
    }
    return update()
        .set(RoomSubscriptionCount::getSubscriptionCount,
            isAdd ? roomSubscription.getSubscriptionCount() + 1 : getAnInt(roomSubscription))
        .eq(RoomSubscriptionCount::getRoomId, roomId).last(PageConstant.LIMIT_ONE)
        .execute();
  }

  private int getAnInt(RoomSubscriptionCount room) {
    return room.getSubscriptionCount() > 0 ? room.getSubscriptionCount() - 1 : 0;
  }
}
