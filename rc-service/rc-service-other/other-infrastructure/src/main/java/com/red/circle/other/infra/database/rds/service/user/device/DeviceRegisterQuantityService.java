package com.red.circle.other.infra.database.rds.service.user.device;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.device.DeviceRegisterQuantity;
import java.util.Collection;
import java.util.Map;

/**
 * <p>
 * 设备注册数量 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-15
 */
public interface DeviceRegisterQuantityService extends BaseService<DeviceRegisterQuantity> {

  /**
   * 累计数量.
   *
   * @param deviceNo 设备号
   */
  void incrQuantity(String deviceNo, String sysOrigin);

  /**
   * 获取注册设备数量.
   *
   * @param deviceNo 设备号
   */
  int getDeviceNoQuantity(String deviceNo, String sysOrigin);

  /**
   * 获取注册设备数量映射.
   */
  Map<String, DeviceRegisterQuantity> mapDeviceRegisterQuantity(
      Collection<String> deviceNos, String sysOrigin);
}
