package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.CpBlessRecordDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.CpBlessRecord;
import com.red.circle.other.infra.database.rds.service.user.user.CpBlessRecordService;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * CP祝福礼物记录 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
@AllArgsConstructor
@Service
public class CpBlessRecordServiceImpl extends
    BaseServiceImpl<CpBlessRecordDAO, CpBlessRecord> implements
    CpBlessRecordService {

  @Override
  public List<CpBlessRecord> listByUserId(Long userId) {
    return query()
        .eq(CpBlessRecord::getUserId, userId)
        .eq(CpBlessRecord::getReading, Boolean.FALSE)
        .orderByDesc(CpBlessRecord::getCreateTime)
        .last(PageConstant.formatLimit(200))
        .list();
  }

  @Override
  public void updateByUserId(Long userId) {
    update()
        .set(CpBlessRecord::getReading, Boolean.TRUE)
        .eq(CpBlessRecord::getUserId, userId)
        .eq(CpBlessRecord::getReading, Boolean.FALSE)
        .execute();
  }

  @Override
  public List<CpBlessRecord> getBlessRecord(Long userId, Set<Long> notSendUserIds) {
    return query()
        .eq(CpBlessRecord::getUserId, userId)
        .notIn(CpBlessRecord::getSendUserId, notSendUserIds)
        .orderByDesc(CpBlessRecord::getCreateTime)
        .last(PageConstant.formatLimit(6))
        .list();
  }

  @Override
  public List<CpBlessRecord> pageList(Long userId, Long lastId) {
    return query()
        .eq(CpBlessRecord::getUserId, userId)
        .lt(Objects.nonNull(lastId), CpBlessRecord::getId, lastId)
        .orderByDesc(CpBlessRecord::getId)
        .last(PageConstant.formatLimit(20))
        .list();
  }

  @Override
  public void deleteByCpValId(Long cpValId) {
    delete().eq(CpBlessRecord::getCpValId, cpValId).execute();
  }
}
