package com.red.circle.other.infra.gateway.props;

import com.google.common.collect.Lists;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.mq.business.model.event.task.TaskApprovalEvent;
import com.red.circle.mq.rocket.business.producer.TaskMqMessage;
import com.red.circle.other.domain.gateway.props.PropsStoreGateway;
import com.red.circle.other.domain.gateway.user.UserProfileGateway;
import com.red.circle.other.infra.convertor.material.PropsMaterialInfraConvertor;
import com.red.circle.other.infra.convertor.material.PropsNobleVipInfraConvertor;
import com.red.circle.other.infra.database.cache.service.other.EnumConfigCacheService;
import com.red.circle.other.infra.database.mongo.service.props.StatisticsPropsSaleQuotaService;
import com.red.circle.other.infra.database.rds.entity.props.PropsBackpack;
import com.red.circle.other.infra.database.rds.entity.props.PropsCommodityStore;
import com.red.circle.other.infra.database.rds.entity.props.PropsSourceRecord;
import com.red.circle.other.infra.database.rds.entity.props.RoomThemeUserBackpack;
import com.red.circle.other.infra.database.rds.entity.props.RunningWaterUserProps;
import com.red.circle.other.infra.database.rds.service.props.PropsBackpackService;
import com.red.circle.other.infra.database.rds.service.props.PropsCommodityStoreService;
import com.red.circle.other.infra.database.rds.service.props.PropsNobleVipAbilityService;
import com.red.circle.other.infra.database.rds.service.props.PropsSourceRecordService;
import com.red.circle.other.infra.database.rds.service.props.RoomThemeUserBackpackService;
import com.red.circle.other.infra.database.rds.service.props.RunningWaterUserPropsService;
import com.red.circle.other.infra.database.rds.service.user.user.ConsumptionLevelService;
import com.red.circle.other.infra.gateway.assembly.NobleVipAbilityAssembly;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import com.red.circle.other.inner.model.dto.material.props.NobleVipAbility;
import com.red.circle.other.inner.model.dto.material.props.ProductProps;
import com.red.circle.other.inner.model.dto.material.props.PropsItem;
import com.red.circle.other.inner.model.dto.material.props.PropsNobleVipAbilityDTO;
import com.red.circle.other.inner.model.dto.material.props.PropsPrice;
import com.red.circle.other.inner.model.dto.material.props.PropsResources;
import com.red.circle.other.inner.model.dto.material.props.PropsStoreCommodity;
import com.red.circle.other.inner.model.dto.material.props.UserBackpackProps;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.LocalDateTimeUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.num.ArithmeticUtils;
import com.red.circle.tool.core.text.StringUtils;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 道具仓库 实现.
 *
 * <AUTHOR> on 2021/5/27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PropsStoreGatewayImpl implements PropsStoreGateway {

  private final UserProfileGateway userProfileGateway;
  private final PropsBackpackService propsBackpackService;
  private final EnumConfigCacheService enumConfigCacheService;
  private final ConsumptionLevelService consumptionLevelService;
  private final NobleVipAbilityAssembly nobleVipAbilityAssembly;
  private final PropsSourceRecordService propsSourceRecordService;
  private final PropsCommodityStoreService propsCommodityStoreService;
  private final PropsNobleVipAbilityService propsNobleVipAbilityService;
  private final PropsMaterialInfraConvertor propsMaterialInfraConvertor;
  private final RunningWaterUserPropsService runningWaterUserPropsService;
  private final RoomThemeUserBackpackService roomThemeUserBackpackService;
  private final StatisticsPropsSaleQuotaService statisticsPropsSaleQuotaService;
  private final PropsNobleVipInfraConvertor propsNobleVipInfraConvertor;
  private final TaskMqMessage taskMqMessage;

  @Override
  public UserBackpackProps randomGiveawayCheapestProps(Long userId, Integer days) {
    PropsSourceRecord propsSourceRecord = propsSourceRecordService.getCheapAvatarFrame();
    return shippingUserBackpackProps(userId, days, propsSourceRecord);
  }

  public UserBackpackProps randomGiveawayCheapestPropsById(Long userId, Integer days, Long propsId) {
    PropsSourceRecord propsSourceRecord = propsSourceRecordService.getPropsById(propsId);

    return shippingUserBackpackProps(userId, days, propsSourceRecord);
  }

  private UserBackpackProps shippingUserBackpackProps(Long userId, Integer days, PropsSourceRecord propsSourceRecord) {
    shippingProductProps(new ProductProps()
            .setInitiateUserId(userId)
            .setDays(days)
            .setProductId(propsSourceRecord.getId())
            .setPropsOrigin("REGISTER_REWARDS")
            .setPropsOriginDesc("Register reward")
            .setPropsResources(propsMaterialInfraConvertor.toPropsSources(propsSourceRecord))
            .setPropsPrices(BigDecimal.ZERO)
    );

    return new UserBackpackProps()
            .setUserId(userId)
            .setType(propsSourceRecord.getType())
            .setPropsResources(propsMaterialInfraConvertor.toPropsSources(propsSourceRecord))
            .setExpireTime(TimestampUtils.nowPlusDays(days))
            .setUseProps(Boolean.FALSE);
  }

  @Override
  public PropsResources getPropsResourcesById(Long id) {
    return propsMaterialInfraConvertor.toPropsSources(propsSourceRecordService.getById(id));
  }

  @Override
  public PropsStoreCommodity getStoreCommodity(Long id) {
    PropsCommodityStore commodityStore = propsCommodityStoreService.getById(id);
    if (Objects.isNull(commodityStore)) {
      return null;
    }

    if (PropsCommodityType.NOBLE_VIP.eq(commodityStore.getPropsType())) {
      return assemblyNobleVipCommodity(List.of(commodityStore)).get(0);
    }

    return assemblyCommodity(commodityStore);
  }

  private PropsStoreCommodity assemblyCommodity(PropsCommodityStore commodityStore) {
    PropsSourceRecord propsSourceRecord = propsSourceRecordService
        .getById(commodityStore.getSourceId());

    if (Objects.isNull(propsSourceRecord)) {
      log.warn("【道具商店】数据异常，商品:{} 对应资源未找到.", commodityStore.getId());
      return null;
    }
    PropsStoreCommodity commodity = propsMaterialInfraConvertor.toPropsStoreCommodity(
        commodityStore);
    commodity.setPropsResources(toPropsResources(propsSourceRecord));
    commodity.setPropsPrices(
        PropsPrice.of(commodity.getValidDays(), propsSourceRecord.getAmount()));
    commodity.setDiamondPrices(PropsPrice.of(commodity.getValidDays(),
        enumConfigCacheService
            .goldsToDiamondQuantity(propsSourceRecord.getAmount(),
                propsSourceRecord.getSysOrigin())));
    return commodity;
  }

  @Override
  public List<PropsStoreCommodity> listReleaseStoreCommodity(SysOriginPlatformEnum sysOrigin,
      PropsCommodityType propsType) {
    List<PropsCommodityStore> commodityStores = propsCommodityStoreService
        .listSysOriginRelease(sysOrigin, propsType.name());

    if (CollectionUtils.isEmpty(commodityStores)) {
      return Lists.newArrayList();
    }

    if (Objects.equals(propsType, PropsCommodityType.NOBLE_VIP)) {
      return assemblyNobleVipCommodity(commodityStores);
    }

    return assemblyCommodities(commodityStores);
  }

  private List<PropsStoreCommodity> assemblyCommodities(List<PropsCommodityStore> commodityStores) {
    Map<Long, PropsSourceRecord> sourceRecordMap = getSourceRecordMap(
        commodityStores.stream().map(PropsCommodityStore::getSourceId)
            .collect(Collectors.toSet()));

    if (CollectionUtils.isEmpty(sourceRecordMap)) {
      return Lists.newArrayList();
    }

    return commodityStores.stream().map(commodity -> {
      PropsStoreCommodity propsStoreCommodity = propsMaterialInfraConvertor
          .toPropsStoreCommodity(commodity);
      PropsSourceRecord propsSourceRecord = sourceRecordMap.get(commodity.getSourceId());
      if (Objects.isNull(propsSourceRecord)) {
        log.warn("【道具商店】数据异常，商品:{} 对应资源未找到.", commodity.getSourceId());
        return null;
      }
      propsStoreCommodity.setPropsResources(toPropsResources(propsSourceRecord));
      propsStoreCommodity.setPropsPrices(
          PropsPrice.of(commodity.getValidDays(), propsSourceRecord.getAmount()));
      propsStoreCommodity.setDiamondPrices(Lists.newArrayList());
      return propsStoreCommodity;
    }).filter(Objects::nonNull).collect(Collectors.toList());
  }

  private Map<Long, PropsSourceRecord> getSourceRecordMap(Set<Long> longStream) {
    return propsSourceRecordService.mapByIds(longStream);
  }


  private List<PropsStoreCommodity> assemblyNobleVipCommodity(
      List<PropsCommodityStore> commodityStores) {

    // 商品资源信息
    Map<Long, PropsSourceRecord> commodityStoreMap = propsSourceRecordService
        .mapByIds(commodityStores.stream().map(PropsCommodityStore::getSourceId)
            .collect(Collectors.toSet()));

    // 贵族能力
    Map<Long, NobleVipAbility> nobleVipAbilityMap = nobleVipAbilityAssembly.mapAbility(
        commodityStores.stream().map(PropsCommodityStore::getSourceId).collect(
            Collectors.toSet()));

    return commodityStores.stream().map(store -> {
      PropsNobleVipAbilityDTO propsNobleVipAbilityDTO = nobleVipAbilityMap.get(store.getSourceId()).getNobleVipAbility();
      PropsSourceRecord commoditySource = commodityStoreMap.get(store.getSourceId());
      PropsStoreCommodity commodity = propsMaterialInfraConvertor.toPropsStoreCommodity(store);
      if (Objects.nonNull(propsNobleVipAbilityDTO)){
        NobleVipAbility nobleVipAbility =  nobleVipAbilityMap.get(store.getSourceId());
        commodity.setNobleVipAbility(nobleVipAbility);
        commodity.getNobleVipAbility().setNobleVipAbility(propsNobleVipAbilityDTO);
        commodity.setPropsAbility(nobleVipAbility);
      }
      commodity.setPropsPrices(PropsPrice
          .of(commodity.getValidDays(), commoditySource.getAmount(), commodity.getDiscount()));
      commodity.setPropsResources(toPropsResources(commoditySource));
      return commodity;
    }).toList();
  }

  @Override
  public List<UserBackpackProps> listUserPropsBackpack(Long userId, PropsCommodityType propsType) {

    List<PropsBackpack> propBackpacks = propsBackpackService
        .listNotExpiredByUserIdAndType(userId, propsType);

    if (CollectionUtils.isEmpty(propBackpacks)) {
      return Lists.newArrayList();
    }

    if (Objects.equals(propsType, PropsCommodityType.AVATAR_FRAME)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.RIDE)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.LAYOUT)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.THEME)) {
      //整合主题两个接口数据
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.NOBLE_VIP)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.CHAT_BUBBLE)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.FLOAT_PICTURE)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.DATA_CARD)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    if (Objects.equals(propsType, PropsCommodityType.RED_PACKET)) {
      return listUserPropsBackpack(propBackpacks, getPropsId(propBackpacks));
    }

    throw new IllegalArgumentException("propsType Not found.");
  }

  @Override
  public Map<Long, List<UserBackpackProps>> mapUserUsePropsByIdTypes(Set<Long> userIds,
      List<PropsCommodityType> propsTypes) {
    return listUserUsePropsByIdTypes(userIds, propsTypes).stream()
        .collect(Collectors.groupingBy(UserBackpackProps::getUserId));
  }

  @Override
  public List<UserBackpackProps> listUserUsePropsByIdTypes(Set<Long> userIds,
      List<PropsCommodityType> types) {
    return assemblyUserUseNobleAndSpecialId(propsBackpackService
        .listNotExpiredUseProps(userIds, types));
  }

  private List<UserBackpackProps> assemblyUserUseNobleAndSpecialId(
      List<PropsBackpack> propsBackpacks) {
    if (CollectionUtils.isEmpty(propsBackpacks)) {
      return Lists.newArrayList();
    }
    Map<Long, PropsSourceRecord> nobleVipConfigMap = propsSourceRecordService
        .mapByIds(filterPropsIds(propsBackpacks, PropsCommodityType.NOBLE_VIP));

    return propsBackpacks.stream().map(propsBackpack -> {
      UserBackpackProps userBackpackProps = propsMaterialInfraConvertor.toUserBackpackProps(
          propsBackpack);
      if (PropsCommodityType.NOBLE_VIP.eq(propsBackpack.getType())) {
        userBackpackProps.setPropsResources(
            propsMaterialInfraConvertor.toPropsSources(
                nobleVipConfigMap.get(propsBackpack.getPropsId())));
        return userBackpackProps;
      }
      return null;
    }).filter(Objects::nonNull).collect(Collectors.toList());
  }

  @Override
  public List<UserBackpackProps> listUserUseProps(Long userId) {
    List<PropsBackpack> propsBackpacks = propsBackpackService
        .listNotExpiredUseProps(userId);

    if (CollectionUtils.isEmpty(propsBackpacks)) {
      return Lists.newArrayList();
    }

    Map<Long, PropsSourceRecord> propsSourceRecordMap = propsSourceRecordService
        .mapByIds(getSetPropsId(propsBackpacks,
            propsBackpack -> !PropsCommodityType.SPECIAL_ID.eq(propsBackpack.getType())));

    return propsBackpacks.stream().map(propsBackpack -> {
      UserBackpackProps userBackpackProps = propsMaterialInfraConvertor.toUserBackpackProps(
          propsBackpack);

      PropsSourceRecord propsSourceRecord = propsSourceRecordMap.get(propsBackpack.getPropsId());
      if (Objects.isNull(propsSourceRecord)) {
        log.warn("【道具】listUserUseProps数据错误：{},{}", userId, propsBackpack.getPropsId());
        return null;
      }
      userBackpackProps.setPropsResources(
          propsMaterialInfraConvertor.toPropsSources(propsSourceRecord));
      return userBackpackProps;
    }).filter(Objects::nonNull).collect(Collectors.toList());
  }

  @Override
  public NobleVipAbility getNobleVipAbility(Long sourceId) {
    return propsNobleVipInfraConvertor.toNobleVipAbility(propsNobleVipAbilityService.getById(sourceId));
  }

  private Set<Long> getSetPropsId(List<PropsBackpack> propsBackpacks,
      Predicate<PropsBackpack> predicate) {
    return propsBackpacks.stream().filter(predicate)
        .map(PropsBackpack::getPropsId).collect(Collectors.toSet());
  }

  private List<UserBackpackProps> listUserPropsBackpack(List<PropsBackpack> propBackpacks,
      List<Long> sourceIds) {
    Map<Long, PropsSourceRecord> sourceRecordMap = propsSourceRecordService
        .mapByIds(toSet(sourceIds));
    return propBackpacks.stream().map(propsBackpack -> {
      UserBackpackProps userBackpackProps = propsMaterialInfraConvertor.toUserBackpackProps(
          propsBackpack);
      PropsSourceRecord propsSourceRecord = sourceRecordMap.get(propsBackpack.getPropsId());
      if (Objects.isNull(userBackpackProps) || Objects.isNull(propsSourceRecord)) {
        return null;
      }
      userBackpackProps.setPropsResources(toPropsResources(propsSourceRecord));
      return userBackpackProps;
    }).filter(Objects::nonNull).collect(Collectors.toList());
  }

  private PropsResources toPropsResources(PropsSourceRecord propsSourceRecord) {
    propsSourceRecord.setSourceUrl(StringUtils.isBlank(propsSourceRecord.getSourceUrl()) ? ""
        : propsSourceRecord.getSourceUrl());
    return propsMaterialInfraConvertor.toPropsSources(propsSourceRecord);
  }

  private Set<Long> toSet(List<Long> ids) {
    return new HashSet<>(ids);
  }

  private List<Long> getPropsId(List<PropsBackpack> propBackpacks) {
    return propBackpacks.stream().map(PropsBackpack::getPropsId).distinct()
        .collect(Collectors.toList());
  }

  private boolean saveOrUpdateThemeBackpack(PropsItem backpack) {
    RoomThemeUserBackpack themeBackpack = roomThemeUserBackpackService.query()
        .eq(RoomThemeUserBackpack::getUserId, backpack.getUserId())
        .eq(RoomThemeUserBackpack::getThemeId, backpack.getPropsId())
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(themeBackpack)) {
      return roomThemeUserBackpackService.save(new RoomThemeUserBackpack()
          .setThemeId(backpack.getPropsId())
          .setUseTheme(Boolean.FALSE)
          .setThemeType(0)
          .setUserId(backpack.getUserId())
          .setExpireTime(isFree(backpack) ? TimestampUtils.nowPlusYear(10).getTime()
              : backpack.thisExpireTime().getTime())
      );
    }

    if (isFree(backpack)) {
      themeBackpack.setExpireTime(TimestampUtils.nowPlusYear(10).getTime());
      return roomThemeUserBackpackService.updateSelectiveById(themeBackpack);
    }

    themeBackpack.setExpireTime(
        backpack.appendExpiredTime(new Timestamp(themeBackpack.getExpireTime())).getTime());
    return roomThemeUserBackpackService.updateSelectiveById(themeBackpack);
  }

  private boolean isFree(PropsItem backpack) {
    return Objects.equals(backpack.getValidDays(), 9999);
  }

  private boolean saveOrUpdatePropsBackpack(PropsItem backpack) {

    propsBackpackService
        .unloadUseProps(backpack.getUserId(),
            PropsCommodityType.valueOf(backpack.getType().name()));

    PropsSourceRecord propsSourceRecord = propsSourceRecordService.getPropsById(
        backpack.getPropsId());
    if (Objects.isNull(propsSourceRecord)) {
      return false;
    }

    PropsBackpack propsBackpack = propsBackpackService.query()
        .eq(PropsBackpack::getUserId, backpack.getUserId())
        .eq(PropsBackpack::getPropsId, backpack.getPropsId())
        .eq(PropsBackpack::getType, backpack.getType())
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(propsBackpack)) {
      PropsBackpack buildBackPack = new PropsBackpack()
          .setUserId(backpack.getUserId())
          .setPropsId(backpack.getPropsId())
          .setType(backpack.getTypeName())
          .setUseProps(Boolean.TRUE)
          .setAllowGive(Boolean.FALSE)
          .setExpireTime(backpack.thisExpireTime());
      propsBackpackService.save(buildBackPack);
      userProfileGateway.switchUseProps(backpack.getUserId(), backpack.getPropsId());
      return false;
    }

    propsBackpack.setUseProps(Boolean.TRUE);
    Long milliSecond = TimestampUtils.expiredIntervalMillisecond(propsBackpack.getExpireTime(),
        TimestampUtils.now());
    propsBackpack.setExpireTime(backpack.appendExpiredTime(propsBackpack.getExpireTime()));

    if (milliSecond < 0) {
      propsBackpack.setAllowGive(propsBackpack.getAllowGive());
    } else {
      propsBackpack.setAllowGive(Boolean.FALSE);
    }
    propsBackpackService.updateSelectiveById(propsBackpack);
    userProfileGateway.switchUseProps(backpack.getUserId(), backpack.getPropsId());
    return false;
  }

  @Override
  public void shippingProductProps(ProductProps productProps) {
    // 默认没写的免费
    if (Objects.isNull(productProps.getFree())) {
      productProps.setFree(Boolean.TRUE);
    }

    // 插入背包
    addPropsBackpack(productProps);

    // 添加流水
    runningWaterUserPropsService.save(new RunningWaterUserProps()
        .setUserId(productProps.finallyAcceptedUserId())
        .setPropsId(productProps.propsResourcesId())
        .setPropsCandy(productProps.getPropsPrices())
        .setOrigin(productProps.getPropsOrigin())
        .setOriginDesc(productProps.getPropsOriginDesc())
        .setType(productProps.propsResourcesTypeName())
        .withCreateUser(productProps.getInitiateUserId()));

    // 累计财富等级
    if (ArithmeticUtils.gtZero(productProps.getPropsPrices())
        && Objects.equals(productProps.getAppendWealth(), Boolean.TRUE)) {
      consumptionLevelService.incrConsumptionGolds(productProps.getInitiateUserId(),
          productProps.getPropsPrices());
      //累计财富等级mq
      taskMqMessage.sendTask(TaskApprovalEvent.builder()
              .taskId(13)
              .userId(productProps.getInitiateUserId())
              .day(LocalDateTimeUtils.nowFormat("yyyy-MM-dd"))
              .build());

    }

    // 统计销售情况
    if (Objects.nonNull(productProps.getCountSysOrigin())) {
      statisticsPropsSaleQuotaService.incSaleQuota(
          SysOriginPlatformEnum.valueOf(productProps.getCountSysOrigin()),
          productProps.propsResourcesTypeName(),
          productProps.propsResourcesId());
    }

  }

  private Set<Long> filterPropsIds(List<PropsBackpack> propsBackpacks,
      PropsCommodityType propsType) {
    return propsBackpacks.stream()
        .filter(propsBackpack -> propsType.eq(propsBackpack.getType()))
        .map(PropsBackpack::getPropsId)
        .collect(Collectors.toSet());
  }

  private void addPropsBackpack(ProductProps productProps) {
    if (Objects.equals(productProps.propsResourcesType(), PropsCommodityType.THEME)) {
      saveOrUpdateThemeBackpack(new PropsItem()
          .setType(productProps.propsResourcesType())
          .setUserId(productProps.finallyAcceptedUserId())
          .setPropsId(productProps.propsResourcesId())
          .setValidDays(Objects.equals(productProps.getFree(), Boolean.TRUE)
              ? 9999 : productProps.getDays())
      );
      return;
    }
    saveOrUpdatePropsBackpack(new PropsItem()
        .setType(productProps.propsResourcesType())
        .setUserId(productProps.finallyAcceptedUserId())
        .setPropsId(productProps.propsResourcesId())
        .setValidDays(productProps.getDays())
    );
  }

}

