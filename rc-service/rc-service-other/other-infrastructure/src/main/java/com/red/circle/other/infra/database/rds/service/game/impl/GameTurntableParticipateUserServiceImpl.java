package com.red.circle.other.infra.database.rds.service.game.impl;

import com.google.common.collect.Lists;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameTurntableParticipateUserDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameTurntableParticipateUser;
import com.red.circle.other.infra.database.rds.service.game.GameTurntableParticipateUserService;
import java.util.List;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 游戏转盘参与用户 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-20
 */
@Service
@RequiredArgsConstructor
public class GameTurntableParticipateUserServiceImpl extends
    BaseServiceImpl<GameTurntableParticipateUserDAO, GameTurntableParticipateUser> implements
    GameTurntableParticipateUserService {

  private final GameTurntableParticipateUserDAO gameTurntableParticipateUserDAO;

  @Override
  public List<GameTurntableParticipateUser> listParticipateUser(Long gameId) {
    return query()
        .eq(GameTurntableParticipateUser::getGameId, gameId)
        .last(PageConstant.formatLimit(12))
        .list();
  }


  @Override
  public List<GameTurntableParticipateUser> listTop20BySysOrigin(String sysOrigin) {
    return Optional.ofNullable(gameTurntableParticipateUserDAO.listTop20BySysOrigin(sysOrigin))
        .orElse(Lists.newArrayList());
  }
}
