package com.red.circle.other.infra.database.rds.service.live;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.live.RoomMember;
import com.red.circle.other.inner.enums.live.RoomUserRolesEnum;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 成员列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-01
 */
public interface RoomMemberService extends BaseService<RoomMember> {

  boolean exists(Long roomId, Long userId);

  RoomMember getByUserId(Long userId);

  RoomMember getRoomMember(Long roomId, Long userId);

  Map<Long, RoomUserRolesEnum> mapMemberRoles(Long roomId, Set<Long> userIds);

  List<RoomMember> listMember(Long roomId, Integer pageSize, Long lastId);

  List<RoomMember> listHomeownerAndManager(Long roomId);

  List<RoomMember> listRoomIdExcludeHomeownerByUserIdFlow(Long userId, Long lastId);

  RoomUserRolesEnum getRoomUserRoles(Long userId, Long roomId);

  Boolean validJoined(Long userId, Long roomId);

  boolean removeMember(Long roomId, Long userId);

  boolean removeMemberById(Long id);

  /**
   * 修改成员角色.
   */
  void updateRole(Long id, String role, Long updateUser);

}
