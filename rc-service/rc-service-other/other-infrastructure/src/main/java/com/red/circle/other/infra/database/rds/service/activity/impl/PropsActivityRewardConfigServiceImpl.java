package com.red.circle.other.infra.database.rds.service.activity.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.PropsActivityRewardConfigDAO;
import com.red.circle.other.infra.database.rds.entity.activity.PropsActivityRewardConfig;
import com.red.circle.other.infra.database.rds.service.activity.PropsActivityRewardConfigService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动道具奖励配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-07
 */
@Service
@RequiredArgsConstructor
public class PropsActivityRewardConfigServiceImpl extends
    BaseServiceImpl<PropsActivityRewardConfigDAO, PropsActivityRewardConfig> implements
    PropsActivityRewardConfigService {

  public Map<Long, List<PropsActivityRewardConfig>> mapGroupByIds(Set<Long> groupIds) {
    return CollectionUtils.isEmpty(groupIds)
        ? CollectionUtils.newHashMap()
        : Optional.ofNullable(query()
                .in(PropsActivityRewardConfig::getGroupId, groupIds)
                .orderByAsc(PropsActivityRewardConfig::getSort)
                .list())
            .map(propsActivityRewardConfigs -> propsActivityRewardConfigs.stream().collect(
                Collectors.groupingBy(PropsActivityRewardConfig::getGroupId)))
            .orElseGet(CollectionUtils::newHashMap);
  }

  @Override
  public Map<String, List<PropsActivityRewardConfig>> mapGroupTypeById(Long groupId) {
    return Optional.ofNullable(
            query().eq(PropsActivityRewardConfig::getGroupId, groupId).list()
        ).map(cnf -> cnf.stream()
            .collect(Collectors.groupingBy(PropsActivityRewardConfig::getType)))
        .orElseGet(CollectionUtils::newHashMap);
  }


  @Override
  public List<PropsActivityRewardConfig> listByGroupId(Long groupId) {
    return Optional.ofNullable(
        query()
            .eq(PropsActivityRewardConfig::getGroupId, groupId)
            .orderByAsc(PropsActivityRewardConfig::getSort)
            .list()
    ).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public List<PropsActivityRewardConfig> listByGroupIdType(Long groupId, String type) {
    return Optional.ofNullable(
        query()
            .eq(PropsActivityRewardConfig::getGroupId, groupId)
            .eq(PropsActivityRewardConfig::getType, type)
            .orderByAsc(PropsActivityRewardConfig::getSort)
            .list()
    ).orElse(CollectionUtils.newArrayList());
  }

  @Override
  public List<PropsActivityRewardConfig> listByGroupIds(List<Long> groupIds) {
    return query()
        .in(PropsActivityRewardConfig::getGroupId, groupIds).list();
  }

  @Override
  public void deleteByGroupId(Long groupId) {
    if (Objects.isNull(groupId)) {
      return;
    }
    delete().eq(PropsActivityRewardConfig::getGroupId, groupId).execute();
  }


}
