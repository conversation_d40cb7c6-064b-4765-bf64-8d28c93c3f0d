package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysUserSpecialIdLogDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysUserSpecialIdLog;
import com.red.circle.other.infra.database.rds.service.sys.SysUserSpecialIdLogService;
import com.red.circle.other.inner.model.cmd.sys.PageUserSpecialIdCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 靓号使用日志记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-16
 */
@Service
public class SysUserSpecialIdLogServiceImpl extends
    BaseServiceImpl<SysUserSpecialIdLogDAO, SysUserSpecialIdLog> implements
    SysUserSpecialIdLogService {

  @Override
  public List<SysUserSpecialIdLog> listLatest(Long userId, Integer size) {
    return query()
        .eq(SysUserSpecialIdLog::getUserId, userId)
        .last("LIMIT " + size)
        .orderByDesc(SysUserSpecialIdLog::getId)
        .list();
  }

  @Override
  public PageResult<SysUserSpecialIdLog> pageSpecialIdLog(PageUserSpecialIdCmd param) {
    return query()
        .eq(Objects.nonNull(param.getUserId()), SysUserSpecialIdLog::getUserId, param.getUserId())
        .eq(StringUtils.isNotBlank(param.getSysOrigin()), SysUserSpecialIdLog::getSysOrigin,
            param.getSysOrigin())
        .orderByDesc(SysUserSpecialIdLog::getCreateTime)
        .page(param.getPageQuery());
  }
}
