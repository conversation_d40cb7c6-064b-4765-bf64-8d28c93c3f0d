package com.red.circle.other.infra.database.rds.service.team.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.team.BillDiamondBalanceDAO;
import com.red.circle.other.infra.database.rds.entity.team.BillDiamondBalance;
import com.red.circle.other.infra.database.rds.entity.team.BusinessDevelopmentBaseInfo;
import com.red.circle.other.infra.database.rds.entity.team.BusinessDevelopmentTeam;
import com.red.circle.other.infra.database.rds.service.team.BillDiamondBalanceService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import com.red.circle.tool.core.tuple.PennyAmount;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 用户账单钻石余额 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2024-03-19 18:08
 */
@Service
public class BillDiamondBalanceServiceImpl extends
    BaseServiceImpl<BillDiamondBalanceDAO, BillDiamondBalance> implements
    BillDiamondBalanceService {

    @Override
    public BillDiamondBalance getEntityByBillBelongAndUserId(String sysOrigin, Integer billBelong, Long userId) {
        return query()
                .eq(BillDiamondBalance::getSysOrigin, sysOrigin)
                .eq(BillDiamondBalance::getUserId, userId)
                .eq(BillDiamondBalance::getBillBelong, billBelong)
                .last(PageConstant.LIMIT_ONE)
                .getOne();
    }

    @Override
    public List<BillDiamondBalance> getEntityByBillBelongAndTeamId(String sysOrigin, Integer billBelong, Long teamId) {
        return query()
                .eq(BillDiamondBalance::getSysOrigin, sysOrigin)
                .eq(BillDiamondBalance::getTeamId, teamId)
                .eq(BillDiamondBalance::getBillBelong, billBelong)
                .list();
    }

    @Override
    public void add(BillDiamondBalance param) {
        BillDiamondBalance billDiamondBalance = this.getEntityByBillBelongAndUserId(param.getSysOrigin(), param.getBillBelong(), param.getUserId());
        if (Objects.isNull(billDiamondBalance)) {
            param.setId(IdWorkerUtils.getId());
            param.setCreateTime(TimestampUtils.now());
            param.setCreateUser(0L);
            save(param);
        } else {
            if (param.getEarnPoints().compareTo(billDiamondBalance.getEarnPoints()) > 0) {
                billDiamondBalance.setUpdateTime(TimestampUtils.now());
                billDiamondBalance.setUpdateUser(0L);
                billDiamondBalance.setBalance(param.getBalance().subtract(billDiamondBalance.getConsumptionPoints()));
                billDiamondBalance.setEarnPoints(param.getEarnPoints());
                saveOrUpdate(billDiamondBalance);
            }
        }
    }

    @Override
    public void addAgent(BillDiamondBalance param) {
        BillDiamondBalance billDiamondBalance = this.getEntityByBillBelongAndUserId(param.getSysOrigin(), param.getBillBelong(), param.getUserId());
        if (Objects.isNull(billDiamondBalance)) {
            param.setId(IdWorkerUtils.getId());
            param.setCreateTime(TimestampUtils.now());
            param.setCreateUser(0L);
            save(param);
        } else {
            if (param.getAgentEarnPoints().compareTo(billDiamondBalance.getAgentEarnPoints()) > 0) {
                billDiamondBalance.setUpdateTime(TimestampUtils.now());
                billDiamondBalance.setUpdateUser(0L);
                billDiamondBalance.setAgentBalance(param.getAgentBalance().subtract(billDiamondBalance.getAgentConsumptionPoints()));
                billDiamondBalance.setAgentEarnPoints(param.getAgentEarnPoints());
                saveOrUpdate(billDiamondBalance);
            }
        }
    }

    @Override
    public void addBalance(BillDiamondBalance param) {
        BillDiamondBalance billDiamondBalance = this.getEntityByBillBelongAndUserId(param.getSysOrigin(), param.getBillBelong(), param.getUserId());
        if (Objects.isNull(billDiamondBalance)) {
            param.setId(IdWorkerUtils.getId());
            param.setCreateTime(TimestampUtils.now());
            param.setCreateUser(0L);
            save(param);
        } else {
            if (param.getEarnPoints().compareTo(billDiamondBalance.getEarnPoints()) > 0) {
                billDiamondBalance.setUpdateTime(TimestampUtils.now());
                billDiamondBalance.setUpdateUser(0L);
                billDiamondBalance.setBalance(param.getBalance());
                billDiamondBalance.setEarnPoints(param.getEarnPoints());
                billDiamondBalance.setConsumptionPoints(BigDecimal.ZERO);
                saveOrUpdate(billDiamondBalance);
            }
        }
    }

    @Override
    public void addAgentBalance(BillDiamondBalance param) {
        BillDiamondBalance billDiamondBalance = this.getEntityByBillBelongAndUserId(param.getSysOrigin(), param.getBillBelong(), param.getUserId());
        if (Objects.isNull(billDiamondBalance)) {
            param.setId(IdWorkerUtils.getId());
            param.setCreateTime(TimestampUtils.now());
            param.setCreateUser(0L);
            save(param);
        } else {
            if (param.getAgentEarnPoints().compareTo(billDiamondBalance.getAgentEarnPoints()) > 0) {
                billDiamondBalance.setUpdateTime(TimestampUtils.now());
                billDiamondBalance.setUpdateUser(0L);
                billDiamondBalance.setAgentBalance(param.getAgentBalance());
                billDiamondBalance.setAgentEarnPoints(param.getAgentEarnPoints());
                billDiamondBalance.setAgentConsumptionPoints(BigDecimal.ZERO);
                saveOrUpdate(billDiamondBalance);
            }
        }
    }

    @Override
    public BillDiamondBalance getBalance(Long userId, Integer billBelong) {
        return query()
                .eq(BillDiamondBalance::getUserId, userId)
                .eq(BillDiamondBalance::getBillBelong, billBelong)
                .getOne();
    }

    @Override
    public boolean exists(Long userId, Integer billBelong) {
        return Optional.ofNullable(
                        query()
                                .eq(BillDiamondBalance::getUserId, userId)
                                .eq(BillDiamondBalance::getBillBelong, billBelong)
                                .last(PageConstant.LIMIT_ONE).getOne())
                .map(BillDiamondBalance -> Objects.nonNull(BillDiamondBalance.getId()))
                .orElse(Boolean.FALSE);
    }

    @Override
    public BillDiamondBalance decr(Long userId, BigDecimal amount, Integer billBelong) {
        if (exists(userId, billBelong)) {
            update()
                    .setSql("consumption_points=consumption_points+" + amount)
                    .setSql("balance=balance-" + amount)
                    .set(BillDiamondBalance::getUpdateUser, userId)
                    .set(BillDiamondBalance::getUpdateTime, TimestampUtils.now())
                    .eq(BillDiamondBalance::getUserId, userId)
                    .eq(BillDiamondBalance::getBillBelong, billBelong)
                    .last(PageConstant.LIMIT_ONE)
                    .execute();
        }
        return getBalance(userId, billBelong);
    }

    @Override
    public BillDiamondBalance decrAgent(Long userId, BigDecimal amount, Integer billBelong) {
        if (exists(userId, billBelong)) {
            update()
                    .setSql("agent_consumption_points=agent_consumption_points+" + amount)
                    .setSql("agent_balance=agent_balance-" + amount)
                    .set(BillDiamondBalance::getUpdateUser, userId)
                    .set(BillDiamondBalance::getUpdateTime, TimestampUtils.now())
                    .eq(BillDiamondBalance::getUserId, userId)
                    .eq(BillDiamondBalance::getBillBelong, billBelong)
                    .last(PageConstant.LIMIT_ONE)
                    .execute();
        }
        return getBalance(userId, billBelong);
    }

    @Override
    public void removeBatchByTeamIds(List<Long> teamIds, Integer billBelong) {
        if (CollectionUtils.isEmpty(teamIds)) {
            return;
        }
        delete()
                .eq(BillDiamondBalance::getBillBelong, billBelong)
                .in(BillDiamondBalance::getTeamId, teamIds)
                .execute();
    }

    @Override
    public void removeByUserId(Long userId, Integer billBelong) {
        delete()
                .eq(BillDiamondBalance::getBillBelong, billBelong)
                .eq(BillDiamondBalance::getUserId, userId)
                .execute();
    }


}
