package com.red.circle.other.infra.database.rds.service.team.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.team.UserHistoryIdentityDAO;
import com.red.circle.other.infra.database.rds.entity.team.UserHistoryIdentity;
import com.red.circle.other.infra.database.rds.service.team.UserHistoryIdentityService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

/**
 * 用户历史身份 服务实现.
 *
 * <AUTHOR> on 2023/8/31
 */
@Service
public class UserHistoryIdentityServiceImpl extends
    BaseServiceImpl<UserHistoryIdentityDAO, UserHistoryIdentity> implements
    UserHistoryIdentityService {

  @Override
  public void saveBd(Long userId) {
    if (exists(userId)) {
      update()
          .set(UserHistoryIdentity::getBd, Boolean.TRUE)
          .eq(UserHistoryIdentity::getId, userId)
          .execute();
      return;
    }

    try {
      save(new UserHistoryIdentity()
          .setId(userId)
          .setBd(Boolean.TRUE)
          .setAgent(Boolean.FALSE)
          .setHost(Boolean.FALSE)
      );
    } catch (DuplicateKeyException ex) {
      // ignore
    }
  }

  @Override
  public void saveHost(Long userId) {
    if (exists(userId)) {
      update()
          .set(UserHistoryIdentity::getHost, Boolean.TRUE)
          .eq(UserHistoryIdentity::getId, userId)
          .execute();
      return;
    }
    try {
      save(new UserHistoryIdentity()
          .setId(userId)
          .setBd(Boolean.FALSE)
          .setAgent(Boolean.FALSE)
          .setHost(Boolean.TRUE)
      );
    } catch (DuplicateKeyException ex) {
      // ignore
    }

  }

  @Override
  public void saveAgentAndHost(Long userId) {
    if (exists(userId)) {
      update()
          .set(UserHistoryIdentity::getHost, Boolean.TRUE)
          .set(UserHistoryIdentity::getAgent, Boolean.TRUE)
          .eq(UserHistoryIdentity::getId, userId)
          .execute();
      return;
    }
    try {
      save(new UserHistoryIdentity()
          .setId(userId)
          .setBd(Boolean.FALSE)
          .setAgent(Boolean.TRUE)
          .setHost(Boolean.TRUE)
      );
    } catch (DuplicateKeyException ex) {
      // ignore
    }
  }

  private boolean exists(Long userId) {
    if (Objects.isNull(userId)) {
      return false;
    }

    return Optional.ofNullable(
        query().select(UserHistoryIdentity::getId).eq(UserHistoryIdentity::getId, userId)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).map(identity -> Objects.nonNull(identity.getId())).orElse(false);
  }

  @Override
  public Map<Long, UserHistoryIdentity> mapByIds(Set<Long> userIds) {
    if (CollectionUtils.isEmpty(userIds)) {
      return CollectionUtils.newHashMap();
    }
    List<UserHistoryIdentity> userHistoryIdentities = query().in(UserHistoryIdentity::getId,
        userIds).list();

    if (CollectionUtils.isEmpty(userHistoryIdentities)) {
      return CollectionUtils.newHashMap();
    }
    return userHistoryIdentities.stream()
        .collect(Collectors.toMap(UserHistoryIdentity::getId, v -> v));
  }


  @Override
  public List<String> getLabels(Long userId) {
    UserHistoryIdentity identity = getById(userId);
    return getLabels(identity);
  }

  @Override
  public List<String> getLabels(UserHistoryIdentity identity) {
    if (Objects.isNull(identity)) {
      return Lists.newArrayList();
    }
    List<String> res = Lists.newArrayList();

    if (Objects.equals(identity.getBd(), Boolean.TRUE)) {
      res.add("Bd");
    }

    if (Objects.equals(identity.getAgent(), Boolean.TRUE)) {
      res.add("Agent");
    }

    if (Objects.equals(identity.getHost(), Boolean.TRUE)) {
      res.add("Host");
    }
    return res;
  }

}
