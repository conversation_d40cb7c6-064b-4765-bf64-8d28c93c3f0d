package com.red.circle.other.infra.database.rds.enums.pet;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 解锁条件.
 *
 * <AUTHOR> on 2021/10/19
 */
public enum PetUnlockConditionEnum {

  /**
   * 大于.
   */
  GT,

  /**
   * 大于等于.
   */
  GE,

  /**
   * 等于.
   */
  EQ,

  /**
   * 小于.
   */
  LT,

  /**
   * 小于等于.
   */
  LE;

  public static boolean isGe(String uint) {
    return Objects.equals(uint, PetUnlockConditionEnum.EQ.name())
        || Objects.equals(uint, PetUnlockConditionEnum.GE.name())
        || Objects.equals(uint, PetUnlockConditionEnum.GT.name());
  }

  public boolean is(BigDecimal source, BigDecimal target) {
    if (Objects.isNull(source)) {
      return Boolean.FALSE;
    }

    if (Objects.isNull(target)) {
      return Boolean.TRUE;
    }

    if (this.equals(PetUnlockConditionEnum.GT)) {
      return source.compareTo(target) > 0;
    }

    if (this.equals(PetUnlockConditionEnum.GE)) {
      return source.compareTo(target) >= 0;
    }

    if (this.equals(PetUnlockConditionEnum.EQ)) {
      return source.compareTo(target) == 0;
    }

    if (this.equals(PetUnlockConditionEnum.LT)) {
      return source.compareTo(target) < 0;
    }

    if (this.equals(PetUnlockConditionEnum.LE)) {
      return source.compareTo(target) <= 0;
    }

    return Boolean.FALSE;
  }
}

