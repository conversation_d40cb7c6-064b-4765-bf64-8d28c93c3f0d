package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.AdministratorRoomDAO;
import com.red.circle.other.infra.database.rds.entity.sys.AdministratorRoom;
import com.red.circle.other.infra.database.rds.service.sys.AdministratorRoomService;
import com.red.circle.other.inner.asserts.RoomErrorCode;
import com.red.circle.other.inner.model.cmd.sys.SysAdministratorRoomCmd;
import java.util.Objects;
import org.springframework.stereotype.Service;

/**
 * <p>
 * APP管理员房间关系表 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-30
 */
@Service
public class AdministratorRoomServiceImpl extends
    BaseServiceImpl<AdministratorRoomDAO, AdministratorRoom> implements AdministratorRoomService {

  @Override
  public Boolean isRoomExist(Long userId, Long roomId) {
    return Objects.nonNull(query().eq(AdministratorRoom::getRoomId, roomId).eq(AdministratorRoom::
        getUserId, userId).last(PageConstant.LIMIT_ONE).getOne());
  }

  @Override
  public void addSysAdministratorRoom(SysAdministratorRoomCmd sysAdministratorRoomDTO) {
    AdministratorRoom sysAdministratorRoom = query()
        .eq(AdministratorRoom::getUserId, sysAdministratorRoomDTO.getUserId())
        .eq(AdministratorRoom::getRoomId, sysAdministratorRoomDTO.getRoomId())
        .getOne();
    ResponseAssert.isNull(RoomErrorCode.ROOM_EXISTS, sysAdministratorRoom);

    save(new AdministratorRoom()
        .setUserId(sysAdministratorRoomDTO.getUserId())
        .setRoomId(sysAdministratorRoomDTO.getRoomId())
    );
  }

  @Override
  public void deleteSysAdministratorRoom(Long id) {
    removeById(id);
  }
}
