package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_operation_exclude_record")
public class OperationExcludeRecord implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识
   */
  @TableId("id")
  private Long id;

  /**
   * 用户id
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 需要被排除的用户id
   */
  @TableField("exclude_user_id")
  private Long excludeUserId;

  /**
   * 1.不喜欢 2.喜欢 3.举报拉黑
   */
  @TableField("type")
  private Boolean type;

  /**
   * 创建时间
   */
  @TableField("create_time")
  private LocalDateTime createTime;

  /**
   * 修改时间
   */
  @TableField("update_time")
  private LocalDateTime updateTime;


}
