package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsRoomLock;
import java.sql.Timestamp;

/**
 * <p>
 * 房间锁 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-03
 */
public interface PropsRoomLockService extends BaseService<PropsRoomLock> {

  /**
   * 添加或者修改.
   *
   * @param userId     用户id
   * @param appendDays 追加天数
   * @return true 成功 ，false 失败
   */
  boolean saveOrUpdate(Long userId, Integer appendDays);

  /**
   * 获取用户房间锁过期时间.
   *
   * @param userId 用户id
   * @return time
   */
  Timestamp getExpireTime(Long userId);

  /**
   * 是否存在未过期房间锁.
   *
   * @param userId 用户id
   * @return true 存在，false 不存在
   */
  boolean existsExpireTime(Long userId);
}
