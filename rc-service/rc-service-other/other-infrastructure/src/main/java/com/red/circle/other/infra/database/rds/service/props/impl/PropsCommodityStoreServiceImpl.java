package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.PropsCommodityStoreDAO;
import com.red.circle.other.infra.database.rds.entity.props.PropsCommodityStore;
import com.red.circle.other.infra.database.rds.service.props.PropsCommodityStoreService;
import com.red.circle.other.inner.enums.material.PropsCommodityType;
import com.red.circle.other.inner.enums.material.PropsCurrencyType;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * 道具商店 服务实现.
 *
 * <AUTHOR> on 2022/4/13
 */
@Service
public class PropsCommodityStoreServiceImpl extends
    BaseServiceImpl<PropsCommodityStoreDAO, PropsCommodityStore> implements
    PropsCommodityStoreService {

  @Override
  public List<PropsCommodityStore> listSysOriginRelease(SysOriginPlatformEnum sysOrigin,
      String propsType) {
    return query()
        .eq(PropsCommodityStore::getSysOrigin, sysOrigin)
        .eq(PropsCommodityStore::getPropsType, propsType)
        .eq(PropsCommodityStore::getShelfStatus, Boolean.TRUE)
        .eq(PropsCommodityStore::getDel, Boolean.FALSE)
        .orderByDesc(PropsCommodityStore::getSort)
        .last(PageConstant.formatLimit(200))
        .list();
  }

  @Override
  public List<PropsCommodityStore> listFreeTheme(SysOriginPlatformEnum sysOrigin) {
    return query()
        .eq(PropsCommodityStore::getSysOrigin, sysOrigin)
        .eq(PropsCommodityStore::getPropsType, PropsCommodityType.THEME)
        .eq(PropsCommodityStore::getShelfStatus, Boolean.TRUE)
        .eq(PropsCommodityStore::getDel, Boolean.FALSE)
        .eq(PropsCommodityStore::getCurrencyTypes, PropsCurrencyType.FREE.name())
        .orderByDesc(PropsCommodityStore::getSort)
        .last(PageConstant.formatLimit(200))
        .list();
  }

  @Override
  public List<Long> listSourceIdByIds(List<Long> ids) {
    return CollectionUtils.isEmpty(ids)
        ? CollectionUtils.newArrayList()
        : Optional.ofNullable(query()
                .select(PropsCommodityStore::getSourceId)
                .in(PropsCommodityStore::getId, ids)
                .list())
            .map(propsCarStores -> propsCarStores.stream()
                .map(PropsCommodityStore::getSourceId).collect(
                    Collectors.toList()))
            .orElse(CollectionUtils.newArrayList());
  }

  @Override
  public Long getSourceIdById(Long id) {
    return Optional.ofNullable(id)
        .flatMap(pId -> Optional.ofNullable(query()
                .select(PropsCommodityStore::getSourceId)
                .eq(PropsCommodityStore::getId, pId)
                .last(PageConstant.LIMIT_ONE)
                .getOne())
            .map(PropsCommodityStore::getSourceId))
        .orElse(null);
  }

  @Override
  public PropsCommodityStore getSourceIdById(String sysOrigin, Long sourceId) {
    return query()
        .eq(PropsCommodityStore::getSysOrigin, sysOrigin)
        .eq(PropsCommodityStore::getSourceId, sourceId)
        .orderByDesc(PropsCommodityStore::getUpdateTime)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PropsCommodityStore getBySourceId(SysOriginPlatformEnum sysOrigin, Long sourceId,
      String propsType) {
    return query()
        .eq(PropsCommodityStore::getSysOrigin, sysOrigin)
        .eq(PropsCommodityStore::getSourceId, sourceId)
        .eq(PropsCommodityStore::getPropsType, propsType)
        .last(PageConstant.LIMIT_ONE)
        .orderByDesc(PropsCommodityStore::getUpdateTime)
        .getOne();
  }

}
