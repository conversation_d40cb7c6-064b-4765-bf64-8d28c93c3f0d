package com.red.circle.other.infra.database.rds.service.team;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.team.BusinessDevelopmentTeam;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 商务拓展团队表 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
public interface BusinessDevelopmentTeamService extends
    BaseService<BusinessDevelopmentTeam> {

  /**
   * 根据BD获得代理ID列表-分页
   *
   * @param userId 用户id
   * @param lastId 记录id
   * @return ignore
   */
  List<BusinessDevelopmentTeam> listByUserId(Long userId, Long lastId);

  /**
   * 成员是否已存在DB列表.
   */
  boolean existTeam(Long agentUserId);

  /**
   * 根据团队ID获得BD用户id.
   */
  Long getUserIdByTeamId(Long teamId);

  /**
   * BD名下代理数量统计.
   */
  Long countTeamByBdUserId(Long bdUserId);

  /**
   * BD名下代理id.
   */
  Set<Long> getTeamIdsByBdUserId(Long bdUserId);

  /**
   * BD名下代理本月代理总目标.
   */
  BigDecimal countThisMonthAgentTarget(Long bdUserId);

  /**
   * 获取团队bd用户id.
   */
  Long getBdUserIdByTeamId(Long teamId);

  /**
   * 获取团队关联bd用户.
   */
  Map<Long, Long> mapTeamBdUserId(Set<Long> teamIds);

  /**
   * 通过团队ID获得BD用户ID.
   */
  Set<Long> getBdUserIdsByTeamIds(Collection<Long> teamIds);

  /**
   * 移除团队关系.
   */
  void removeBatchByTeamIds(List<Long> teamIds);

  /**
   * 添加DB成员.
   */
  void addTeam(String sysOrigin, Long agentUserId, Long bdUserId, Long teamId,
      Long updateUserId);

  /**
   * 获得bd的成员.
   */
  List<BusinessDevelopmentTeam> listByUserId(Long userId);

  /**
   * 根据bd用户id解散这个bd团队.
   */
  void deleteAllByUserId(Long userId);

}
