package com.red.circle.other.infra.database.rds.service.team;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.team.TeamTerminationFeeRecord;
import java.util.List;

/**
 * <p>
 * 团队主播解约费记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-09-20 15:10
 */
public interface TeamTerminationFeeRecordService extends BaseService<TeamTerminationFeeRecord> {

  List<TeamTerminationFeeRecord> listByTimeGt24AndPayeeUserNull();

}
