package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户手机号注册.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_mobile_auth")
public class MobileAuth extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 手机号码前缀.
   */
  @TableField("phone_prefix")
  private Integer phonePrefix;

  /**
   * 手机号码.
   */
  @TableField("phone_number")
  private String phoneNumber;

  /**
   * 手机号密码.
   */
  @TableField("pwd")
  private String pwd;

  /**
   * 0.未删除 1.已删除.
   */
  @TableField("is_del")
  private Boolean del;


}
