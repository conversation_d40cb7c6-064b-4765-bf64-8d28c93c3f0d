package com.red.circle.other.infra.database.rds.service.activity.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.ActivityGlobalizationDescriptionDAO;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityGlobalizationDescription;
import com.red.circle.other.infra.database.rds.service.activity.ActivityGlobalizationDescriptionService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动国际化语言描述 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-10
 */
@Service
public class ActivityGlobalizationDescriptionServiceImpl extends
    BaseServiceImpl<ActivityGlobalizationDescriptionDAO, ActivityGlobalizationDescription> implements
    ActivityGlobalizationDescriptionService {

  @Override
  public Map<Long, List<ActivityGlobalizationDescription>> mapByRelationIds(Set<Long> relationIds) {

    if (CollectionUtils.isEmpty(relationIds)) {
      return CollectionUtils.newHashMap();
    }

    return Optional.ofNullable(
            query()
                .in(ActivityGlobalizationDescription::getRelationId, relationIds)
                .list()
        ).map(
            descriptions -> descriptions.stream()
                .collect(Collectors.groupingBy(ActivityGlobalizationDescription::getRelationId)))
        .orElse(CollectionUtils.newHashMap());


  }

  @Override
  public List<ActivityGlobalizationDescription> listByRelationId(Long relationId) {
    return query().eq(ActivityGlobalizationDescription::getRelationId, relationId)
        .last(PageConstant.formatLimit(280))
        .list();
  }

}
