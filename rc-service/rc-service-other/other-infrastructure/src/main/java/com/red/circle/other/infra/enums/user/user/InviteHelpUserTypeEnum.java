package com.red.circle.other.infra.enums.user.user;

/**
 * <p>
 * 红包邀请活动-助力用户类型.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 17:21
 */
public enum InviteHelpUserTypeEnum {
  /**
   * 新用户.
   */
  NEW_USER("新用户"),

  /**
   * 回归用户.
   */
  RETURN_USER("回归用户"),

  /**
   * 其他.
   */
  OTHER("其他")

  ;
  private final String desc;

  InviteHelpUserTypeEnum(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }
}
