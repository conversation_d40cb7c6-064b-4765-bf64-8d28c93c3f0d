package com.red.circle.other.infra.database.rds.service.user.user.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.text.StringUtils;
import com.red.circle.other.infra.database.rds.dao.user.user.UserBeautifulNumberApplyDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserBeautifulNumberApply;
import com.red.circle.other.infra.database.rds.service.user.user.UserBeautifulNumberApplyService;
import com.red.circle.other.inner.model.cmd.user.UserBeautifulNumberApplyQryCmd;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户申请靓号记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
@Service
public class UserBeautifulNumberApplyServiceImpl extends
    BaseServiceImpl<UserBeautifulNumberApplyDAO, UserBeautifulNumberApply> implements
    UserBeautifulNumberApplyService {

  @Override
  public Boolean isExistPending(Long userId) {

    return Optional.ofNullable(query()
        .eq(UserBeautifulNumberApply::getApplyUser, userId)
        .eq(UserBeautifulNumberApply::getState, 0)
        .list()).map(CollectionUtil::isNotEmpty).orElse(Boolean.FALSE);
  }

  @Override
  public PageResult<UserBeautifulNumberApply> page(UserBeautifulNumberApplyQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), UserBeautifulNumberApply::getSysOrigin,
            query.getSysOrigin())
        .eq(Objects.nonNull(query.getUserId()), UserBeautifulNumberApply::getApplyUser,
            query.getUserId())
        .orderByDesc(UserBeautifulNumberApply::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public void handleApply(Long id, Integer state) {

    ResponseAssert.isTrue(CommonErrorCode.TYPE_IS_NOT_IN_SCOPE, checkState(state));

    UserBeautifulNumberApply apply = query().eq(UserBeautifulNumberApply::getId, id).getOne();
    ResponseAssert.notNull(CommonErrorCode.DATA_ERROR, apply);

    apply.setUpdateTime(TimestampUtils.now());
    apply.setState(state);
    updateSelectiveById(apply);

  }


  private boolean checkState(Integer state) {
    return Objects.equals(state, 1) || Objects.equals(state, 2);
  }
}
