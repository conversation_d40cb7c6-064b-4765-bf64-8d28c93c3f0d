package com.red.circle.other.infra.database.rds.service.team;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.team.UserHistoryIdentity;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 用户历史身份 服务.
 *
 * <AUTHOR> on 2023/8/31
 */
public interface UserHistoryIdentityService extends BaseService<UserHistoryIdentity> {

  /**
   * 保存身份=bd.
   */
  void saveBd(Long userId);

  /**
   * 保存身份=host.
   */
  void saveHost(Long userId);

  /**
   * 保存身份=agent and host.
   */
  void saveAgentAndHost(Long userId);

  /**
   * 获取一组历史身份.
   */
  Map<Long, UserHistoryIdentity> mapByIds(Set<Long> userIds);

  /**
   * 获取身份标签.
   */
  List<String> getLabels(Long userId);

  /**
   * 获取身份标签.
   */
  List<String> getLabels(UserHistoryIdentity identity);
}
