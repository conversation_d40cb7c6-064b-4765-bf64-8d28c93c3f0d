package com.red.circle.other.infra.database.rds.service.sys;


import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.WeekStarGroup;
import com.red.circle.other.inner.enums.activity.WeekStarGiftTypeEnum;
import com.red.circle.other.inner.model.cmd.sys.SysWeekStarGroupQryCmd;
import java.util.List;
import java.util.Optional;

/**
 * <p>
 * 当前周星礼物 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
public interface WeekStarGroupService extends BaseService<WeekStarGroup> {

  /**
   * 修改类型.
   *
   * @param id   记录id
   * @param type 类型
   */
  void updateTypeById(Long id, WeekStarGiftTypeEnum type);

  /**
   * 获取下周礼物.
   *
   * @param sysOrigin 系统
   * @param type      类型
   */
  Optional<WeekStarGroup> getByTypeOne(SysOriginPlatformEnum sysOrigin, WeekStarGiftTypeEnum type);

  /**
   * 获取滚动数最新的.
   *
   * @param sysOrigin 系统
   * @return ignore
   */
  Optional<WeekStarGroup> getStatusNoneMinDisplayNumber(SysOriginPlatformEnum sysOrigin);


  /**
   * 上周礼物修改为none.
   *
   * @param sysOrigin 系统
   */
  void updateLastWeekToNone(SysOriginPlatformEnum sysOrigin);

  /**
   * 将指定类型重制.
   */
  void updateLastWeekToNone(SysOriginPlatformEnum sysOrigin, List<WeekStarGiftTypeEnum> types);


  /**
   * 周星礼物分组列表.
   */
  PageResult<WeekStarGroup> getPage(SysWeekStarGroupQryCmd query);

  /**
   * 删除礼物分组.
   */
  void deleteById(Long id);
}
