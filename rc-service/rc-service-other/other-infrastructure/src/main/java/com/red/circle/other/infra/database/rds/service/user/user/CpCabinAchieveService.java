package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.CpCabinAchieve;

/**
 * <p>
 * CP小屋成就 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-11-16 14:36
 */
public interface CpCabinAchieveService extends BaseService<CpCabinAchieve> {

  /**
   * 累计cp成就.
   */
  void incr(Long cpValId, Long cpVal);

  /**
   * 上个月cp成就.
   */
  Long lastMonthCpVal(Long cpValId);

  /**
   * 历史最大成就.
   */
  Long maxCpVal(Long cpValId);

  /**
   * 根据cpID删除数据.
   */
  void deleteByCpValId(Long cpValId);

}
