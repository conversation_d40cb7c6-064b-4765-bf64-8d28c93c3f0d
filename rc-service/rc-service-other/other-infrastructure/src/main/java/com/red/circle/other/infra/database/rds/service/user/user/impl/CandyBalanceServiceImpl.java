package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.CandyBalanceDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.CandyBalance;
import com.red.circle.other.infra.database.rds.service.user.user.CandyBalanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户糖果余额 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CandyBalanceServiceImpl extends
    BaseServiceImpl<CandyBalanceDAO, CandyBalance> implements CandyBalanceService {

}
