package com.red.circle.other.infra.database.rds.service.pet;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.dto.pet.PetFeedingDTO;
import com.red.circle.other.infra.database.rds.entity.pet.PetUserPetBag;
import java.util.Map;

/**
 * <p>
 * 我的宠物背包 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface PetUserPetBagService extends BaseService<PetUserPetBag> {


  /**
   * 获取宠物映射.
   *
   * @param userId 用户id
   * @return map
   */
  Map<Long, PetUserPetBag> mapPet(Long userId);

  /**
   * 添加宠物.
   *
   * @param petBag 参数
   * @return true 成功 false 失败
   */
  boolean add(PetUserPetBag petBag);

  /**
   * 最新的一只宠物.
   *
   * @param userId 用户id
   * @return info
   */
  PetUserPetBag getLatest(Long userId);

  /**
   * 同步最新喂养时间.
   *
   * @param id 记录id
   */
  void syncLatestFeedingTime(Long id);

  /**
   * 改变喂食阶段信息.
   *
   * @param petFeeding param
   */
  void changeStage(PetFeedingDTO petFeeding);

  /**
   * 进阶.
   *
   * @param id    记录id
   * @param stage 阶段
   */
  void feedingAdvancedStage(Long id, String stage);

  /**
   * 获取最新的指定宠物信息.
   *
   * @param userId 用户id
   * @param petId  宠物id
   * @return obj
   */
  PetUserPetBag getLatestByPetId(Long userId, Long petId);
}
