package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.common.business.core.enums.GameStateEnum;
import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomPkRecord;
import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 房间PK记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-24
 */
public interface GameRoomPkRecordService extends BaseService<GameRoomPkRecord> {

  /**
   * 根据id与状态获得PK信息
   */
  GameRoomPkRecord getByIdByStatus(Long id, GameStateEnum gameStateEnum);

  /**
   * 根据roomId与状态获得PK信息
   */
  GameRoomPkRecord getByRoomIdByStatus(Long roomId, GameStateEnum gameStateEnum);

  /**
   * 根据userId与状态获得PK信息
   */
  GameRoomPkRecord getByUserIdByStatus(Long userId, GameStateEnum gameStateEnum);

  /**
   * 根据roomId与状态获得有效的PK信息
   */
  GameRoomPkRecord getEfficientPkByRoomId(Long roomId);

  /**
   * 是否有资格参加或发起pk
   */
  Boolean isCanJoinOrCreateGame(Long userId);

  /**
   * 根据id修改状态为主动结束
   */
  void updateGameOverById(Long id);

  /**
   * 根据条件获得集合
   */
  List<GameRoomPkRecord> listByLastIdByStatus(Long lastId, SysOriginPlatformEnum sysOrigin,
      GameStateEnum gameState, Long notRoomId, Long notUserId);

  /**
   * 查询当前时间大于结束时间(+1分钟 处理数据时间) ，且还在决斗中状态的PK记录
   */
  List<GameRoomPkRecord> listExpiredStartedRecordByNow(Timestamp nowTime);

  /**
   * 查询当前时间大于创建时间(+3分钟:等待时间， +1分钟 处理数据时间) ，且还在等待状态的PK记录
   */
  List<GameRoomPkRecord> listExpiredWaitingRecordByNow(Timestamp nowTime);

  /**
   * 是否有权限创建游戏
   *
   * @return true有权限 false无
   */
  Boolean isCanGame(Long userId, Long roomId);

}
