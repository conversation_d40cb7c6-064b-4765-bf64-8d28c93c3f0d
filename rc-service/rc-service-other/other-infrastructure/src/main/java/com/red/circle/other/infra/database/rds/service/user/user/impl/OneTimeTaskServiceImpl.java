package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.OneTimeTaskDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.OneTimeTask;
import com.red.circle.other.infra.database.rds.service.user.user.OneTimeTaskService;
import com.red.circle.other.infra.enums.user.user.UserOneTimeTaskTypeEnum;
import java.math.BigDecimal;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户只可触发一次的任务 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-15
 */
@Service
@RequiredArgsConstructor
public class OneTimeTaskServiceImpl extends BaseServiceImpl<OneTimeTaskDAO, OneTimeTask> implements
    OneTimeTaskService {


  @Override
  public OneTimeTask queryCompletedFirstCharge(Long userId, String sysOrigin) {
    return query()
        .eq(OneTimeTask::getUserId, userId)
        .eq(OneTimeTask::getSysOrigin, sysOrigin)
        .eq(OneTimeTask::getTaskType, UserOneTimeTaskTypeEnum.FIRST_CHARGE.name())
        .getOne();
  }

  @Override
  public void completedFirstCharge(Long userId, String sysOrigin) {
    if (isCompletedFirstCharge(userId, sysOrigin)) {
      return;
    }
    save(new OneTimeTask()
        .setUserId(userId)
        .setSysOrigin(sysOrigin)
        .setTaskType(UserOneTimeTaskTypeEnum.FIRST_CHARGE.name())
    );
  }

  @Override
  public void firstChargeStatusChangeReceive(Long userId, String sysOrigin,
      BigDecimal rewardQuantity) {

    OneTimeTask oneTimeTask = query()
        .eq(OneTimeTask::getUserId, userId)
        .eq(OneTimeTask::getSysOrigin, sysOrigin)
        .eq(OneTimeTask::getAwardStatus, Boolean.FALSE)
        .eq(OneTimeTask::getTaskType, UserOneTimeTaskTypeEnum.FIRST_CHARGE.name())
        .getOne();

    if (Objects.nonNull(oneTimeTask)) {
      oneTimeTask.setRewardQuantity(rewardQuantity);
      oneTimeTask.setAwardStatus(Boolean.TRUE);
      updateSelectiveById(oneTimeTask);
    }

  }

  Boolean isCompletedFirstCharge(Long userId, String sysOrigin) {
    OneTimeTask oneTimeTask = query()
        .eq(OneTimeTask::getUserId, userId)
        .eq(OneTimeTask::getSysOrigin, sysOrigin)
        .eq(OneTimeTask::getTaskType, UserOneTimeTaskTypeEnum.FIRST_CHARGE.name())
        .getOne();
    return Objects.nonNull(oneTimeTask);
  }


}
