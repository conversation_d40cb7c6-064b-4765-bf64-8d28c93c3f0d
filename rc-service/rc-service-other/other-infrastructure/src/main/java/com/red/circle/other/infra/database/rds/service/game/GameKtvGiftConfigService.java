package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameKtvGiftConfig;
import com.red.circle.other.inner.model.cmd.game.GameKtvGiftConfigQryCmd;
import com.red.circle.other.inner.model.dto.game.GameKtvGiftConfigDTO;
import java.util.List;

/**
 * <p>
 * ktv礼物配置信息.
 * </p>
 *
 * <AUTHOR> on 2023-05-05 18:06
 */
public interface GameKtvGiftConfigService extends BaseService<GameKtvGiftConfig> {

  /**
   * 获得礼物，表情数据.
   *
   * @param sysOrigin 系统
   * @param regionId  区域
   */
  List<GameKtvGiftConfig> listGiftEmojiByRegion(String sysOrigin, String regionId);

  PageResult<GameKtvGiftConfig> getSongGiftInfo(GameKtvGiftConfigQryCmd query);

  void addSongGift(GameKtvGiftConfigDTO param);

  void updateSongGift(GameKtvGiftConfigDTO param);

  void deleteSongGift(Long id, Long updateUser);
}
