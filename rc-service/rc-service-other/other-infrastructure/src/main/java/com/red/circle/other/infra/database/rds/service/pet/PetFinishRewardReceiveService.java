package com.red.circle.other.infra.database.rds.service.pet;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.pet.PetFinishRewardReceive;

/**
 * <p>
 * 宠物养成奖励领取记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
public interface PetFinishRewardReceiveService extends BaseService<PetFinishRewardReceive> {


  /**
   * 宠物领取状态.
   *
   * @param userId   用户id
   * @param petBagId 宠物背包id
   * @return true 已领取，false 没有领取
   */
  boolean existsReceiveStatus(Long userId, Long petBagId);
}
