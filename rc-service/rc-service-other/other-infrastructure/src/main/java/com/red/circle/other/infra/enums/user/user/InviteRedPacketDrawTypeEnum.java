package com.red.circle.other.infra.enums.user.user;

/**
 * <p>
 * 红包邀请活动-转盘奖励类型.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 17:21
 */
public enum InviteRedPacketDrawTypeEnum {

  /**
   * 美金.
   */
  USD("美金"),

  /**
   * 立即提现.
   */
  WITHDRAWAL("立即提现"),

  /**
   * 目标.
   */
  TARGET("目标"),

  /**
   * 钻石.
   */
  DIAMOND("钻石"),

  /**
   * 开红包.
   */
  OPEN_RED_PACKET("开红包");
  ;
  private final String desc;

  InviteRedPacketDrawTypeEnum(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }
}
