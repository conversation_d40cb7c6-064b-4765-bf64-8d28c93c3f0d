package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.GameListConfig;
import com.red.circle.other.inner.model.cmd.sys.SysGameListQryCmd;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 游戏列表配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
public interface GameListConfigService extends BaseService<GameListConfig> {

  /**
   * 获取上架配置.
   */
  List<GameListConfig> listShowcaseConfig(String sysOrigin,boolean fullScreen,boolean hotGame);

  /**
   * 根据游戏code获得游戏封面图.
   */
  String getCoverByGameCode(String gameCode);

  /**
   * 分页列表.
   */
  PageResult<GameListConfig> pageQuery(SysGameListQryCmd query);

  /**
   * 查询游戏信息
   */
  GameListConfig getByGameCode(String gameCode, String sysOrigin);

  /**
   * 校验是否有游戏下架
   */
  Boolean checkGameUnshelve(Set<Long> gameIds);
}
