package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.FriendshipCardConfig;
import com.red.circle.other.inner.model.cmd.user.SysUserFriendshipCardConfigQryCmd;
import java.util.List;

/**
 * <p>
 * 用户友谊卡片配置 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-10
 */
public interface FriendshipCardConfigService extends BaseService<FriendshipCardConfig> {

  List<FriendshipCardConfig> listConfig(String sysOrigin);

  FriendshipCardConfig getConfigByType(String sysOrigin, String type);

  PageResult<FriendshipCardConfig> pageConfig(
      SysUserFriendshipCardConfigQryCmd query);
}
