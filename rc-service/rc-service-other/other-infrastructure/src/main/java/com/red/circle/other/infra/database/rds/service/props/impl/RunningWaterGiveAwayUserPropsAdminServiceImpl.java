package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.RunningWaterGiveAwayUserPropsAdminDAO;
import com.red.circle.other.infra.database.rds.entity.props.RunningWaterGiveAwayUserPropsAdmin;
import com.red.circle.other.infra.database.rds.service.props.RunningWaterGiveAwayUserPropsAdminService;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 管理员赠送道具 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-29
 */
@Service
public class RunningWaterGiveAwayUserPropsAdminServiceImpl extends
    BaseServiceImpl<RunningWaterGiveAwayUserPropsAdminDAO, RunningWaterGiveAwayUserPropsAdmin> implements
    RunningWaterGiveAwayUserPropsAdminService {

  @Override
  public boolean existsByAcceptUserId(String acceptUserId) {
    return Optional.ofNullable(
            query()
                .select(RunningWaterGiveAwayUserPropsAdmin::getId)
                .eq(RunningWaterGiveAwayUserPropsAdmin::getAcceptUserId, acceptUserId)
                .last(PageConstant.LIMIT_ONE)
                .getOne()
        ).map(runningWaterGiveAwayUserPropsAdmin -> Objects
            .nonNull(runningWaterGiveAwayUserPropsAdmin.getId()))
        .orElse(Boolean.FALSE);
  }
}
