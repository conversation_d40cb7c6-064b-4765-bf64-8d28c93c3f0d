package com.red.circle.other.infra.database.rds.service.user.user.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.NotActionLogDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.NotActionLog;
import com.red.circle.other.infra.database.rds.service.user.user.NotActionLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户不活跃清除日志 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-09-06 18:08
 */
@Service
@RequiredArgsConstructor
public class NotActionLogServiceImpl extends
    BaseServiceImpl<NotActionLogDAO, NotActionLog> implements
    NotActionLogService {

}
