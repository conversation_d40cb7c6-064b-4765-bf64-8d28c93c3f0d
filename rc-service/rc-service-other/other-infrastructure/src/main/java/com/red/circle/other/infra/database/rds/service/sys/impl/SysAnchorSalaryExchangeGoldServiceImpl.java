package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.SysAnchorSalaryExchangeGoldDAO;
import com.red.circle.other.infra.database.rds.entity.sys.SysAnchorSalaryExchangeGold;
import com.red.circle.other.infra.database.rds.service.sys.SysAnchorSalaryExchangeGoldService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 主播工资兑换糖果记录.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Service
@RequiredArgsConstructor
public class SysAnchorSalaryExchangeGoldServiceImpl extends
    BaseServiceImpl<SysAnchorSalaryExchangeGoldDAO, SysAnchorSalaryExchangeGold> implements
    SysAnchorSalaryExchangeGoldService {

  private final SysAnchorSalaryExchangeGoldDAO sysAnchorSalaryExchangeGoldDAO;


}
