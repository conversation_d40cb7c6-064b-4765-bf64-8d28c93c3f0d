package com.red.circle.other.infra.database.rds.service.activity.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.activity.ActivityRewardSendListDAO;
import com.red.circle.other.infra.database.rds.entity.activity.ActivityRewardSendList;
import com.red.circle.other.infra.database.rds.service.activity.ActivityRewardSendListService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 活动奖励发送清单以及容错撤回奖励 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-18
 */
@Service
public class ActivityRewardSendListServiceImpl extends
    BaseServiceImpl<ActivityRewardSendListDAO, ActivityRewardSendList> implements
    ActivityRewardSendListService {

  @Override
  public List<ActivityRewardSendList> listUserReward(Integer sendRewardTime) {

    return query()
        .eq(ActivityRewardSendList::getSendTime, sendRewardTime)
        .eq(ActivityRewardSendList::getRevoking, Boolean.FALSE)
        .list();
  }

  @Override
  public void updateRevokeByIds(Set<Long> ids) {

    if (CollectionUtils.isEmpty(ids)) {
      return;
    }

    update()
        .set(ActivityRewardSendList::getRevoking, Boolean.TRUE)
        .set(ActivityRewardSendList::getRevokingTime, LocalDateTime.now())
        .in(ActivityRewardSendList::getId, ids)
        .execute();

  }
}
