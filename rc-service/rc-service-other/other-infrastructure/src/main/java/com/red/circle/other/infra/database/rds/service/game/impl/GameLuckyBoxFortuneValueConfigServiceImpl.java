package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxFortuneValueConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxFortuneValueConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxFortuneValueConfigService;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 幸运值概率配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxFortuneValueConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxFortuneValueConfigDAO, GameLuckyBoxFortuneValueConfig> implements
    GameLuckyBoxFortuneValueConfigService {

  @Override
  public GameLuckyBoxFortuneValueConfig getBySysOrigin(String sysOriginName) {
    return query().eq(GameLuckyBoxFortuneValueConfig::getSysOrigin, sysOriginName).getOne();
  }

  @Override
  public GameLuckyBoxFortuneValueConfig getLuckyBoxFortuneValueConfig(String sysOrigin) {
    return Optional.ofNullable(
            query().eq(GameLuckyBoxFortuneValueConfig::getSysOrigin, sysOrigin).getOne())
        .orElse(new GameLuckyBoxFortuneValueConfig());
  }

}
