package com.red.circle.other.infra.database.rds.service.pet.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.pet.PetUserPetBagDAO;
import com.red.circle.other.infra.database.rds.dto.pet.PetFeedingDTO;
import com.red.circle.other.infra.database.rds.entity.pet.PetUserPetBag;
import com.red.circle.other.infra.database.rds.service.pet.PetUserPetBagService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 我的宠物背包 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
@Service
public class PetUserPetBagServiceImpl extends
    BaseServiceImpl<PetUserPetBagDAO, PetUserPetBag> implements PetUserPetBagService {

  @Override
  public Map<Long, PetUserPetBag> mapPet(Long userId) {
    return Optional.ofNullable(query()
            .eq(PetUserPetBag::getUserId, userId)
            .last(PageConstant.MAX_LIMIT).list())
        .map(petUserPetBags -> petUserPetBags.stream()
            .collect(Collectors.toMap(PetUserPetBag::getPetId, v -> v)))
        .orElseGet(CollectionUtils::newHashMap);
  }

  @Override
  public boolean add(PetUserPetBag petBag) {
    if (exitsByPetId(petBag.getUserId(), petBag.getPetId())) {
      return Boolean.FALSE;
    }
    return save(petBag);
  }

  @Override
  public PetUserPetBag getLatest(Long userId) {
    return query()
        .eq(PetUserPetBag::getUserId, userId)
        .orderByDesc(PetUserPetBag::getId)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public void syncLatestFeedingTime(Long id) {
    update()
        .set(PetUserPetBag::getLatestFeedingTime, TimestampUtils.now())
        .eq(PetUserPetBag::getId, id)
        .execute();
  }

  @Override
  public void changeStage(PetFeedingDTO petFeeding) {
    update()
        .setSql("consume_food=consume_food+" + petFeeding.getConsumeQuantity())
        .setSql(Objects.nonNull(petFeeding.getFeedingNum()),
            "feeding_num=feeding_num+" + petFeeding.getFeedingNum())
        .set(PetUserPetBag::getPetStage, petFeeding.getStage())
        .set(PetUserPetBag::getLatestFeedingTime, TimestampUtils.now())
        .eq(PetUserPetBag::getId, petFeeding.getId())
        .execute();
  }

  @Override
  public void feedingAdvancedStage(Long id, String stage) {
    update()
        .set(PetUserPetBag::getFeedingNum, 0)
        .set(PetUserPetBag::getPetStage, stage)
        .set(PetUserPetBag::getLatestFeedingTime, TimestampUtils.now())
        .eq(PetUserPetBag::getId, id)
        .execute();
  }

  @Override
  public PetUserPetBag getLatestByPetId(Long userId, Long petId) {
    return query()
        .eq(PetUserPetBag::getUserId, userId)
        .eq(PetUserPetBag::getPetId, petId)
        .last(PageConstant.LIMIT_ONE)
        .orderByDesc(PetUserPetBag::getId)
        .getOne();
  }

  private boolean exitsByPetId(Long userId, Long petId) {
    return Optional.ofNullable(
        query()
            .select(PetUserPetBag::getId)
            .eq(PetUserPetBag::getUserId, userId)
            .eq(PetUserPetBag::getPetId, petId)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).map(petBag -> Objects.nonNull(petBag.getId())).orElse(Boolean.FALSE);
  }

}
