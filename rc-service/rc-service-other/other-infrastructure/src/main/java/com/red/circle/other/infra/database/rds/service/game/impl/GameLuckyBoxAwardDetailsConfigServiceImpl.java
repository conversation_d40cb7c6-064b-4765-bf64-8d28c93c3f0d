package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxAwardDetailsConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxAwardDetailsConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxAwardDetailsConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckyBoxAwardDetailsConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 累计抽奖奖励配置详情 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxAwardDetailsConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxAwardDetailsConfigDAO, GameLuckyBoxAwardDetailsConfig> implements
    GameLuckyBoxAwardDetailsConfigService {

  @Override
  public List<GameLuckyBoxAwardDetailsConfig> listByAwardId(Long awardId) {
    return query().eq(GameLuckyBoxAwardDetailsConfig::getAwardId, awardId).list();
  }

  @Override
  public void deleteByAwardId(Long awardId) {
    delete().eq(GameLuckyBoxAwardDetailsConfig::getAwardId, awardId).execute();
  }

  @Override
  public List<GameLuckyBoxAwardDetailsConfig> listGameLuckyBoxAwardDetailsConfig(
      GameLuckyBoxAwardDetailsConfigQryCmd query) {
    return query().eq(
            GameLuckyBoxAwardDetailsConfig::getAwardId, query.getAwardId())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()),
            GameLuckyBoxAwardDetailsConfig::getSysOrigin, query.getSysOrigin()).list();
  }
}
