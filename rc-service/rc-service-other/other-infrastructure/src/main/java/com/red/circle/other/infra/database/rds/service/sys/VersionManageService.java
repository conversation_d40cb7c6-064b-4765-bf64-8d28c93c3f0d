package com.red.circle.other.infra.database.rds.service.sys;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.sys.VersionManage;
import com.red.circle.other.inner.model.cmd.sys.VersionManageQryCmd;

/**
 * <p>
 * app版本管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-12
 */
public interface VersionManageService extends BaseService<VersionManage> {

  VersionManage getVersion(String sysOrigin, String platform, String channel, Boolean review);

  PageResult<VersionManage> pageVersionManage(VersionManageQryCmd cmd);

  Boolean saveVersionManage(VersionManage param);

  void deleteVersionManage(Long id);

  void updateVersionManage(VersionManage param);
}
