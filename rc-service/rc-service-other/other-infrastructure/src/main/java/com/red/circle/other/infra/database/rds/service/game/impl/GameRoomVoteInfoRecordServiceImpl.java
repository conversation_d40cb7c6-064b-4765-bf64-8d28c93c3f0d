package com.red.circle.other.infra.database.rds.service.game.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameRoomVoteInfoRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomVoteInfoRecord;
import com.red.circle.other.infra.database.rds.service.game.GameRoomVoteInfoRecordService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-04
 */
@Service
public class GameRoomVoteInfoRecordServiceImpl extends
    BaseServiceImpl<GameRoomVoteInfoRecordDAO, GameRoomVoteInfoRecord> implements
    GameRoomVoteInfoRecordService {

}
