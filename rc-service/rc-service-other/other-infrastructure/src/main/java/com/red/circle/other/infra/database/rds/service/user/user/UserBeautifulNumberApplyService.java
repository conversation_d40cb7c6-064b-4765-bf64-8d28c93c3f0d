package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserBeautifulNumberApply;
import com.red.circle.other.inner.model.cmd.user.UserBeautifulNumberApplyQryCmd;

/**
 * <p>
 * 用户申请靓号记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
public interface UserBeautifulNumberApplyService extends BaseService<UserBeautifulNumberApply> {

  Boolean isExistPending(Long userId);

  PageResult<UserBeautifulNumberApply> page(UserBeautifulNumberApplyQryCmd query);

  void handleApply(Long id, Integer state);
}
