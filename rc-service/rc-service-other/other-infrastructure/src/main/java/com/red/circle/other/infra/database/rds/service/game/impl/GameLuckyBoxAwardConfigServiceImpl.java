package com.red.circle.other.infra.database.rds.service.game.impl;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxAwardConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxAwardConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxAwardConfigService;
import com.red.circle.other.inner.model.cmd.game.GameLuckBoxAwardConfigQryCmd;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 累计抽奖奖励配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
public class GameLuckyBoxAwardConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxAwardConfigDAO, GameLuckyBoxAwardConfig> implements
    GameLuckyBoxAwardConfigService {

  @Override
  public List<GameLuckyBoxAwardConfig> listBySysOrigin(String sysOriginName) {
    return query().eq(GameLuckyBoxAwardConfig::getSysOrigin, sysOriginName).list();
  }

  @Override
  public PageResult<GameLuckyBoxAwardConfig> pageAwardConfig(
      GameLuckBoxAwardConfigQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), GameLuckyBoxAwardConfig::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getLotteryType()), GameLuckyBoxAwardConfig::getLotteryType,
            query.getLotteryType())
        .orderByDesc(GameLuckyBoxAwardConfig::getCreateTime)
        .page(query.getPageQuery());
  }
}
