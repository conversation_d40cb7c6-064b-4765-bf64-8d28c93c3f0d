package com.red.circle.other.infra.gateway.approval;

import com.red.circle.common.business.core.enums.DataApprovalTypeEnum;
import com.red.circle.common.business.core.enums.DataApprovalTypeEnum.Type;
import com.red.circle.mq.business.model.event.approval.CensorContent;
import com.red.circle.mq.business.model.event.approval.CensorProfileEvent;
import com.red.circle.mq.rocket.business.producer.CensorMqMessage;
import com.red.circle.other.domain.gateway.approval.ProfileApprovalGateway;
import com.red.circle.other.domain.model.approval.ProfileApprovalContent;
import com.red.circle.other.domain.model.approval.ProfileContent;
import com.red.circle.other.infra.database.rds.service.approval.ApprovalUserSettingDataService;
import com.red.circle.tool.core.collection.CollectionUtils;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 资料审核.
 *
 * <AUTHOR> on 2024/1/18
 */
@Component
@RequiredArgsConstructor
public class ProfileApprovalGatewayImpl implements ProfileApprovalGateway {

  private final CensorMqMessage censorMqMessage;
  private final ApprovalUserSettingDataService approvalUserSettingDataService;

  @Override
  public void submitApproval(ProfileApprovalContent content) {

    DataApprovalTypeEnum approvalType = content.getApprovalType();

    if (Objects.equals(approvalType.getType(), Type.TEXT)) {
      processText(content);
      return;
    }

    if (Objects.equals(approvalType.getType(), Type.IMAGE)) {
      processImages(content);
      return;
    }

    throw new IllegalArgumentException(
        "不支持类型: " + content.getApprovalType().getType().getClass()
    );
  }

  @Override
  public void submitApprovalBatch(List<ProfileApprovalContent> content) {
    for (ProfileApprovalContent profileApprovalContent : content) {
      submitApproval(profileApprovalContent);
    }
  }

  private void processText(ProfileApprovalContent content) {
    approvalUserSettingDataService
        .saveOrUpdateApproval(content.getBeUserId(),
            content.getSysOrigin(),
            content.getApprovalType()
        );
  }

  private void processImages(ProfileApprovalContent content) {
    // 更新审批状态
    approvalUserSettingDataService.saveOrUpdateApproval(content.getBeUserId(),
        content.getSysOrigin(),
        content.getApprovalType());
    // 发送审核
    censorMqMessage.image(toCensorProfileEvent(content.getApprovalType(), content));
  }

  private CensorProfileEvent toCensorProfileEvent(DataApprovalTypeEnum approvalType,
      ProfileApprovalContent content) {
    return new CensorProfileEvent()
        .setUserId(content.getBeUserId())
        .setApprovalType(approvalType)
        .setContents(toListCensorContent(content.getContents()));
  }

  private List<CensorContent> toListCensorContent(List<ProfileContent> contents) {
    if (CollectionUtils.isEmpty(contents)) {
      throw new IllegalArgumentException("contents required.");
    }

    return contents.stream().map(item -> new CensorContent()
        .setId(item.getId())
        .setContent(item.getContent())
    ).toList();

  }

}
