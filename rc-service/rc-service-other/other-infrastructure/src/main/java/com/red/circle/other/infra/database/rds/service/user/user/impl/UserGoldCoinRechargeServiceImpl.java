package com.red.circle.other.infra.database.rds.service.user.user.impl;


import cn.hutool.core.text.CharSequenceUtil;
import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.UserGoldCoinRechargeDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.UserGoldCoinRecharge;
import com.red.circle.other.infra.database.rds.service.user.user.UserGoldCoinRechargeService;
import com.red.circle.other.inner.model.cmd.team.bd.BdTeamWorkStatisticsQryCmd;
import com.red.circle.other.inner.model.cmd.user.InviteStatisticsQryCmd;
import com.red.circle.other.inner.model.dto.user.UserGoldCoinRechargeDTO;
import com.red.circle.tool.core.thread.ThreadPoolManager;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


/**
 * <p>
 * 用户账号认证 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-09-13 11:43
 */
@Service
@RequiredArgsConstructor
public class UserGoldCoinRechargeServiceImpl extends
    BaseServiceImpl<UserGoldCoinRechargeDAO, UserGoldCoinRecharge> implements
    UserGoldCoinRechargeService {

  private final UserGoldCoinRechargeDAO userGoldCoinRechargeDAO;

  @Override
  public List<UserGoldCoinRechargeDTO> listRechargeCoinsByUserIds(Set<Long> userIds) {
    return userGoldCoinRechargeDAO.listRechargeCoinsByUserIds(userIds);
  }

  @Override
  public Map<Long, BigDecimal> mapGoldCoinRechargeByUserIds(InviteStatisticsQryCmd qryCmd) {
    Map<Long, BigDecimal> map = Maps.newConcurrentMap();
    BdTeamWorkStatisticsQryCmd cmd = qryCmd.getQryCmd();
    if (CharSequenceUtil.hasBlank(cmd.getStartDate(), cmd.getEndDate())) {
      cmd.setStartDate(LocalDate.now().minusMonths(1).toString());
      cmd.setEndDate(LocalDate.now().plusDays(1).toString());
    }
    ExecutorService es = Executors.newCachedThreadPool();
    List<CompletableFuture<Void>> futures = qryCmd.getUserIds().stream()
        .map(userId -> CompletableFuture.supplyAsync(() -> {
              BigDecimal totalAmount = userGoldCoinRechargeDAO.getInviteTotalAmount(cmd, userId);
              if(null==totalAmount){
                totalAmount=BigDecimal.ZERO;
              }
              return Map.entry(userId, totalAmount);
            }, es)
            .thenAccept(entry -> map.put(entry.getKey(), entry.getValue())))
            .toList();
    CompletableFuture<Void> allOf = CompletableFuture.allOf(
        futures.toArray(new CompletableFuture[0]));
    try {
      // 等待所有任务完成
      allOf.get();
    } catch (InterruptedException | ExecutionException e) {
      throw new RuntimeException(e);
    }
    return map;
  }
}
