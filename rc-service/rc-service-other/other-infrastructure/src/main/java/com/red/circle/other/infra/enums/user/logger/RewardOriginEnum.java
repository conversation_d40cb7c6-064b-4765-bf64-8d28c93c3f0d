package com.red.circle.other.infra.enums.user.logger;

/**
 * 用户邀请奖励枚举.
 *
 * <AUTHOR> on 2021/4/27
 */
public enum RewardOriginEnum {

  /**
   * 邀请用户注册 (invite register).
   */
  INVITE_REGISTER("invite register"),

  /**
   * 邀请的用户首次充值.
   */
  INVITE_FIRST_RECHARGE("first recharge"),

  /**
   * 邀请用户每笔充值奖励百分比.
   */
  INVITED_USER_EACH_RECHARGE("each recharge");

  private final String desc;

  RewardOriginEnum(String desc) {
    this.desc = desc;
  }

  public String getDesc() {
    return desc;
  }
}
