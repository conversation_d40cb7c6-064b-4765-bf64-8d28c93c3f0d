package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.StartPageBackDAO;
import com.red.circle.other.infra.database.rds.entity.sys.StartPageBack;
import com.red.circle.other.infra.database.rds.enums.StartPageType;
import com.red.circle.other.infra.database.rds.service.sys.StartPageBackService;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 启动页背景图配置 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-30
 */
@Service
public class StartPageBackServiceImpl extends
    BaseServiceImpl<StartPageBackDAO, StartPageBack> implements StartPageBackService {

  @Override
  public String getBackByType(SysOriginPlatformEnum sysOrigin, StartPageType startPageType) {
    return Optional.ofNullable(
        query()
            .select(StartPageBack::getBackUrl)
            .eq(StartPageBack::getSysOrigin, sysOrigin)
            .eq(StartPageBack::getType, startPageType)
            .last(PageConstant.LIMIT_ONE)
            .getOne()
    ).map(StartPageBack::getBackUrl).orElse(null);
  }

}
