package com.red.circle.other.infra.database.rds.service.pet.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.pet.PetFinishRewardReceiveDAO;
import com.red.circle.other.infra.database.rds.entity.pet.PetFinishRewardReceive;
import com.red.circle.other.infra.database.rds.service.pet.PetFinishRewardReceiveService;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 宠物养成奖励领取记录 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Service
public class PetFinishRewardReceiveServiceImpl extends
    BaseServiceImpl<PetFinishRewardReceiveDAO, PetFinishRewardReceive> implements
    PetFinishRewardReceiveService {

  @Override
  public boolean existsReceiveStatus(Long userId, Long petBagId) {
    return Optional.ofNullable(query()
            .select(PetFinishRewardReceive::getId)
            .eq(PetFinishRewardReceive::getUserId, userId)
            .eq(PetFinishRewardReceive::getPetBagId, petBagId)
            .last(PageConstant.LIMIT_ONE)
            .getOne())
        .map(petFinishRewardReceive -> Objects.nonNull(petFinishRewardReceive.getId()))
        .orElse(Boolean.FALSE);
  }
}
