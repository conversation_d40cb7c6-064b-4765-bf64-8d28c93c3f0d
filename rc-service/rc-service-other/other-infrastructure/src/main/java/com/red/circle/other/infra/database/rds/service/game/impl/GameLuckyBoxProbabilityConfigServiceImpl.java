package com.red.circle.other.infra.database.rds.service.game.impl;


import com.google.common.collect.Maps;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.convertor.game.GameListConvertor;
import com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxProbabilityConfigDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameLuckyBoxProbabilityConfig;
import com.red.circle.other.infra.database.rds.service.game.GameLuckyBoxProbabilityConfigService;
import com.red.circle.other.inner.model.dto.game.GameLuckyBoxProbabilityConfigDTO;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 抽奖概率配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-08-26 10:41
 */
@Service
@RequiredArgsConstructor
public class GameLuckyBoxProbabilityConfigServiceImpl extends
    BaseServiceImpl<GameLuckyBoxProbabilityConfigDAO, GameLuckyBoxProbabilityConfig> implements
    GameLuckyBoxProbabilityConfigService {

  private final GameListConvertor gameListConvertor;

  @Override
  public List<GameLuckyBoxProbabilityConfig> getByTimeId(Long timeId) {
    return query().eq(GameLuckyBoxProbabilityConfig::getTimeId, timeId).list();
  }

  @Override
  public void deleteByTimeId(Long timeId) {
    delete().eq(GameLuckyBoxProbabilityConfig::getTimeId, timeId).execute();
  }

  @Override
  public Map<Long, List<GameLuckyBoxProbabilityConfigDTO>> getLuckyBoxProbabilityDetails(
      String sysOrigin) {
    return Optional.ofNullable(
            query().eq(GameLuckyBoxProbabilityConfig::getSysOrigin, sysOrigin).list())
        .map(boxProbabilityConfigs ->
            boxProbabilityConfigs.stream().map(
                gameListConvertor::toGameLuckyBoxProbabilityConfigDTO).collect(
                Collectors.groupingBy(GameLuckyBoxProbabilityConfigDTO::getTimeId)))
        .orElse(Maps.newHashMap());
  }


  @Override
  public void add(Long timeId, List<GameLuckyBoxProbabilityConfig> gameLuckyBoxProbabilityDetails) {
    delete().eq(GameLuckyBoxProbabilityConfig::getTimeId, timeId).execute();
    saveBatch(gameLuckyBoxProbabilityDetails);
  }
}
