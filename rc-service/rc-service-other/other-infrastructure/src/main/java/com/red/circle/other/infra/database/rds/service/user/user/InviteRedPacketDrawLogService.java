package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.InviteRedPacketDrawLog;
import com.red.circle.other.inner.model.cmd.user.invite.InviteRedPacketDrawLogPageQryCmd;
import java.util.List;

/**
 * <p>
 * 邀请新用户-红包抽奖记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-12-05 18:10
 */
public interface InviteRedPacketDrawLogService extends BaseService<InviteRedPacketDrawLog> {

  List<InviteRedPacketDrawLog> pageLotteryDetails(Long lastId, Long userId);

  PageResult<InviteRedPacketDrawLog> pageLog(InviteRedPacketDrawLogPageQryCmd cmd);

}
