package com.red.circle.other.infra.database.rds.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 主播工资兑换糖果记录.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-26
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("sys_anchor_salary_exchange_gold")
public class SysAnchorSalaryExchangeGold extends TimestampBaseEntity {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 糖果数.
   */
  @TableField("candy_number")
  private BigDecimal candyNumber;

  /**
   * 价值(美元).
   */
  @TableField("usd_amount")
  private BigDecimal usdAmount;


}
