package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 邀请用户奖励流水表.
 * </p>
 *
 * <AUTHOR> on 2021/4/27
 * @since 2021/5/25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_invite_user_reward_record")
public class InviteUserRewardRecord extends TimestampBaseEntity implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统来源.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户ID.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 被邀用户ID.
   */
  @TableField("invite_user_id")
  private Long inviteUserId;

  /**
   * 来源类型.
   */
  @TableField("origin")
  private String origin;

  /**
   * 来源类型-名称.
   */
  @TableField("origin_name")
  private String originName;

  /**
   * 数量.
   */
  @TableField("quantity")
  private BigDecimal quantity;

}
