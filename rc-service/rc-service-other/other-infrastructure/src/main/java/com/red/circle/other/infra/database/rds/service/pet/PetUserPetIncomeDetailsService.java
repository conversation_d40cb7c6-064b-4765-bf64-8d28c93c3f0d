package com.red.circle.other.infra.database.rds.service.pet;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.pet.PetUserPetIncomeDetails;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 我的宠物收益明细 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-14
 */
public interface PetUserPetIncomeDetailsService extends BaseService<PetUserPetIncomeDetails> {

  /**
   * 获取收益详情.
   *
   * @param userId 用户id
   * @return list
   */
  List<PetUserPetIncomeDetails> listDetailsTwoSize(Long userId);

  /**
   * 是否可以领取.
   *
   * @param id 记录id
   * @return true 可以 false 不可以
   */
  boolean checkCanReceive(Long id);

  /**
   * 更新领取状态.
   *
   * @param details 记录信息
   * @return true 成功，false 失败
   */
  boolean updateCharged(PetUserPetIncomeDetails details);

  /**
   * 更新版本号.
   *
   * @param details 记录信息
   * @return true 成功，false 失败
   */
  boolean updateVersion(PetUserPetIncomeDetails details);

  /**
   * 是否存在为收取.
   *
   * @param userId 用户id
   * @return true 存在，false 不存在
   */
  boolean existsNotCharged(Long userId);

  /**
   * 修改可偷取量
   *
   * @param id               记录id
   * @param stealQuantity    偷取量
   * @param casStealQuantity 偷取锁
   * @return true 成功，false 失败
   */
  boolean updateCasStealQuantity(Long id, BigDecimal stealQuantity, BigDecimal casStealQuantity);

  /**
   * 验证用户是否可以被偷取.
   *
   * @param userIds 用户id
   * @return map
   */
  Map<Long, List<PetUserPetIncomeDetails>> mapStealableByUserId(Set<Long> userIds);
}
