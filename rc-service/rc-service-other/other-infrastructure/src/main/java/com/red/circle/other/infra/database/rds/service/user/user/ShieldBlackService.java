package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.ShieldBlack;
import com.red.circle.other.infra.enums.user.user.PullBlackEnum;
import java.util.List;

/**
 * <p>
 * 用户屏蔽不喜欢 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-01
 */
public interface ShieldBlackService extends BaseService<ShieldBlack> {

  /**
   * 保存屏蔽信息.
   *
   * @param userId       用户id
   * @param shieldUserId 被屏蔽拉黑的用户id
   * @since 1.0
   */
  void saveShieldBlack(Long userId, Long shieldUserId);

  /**
   * 检查是否已在黑名单.
   *
   * @param userId       用户id
   * @param shieldUserId 拉黑用户
   * @return true 存在， false 不存在
   */
  boolean checkShieldBlack(Long userId, Long shieldUserId);

  /**
   * 获取拉黑状态（屏蔽拉黑都一样）.
   *
   * @param userId      用户id
   * @param blackUserId 被屏蔽拉黑的用户id
   * @return 状态
   */
  PullBlackEnum getBlackStatus(Long userId, Long blackUserId);

  /**
   * 移除黑名单.
   *
   * @param userId       用户id
   * @param shieldUserId 被屏蔽拉黑的用户id
   * @since 1.0
   */
  void removeShieldBlack(Long userId, Long shieldUserId);

  /**
   * 移除黑名单-双方.
   *
   * @param userId       用户id
   * @param shieldUserId 被屏蔽拉黑的用户id
   * @since 1.0
   */
  void removeShieldBlackBothSides(Long userId, Long shieldUserId);

  /**
   * 分頁.
   *
   * @param userId 用戶
   * @param lastId 最后一个标识ID
   * @return 结果
   */
  List<ShieldBlack> pageUserBlacklist(Long userId, Long lastId);

}
