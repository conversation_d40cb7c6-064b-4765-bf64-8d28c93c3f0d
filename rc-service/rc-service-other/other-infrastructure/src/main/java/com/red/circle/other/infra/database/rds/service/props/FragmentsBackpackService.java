package com.red.circle.other.infra.database.rds.service.props;


import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.FragmentsBackpack;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 用户碎片背包 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
public interface FragmentsBackpackService extends BaseService<FragmentsBackpack> {


  /**
   * 添加碎片.
   *
   * @param userId      用户id
   * @param fragmentsId 碎片id
   * @param quantity    数量
   * @param type        类型
   * @return Fragments
   */
  FragmentsBackpack incrFragments(Long userId, Long fragmentsId, Integer quantity, String type);

  /**
   * 减少碎片.
   *
   * @param userId       用户id
   * @param fragmentsIds 碎片ids
   * @param quantity     数量
   */
  void decrFragments(Long userId, Set<Long> fragmentsIds, Integer quantity);

  /**
   * 根据用户id与碎片集合查询用户碎片背包，并且数量大于0
   *
   * @param userId       用户id
   * @param fragmentsIds 碎片id
   * @return 背包
   */
  List<FragmentsBackpack> listByUserIdByFragmentsIds(Long userId, Set<Long> fragmentsIds);


}
