package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.sql.Timestamp;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import lombok.experimental.Accessors;

/**
 * <p>
 * 告白机会.
 * </p>
 *
 * <AUTHOR> on 2023-11-14 15:45
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@Accessors(chain = true)
@TableName("user_confession_chance")
public class ConfessionChance extends TimestampBaseEntity {

  private static final long serialVersionUID = 1L;

  /**
   * 主键id.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 系统平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 告白对象用户id.
   */
  @TableField("confession_user_id")
  private Long confessionUserId;

  /**
   * 告白礼物id.
   */
  @TableField("gift_id")
  private Long giftId;

  /**
   * 有效期.
   */
  @TableField("expired_time")
  private Timestamp expiredTime;

  /**
   * 0.未告白,1.已告白.
   */
  @TableField("is_confessed")
  private Boolean confessed;


}
