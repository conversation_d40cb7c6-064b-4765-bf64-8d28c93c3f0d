package com.red.circle.other.infra.database.rds.service.props;

import com.red.circle.common.business.core.enums.SysOriginPlatformEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.props.PropsCommodityStore;
import java.util.List;

/**
 * <p>
 * 道具商店 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022/04/13
 */
public interface PropsCommodityStoreService extends BaseService<PropsCommodityStore> {


  /**
   * 获取平台已发布道具信息.
   *
   * @param sysOrigin 平台
   * @param propsType 道具类型
   * @return list
   */
  List<PropsCommodityStore> listSysOriginRelease(SysOriginPlatformEnum sysOrigin, String propsType);

  /**
   * 获取平台免费主题.
   *
   * @param sysOrigin 平台
   * @return list
   */
  List<PropsCommodityStore> listFreeTheme(SysOriginPlatformEnum sysOrigin);

  /**
   * 获取一组指定的资源编号.
   *
   * @param ids id集合
   * @return list
   */
  List<Long> listSourceIdByIds(List<Long> ids);

  /**
   * 获取资源编号.
   *
   * @param id id
   * @return ignore
   */
  Long getSourceIdById(Long id);

  /**
   * 获取平台指定资源.
   */
  PropsCommodityStore getSourceIdById(String sysOrigin, Long sourceId);

  /**
   * 根据资源id商品信息.
   */
  PropsCommodityStore getBySourceId(SysOriginPlatformEnum sysOrigin, Long sourceId,
      String propsType);

}
