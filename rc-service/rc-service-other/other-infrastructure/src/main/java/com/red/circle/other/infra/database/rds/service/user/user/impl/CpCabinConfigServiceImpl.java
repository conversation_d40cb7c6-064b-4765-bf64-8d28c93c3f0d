package com.red.circle.other.infra.database.rds.service.user.user.impl;


import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.user.user.CpCabinConfigDAO;
import com.red.circle.other.infra.database.rds.entity.user.user.CpCabinConfig;
import com.red.circle.other.infra.database.rds.service.user.user.CpCabinConfigService;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * CP小屋配置 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2023-11-20 10:18
 */

@Service
@AllArgsConstructor
public class CpCabinConfigServiceImpl extends
    BaseServiceImpl<CpCabinConfigDAO, CpCabinConfig> implements
    CpCabinConfigService {

  @Override
  public List<CpCabinConfig> getCpCabinConfig(String sysOriginName) {
    return query()
        .eq(CpCabinConfig::getSysOrigin, sysOriginName)
        .orderByAsc(CpCabinConfig::getCabinUnlockValue)
        .last(PageConstant.formatLimit(20))
        .list();
  }
}
