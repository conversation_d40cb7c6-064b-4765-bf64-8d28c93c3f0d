package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.red.circle.framework.mybatis.entity.TimestampBaseEntity;
import java.io.Serial;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户最新设备信息.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("user_latest_mobile_device")
public class LatestMobileDevice extends TimestampBaseEntity implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户id.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 语言.
   */
  @TableField("language")
  private String language;

  /**
   * 时区.
   */
  @TableField("zone_id")
  private String zoneId;

  /**
   * 最近登录ip.
   */
  @TableField("ip")
  private String ip;

  /**
   * 系统来源.
   */
  @TableField("imei")
  private String imei;

  /**
   * 请求平台.
   */
  @TableField("request_client")
  private String requestClient;

  /**
   * 手机型号.
   */
  @TableField("phone_model")
  private String phoneModel;

  /**
   * 手机操作系统.
   */
  @TableField("phone_sys_version")
  private String phoneSysVersion;

  /**
   * 编译版本.
   */
  @TableField("build_version")
  private Integer buildVersion;

  /**
   * app版本.
   */
  @TableField("app_version")
  private String appVersion;

  /**
   * 来源系统平台.
   */
  @TableField("sys_origin")
  private String sysOrigin;

}
