package com.red.circle.other.infra.database.rds.service.team.impl;

import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.team.BillDiamondBalanceDetailsDAO;
import com.red.circle.other.infra.database.rds.entity.team.BillDiamondBalanceDetails;
import com.red.circle.other.infra.database.rds.service.team.BillDiamondBalanceDetailsService;
import com.red.circle.tool.core.collection.CollectionUtils;
import com.red.circle.tool.core.date.TimestampUtils;
import com.red.circle.tool.core.sequence.IdWorkerUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>
 * 用户账单钻石余额明细 服务实现类.
 * </p>
 *
 * <AUTHOR> on 2024-05-28 18:08
 */
@Service
public class BillDiamondBalanceDetailsServiceImpl extends
    BaseServiceImpl<BillDiamondBalanceDetailsDAO, BillDiamondBalanceDetails> implements
    BillDiamondBalanceDetailsService {

    @Override
    public BillDiamondBalanceDetails getEntityByBillBelongAndUserId(String sysOrigin, Long userId, Integer billBelongDay) {
        return query()
                .eq(BillDiamondBalanceDetails::getSysOrigin, sysOrigin)
                .eq(BillDiamondBalanceDetails::getUserId, userId)
                .eq(BillDiamondBalanceDetails::getBillBelongDay, billBelongDay)
                .last(PageConstant.LIMIT_ONE)
                .getOne();
    }

    @Override
    public BillDiamondBalanceDetails getEntityByBillBelongAndUserIdAndBillBelongDay(String sysOrigin, Long userId, Integer billBelongDay) {
        return query()
                .eq(BillDiamondBalanceDetails::getSysOrigin, sysOrigin)
                .eq(BillDiamondBalanceDetails::getUserId, userId)
                .eq(BillDiamondBalanceDetails::getBillBelongDay, billBelongDay)
                .last(PageConstant.LIMIT_ONE)
                .getOne();
    }

    @Override
    public List<BillDiamondBalanceDetails> getEntityByBillBelongAndTeamId(String sysOrigin, Integer billBelong, Long teamId) {
        return query()
                .eq(BillDiamondBalanceDetails::getSysOrigin, sysOrigin)
                .eq(BillDiamondBalanceDetails::getTeamId, teamId)
                .eq(BillDiamondBalanceDetails::getBillBelong, billBelong)
                .list();
    }

    @Override
    public void add(BillDiamondBalanceDetails param) {
        BillDiamondBalanceDetails details = this.getEntityByBillBelongAndUserId(param.getSysOrigin(), param.getUserId(), param.getBillBelongDay());
        if (Objects.isNull(details)) {
            param.setId(IdWorkerUtils.getId());
            param.setCreateTime(TimestampUtils.now());
            param.setCreateUser(0L);
            save(param);
        } else {
            if (param.getEarnPoints().compareTo(details.getEarnPoints()) > 0) {
                details.setUpdateTime(TimestampUtils.now());
                details.setUpdateUser(0L);
                details.setBalance(param.getBalance().subtract(details.getConsumptionPoints()));
                details.setEarnPoints(param.getEarnPoints());
                saveOrUpdate(details);
            }
        }
    }

    @Override
    public void addAgent(BillDiamondBalanceDetails param) {
        BillDiamondBalanceDetails details = this.getEntityByBillBelongAndUserId(param.getSysOrigin(), param.getUserId(), param.getBillBelongDay());
        if (Objects.isNull(details)) {
            param.setId(IdWorkerUtils.getId());
            param.setCreateTime(TimestampUtils.now());
            param.setCreateUser(0L);
            save(param);
        } else {
            if (param.getAgentEarnPoints().compareTo(details.getAgentEarnPoints()) > 0) {
                details.setUpdateTime(TimestampUtils.now());
                details.setUpdateUser(0L);
                details.setAgentBalance(param.getAgentBalance().subtract(details.getAgentConsumptionPoints()));
                details.setAgentEarnPoints(param.getAgentEarnPoints());
                saveOrUpdate(details);
            }
        }
    }

    @Override
    public BillDiamondBalanceDetails getBalance(Long userId, Integer billBelong) {
        return query()
                .eq(BillDiamondBalanceDetails::getUserId, userId)
                .eq(BillDiamondBalanceDetails::getBillBelong, billBelong)
                .getOne();
    }

    @Override
    public boolean exists(Long userId, Integer billBelong) {
        return Optional.ofNullable(
                        query()
                            .eq(BillDiamondBalanceDetails::getUserId, userId)
                            .eq(BillDiamondBalanceDetails::getBillBelong, billBelong)
                            .last(PageConstant.LIMIT_ONE).getOne())
            .map(BillDiamondBalanceDetails -> Objects.nonNull(BillDiamondBalanceDetails.getId()))
            .orElse(Boolean.FALSE);
    }

    @Override
    public BillDiamondBalanceDetails decr(Long userId, BigDecimal amount, Integer billBelong) {
        if (exists(userId, billBelong)) {
            update()
                    .setSql("consumption_points=consumption_points+" + amount)
                    .setSql("balance=balance-" + amount)
                    .set(BillDiamondBalanceDetails::getUpdateUser, userId)
                    .set(BillDiamondBalanceDetails::getUpdateTime, TimestampUtils.now())
                    .eq(BillDiamondBalanceDetails::getUserId, userId)
                    .eq(BillDiamondBalanceDetails::getBillBelong, billBelong)
                    .last(PageConstant.LIMIT_ONE)
                    .execute();
        }
        return getBalance(userId, billBelong);
    }

    @Override
    public BillDiamondBalanceDetails decrAgent(Long userId, BigDecimal amount, Integer billBelong) {
        if (exists(userId, billBelong)) {
            update()
                    .setSql("agent_consumption_points=agent_consumption_points+" + amount)
                    .setSql("agent_balance=agent_balance-" + amount)
                    .set(BillDiamondBalanceDetails::getUpdateUser, userId)
                    .set(BillDiamondBalanceDetails::getUpdateTime, TimestampUtils.now())
                    .eq(BillDiamondBalanceDetails::getUserId, userId)
                    .eq(BillDiamondBalanceDetails::getBillBelong, billBelong)
                    .last(PageConstant.LIMIT_ONE)
                    .execute();
        }
        return getBalance(userId, billBelong);
    }

    @Override
    public BillDiamondBalanceDetails getBalanceByBillBelongDay(Long userId, Integer billBelongDay) {
        return query()
                .eq(BillDiamondBalanceDetails::getUserId, userId)
                .eq(BillDiamondBalanceDetails::getBillBelongDay, billBelongDay)
                .getOne();
    }

    @Override
    public boolean existsBillBelongDay(Long userId, Integer billBelongDay) {
        return Optional.ofNullable(
                    query()
                        .eq(BillDiamondBalanceDetails::getUserId, userId)
                        .eq(BillDiamondBalanceDetails::getBillBelongDay, billBelongDay)
                        .last(PageConstant.LIMIT_ONE).getOne())
            .map(BillDiamondBalanceDetails -> Objects.nonNull(BillDiamondBalanceDetails.getId()))
            .orElse(Boolean.FALSE);
    }

    @Override
    public BillDiamondBalanceDetails decrBillBelongDay(Long userId, BigDecimal amount, Integer billBelongDay) {
        if (existsBillBelongDay(userId, billBelongDay)) {
            update()
                .setSql("consumption_points=consumption_points+" + amount)
                .setSql("balance=balance-" + amount)
                .set(BillDiamondBalanceDetails::getUpdateUser, userId)
                .set(BillDiamondBalanceDetails::getUpdateTime, TimestampUtils.now())
                .eq(BillDiamondBalanceDetails::getUserId, userId)
                .eq(BillDiamondBalanceDetails::getBillBelongDay, billBelongDay)
                .last(PageConstant.LIMIT_ONE)
                .execute();
        }
        return getBalanceByBillBelongDay(userId, billBelongDay);
    }

    @Override
    public BillDiamondBalanceDetails decrAgentBillBelongDay(Long userId, BigDecimal amount, Integer billBelongDay) {
        if (existsBillBelongDay(userId, billBelongDay)) {
            update()
                .setSql("agent_consumption_points=agent_consumption_points+" + amount)
                .setSql("agent_balance=agent_balance-" + amount)
                .set(BillDiamondBalanceDetails::getUpdateUser, userId)
                .set(BillDiamondBalanceDetails::getUpdateTime, TimestampUtils.now())
                .eq(BillDiamondBalanceDetails::getUserId, userId)
                .eq(BillDiamondBalanceDetails::getBillBelongDay, billBelongDay)
                .last(PageConstant.LIMIT_ONE)
                .execute();
        }
        return getBalanceByBillBelongDay(userId, billBelongDay);
    }

    @Override
    public void removeBatchByTeamIds(List<Long> teamIds, Integer billBelong) {
        if (CollectionUtils.isEmpty(teamIds)) {
            return;
        }
        delete()
                .eq(BillDiamondBalanceDetails::getBillBelong, billBelong)
                .in(BillDiamondBalanceDetails::getTeamId, teamIds)
                .execute();
    }

    @Override
    public void removeByUserId(Long userId, Integer billBelong) {
        delete()
                .eq(BillDiamondBalanceDetails::getBillBelong, billBelong)
                .in(BillDiamondBalanceDetails::getUserId, userId)
                .execute();
    }


}
