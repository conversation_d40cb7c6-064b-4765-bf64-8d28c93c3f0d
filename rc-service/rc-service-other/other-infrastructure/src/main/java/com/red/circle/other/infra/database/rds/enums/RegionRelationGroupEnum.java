package com.red.circle.other.infra.database.rds.enums;

import com.red.circle.tool.core.text.StringPool;
import java.util.Arrays;
import java.util.Objects;

/**
 * <p>
 * 区域业务关系分组类型.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-01
 */
public enum RegionRelationGroupEnum {

  /**
   * 开通支付国家.
   */
  OPEN_PAY_COUNTRY("开通支付国家"),

  ;

  private final String desc;

  RegionRelationGroupEnum(String desc) {
    this.desc = desc;
  }

  public static String getDescValue(String name) {
    return Arrays.stream(RegionRelationGroupEnum.values())
        .filter(propsTypeEnum -> Objects.equals(propsTypeEnum.name(), name))
        .findFirst()
        .map(RegionRelationGroupEnum::getDesc)
        .orElse(StringPool.UNKNOWN);
  }

  public String getDesc() {
    return desc;
  }

}
