package com.red.circle.other.infra.database.rds.service.game.impl;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.game.GameRoomSudRecordDAO;
import com.red.circle.other.infra.database.rds.entity.game.GameRoomSudRecord;
import com.red.circle.other.infra.database.rds.service.game.GameRoomSudRecordService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间sud游戏记录 服务实现类.
 * </p>
 *
 */
@Service
@AllArgsConstructor
public class GameRoomSudRecordServiceImpl extends
        BaseServiceImpl<GameRoomSudRecordDAO, GameRoomSudRecord> implements GameRoomSudRecordService {

    @Override
    public GameRoomSudRecord getRoomIdSud(Long roomId) {
        return query()
                .eq(GameRoomSudRecord::getRoomId,roomId)
                .last(PageConstant.LIMIT_ONE)
                .getOne();
    }

    @Override
    public void dalRoomSudRecord(Long roomId) {
        delete().eq(GameRoomSudRecord::getRoomId,roomId).execute();

    }
}



