package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserConfessionRecord;
import com.red.circle.other.inner.model.cmd.user.UserConfessionRecordQryCmd;
import java.util.List;
import org.mapstruct.control.MappingControl.Use;

/**
 * <p>
 * 告白记录 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-11-15 17:23
 */
public interface UserConfessionRecordService extends BaseService<UserConfessionRecord> {

  PageResult<UserConfessionRecord> pageListQuery(UserConfessionRecordQryCmd query);

  List<UserConfessionRecord> listConfessionRecordFloat(String regionCode);

  List<UserConfessionRecord> getUserConfessionRecord(String regionCode, <PERSON><PERSON><PERSON> self, Long userId,
      Long lastId);

}
