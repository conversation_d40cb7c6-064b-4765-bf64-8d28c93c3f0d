package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.google.common.collect.Lists;
import com.red.circle.common.business.core.SysConfigEnum;
import com.red.circle.framework.core.asserts.ResponseAssert;
import com.red.circle.framework.core.response.CommonErrorCode;
import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.constant.PageConstant;
import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.EnumConfigDAO;
import com.red.circle.other.infra.database.rds.entity.sys.EnumConfig;
import com.red.circle.other.infra.database.rds.service.sys.EnumConfigService;
import com.red.circle.other.inner.enums.config.EnumConfigKey;
import com.red.circle.other.inner.model.cmd.sys.SysEnumConfigQryCmd;
import com.red.circle.other.inner.model.dto.sys.SysEnumConfigSortDTO;
import com.red.circle.tool.core.text.StringPool;
import com.red.circle.tool.core.text.StringUtils;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统枚举配置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-30
 */
@Service
public class EnumConfigServiceImpl extends BaseServiceImpl<EnumConfigDAO, EnumConfig> implements
    EnumConfigService {

  @Override
  public List<EnumConfig> listReturnAppConfig(String sysOrigin) {
    return query()
        .eq(EnumConfig::getReturnApp, Boolean.TRUE)
        .eq(EnumConfig::getSysOrigin, sysOrigin)
        .list();
  }

  @Override
  public String getValue(EnumConfigKey key, String sysOrigin) {
    EnumConfig enumConfig = query().select(EnumConfig::getVal)
        .eq(EnumConfig::getName, key)
        .eq(EnumConfig::getSysOrigin, sysOrigin)
        .last(PageConstant.LIMIT_ONE)
        .getOne();

    if (Objects.isNull(enumConfig)) {
      return StringPool.EMPTY;
    }

    return enumConfig.getVal();
  }

  @Override
  public EnumConfig getByEnumConfig(EnumConfigKey key, String sysOrigin) {
    return query()
        .eq(EnumConfig::getName, key)
        .eq(EnumConfig::getSysOrigin, sysOrigin)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public EnumConfig getByEnumConfig(SysConfigEnum sysConfigEnum, String sysOrigin) {
    return query()
        .eq(EnumConfig::getSysOrigin, sysOrigin)
        .eq(EnumConfig::getName, sysConfigEnum.getKey())
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public EnumConfig getByEnumConfig(String key, String sysOrigin) {
    return query()
        .eq(EnumConfig::getName, key)
        .eq(EnumConfig::getSysOrigin, sysOrigin)
        .last(PageConstant.LIMIT_ONE)
        .getOne();
  }

  @Override
  public PageResult<EnumConfig> pageSysEnumConfig(SysEnumConfigQryCmd query) {
    return query()
        .eq(StringUtils.isNotBlank(query.getGroupName()), EnumConfig::getGroupName,
            query.getGroupName())
        .eq(Objects.nonNull(query.getInoperable()), EnumConfig::getInoperable,
            query.getInoperable())
        .eq(StringUtils.isNotBlank(query.getName()), EnumConfig::getName,
            query.getName())
        .eq(StringUtils.isNotBlank(query.getSysOrigin()), EnumConfig::getSysOrigin,
            query.getSysOrigin())
        .eq(StringUtils.isNotBlank(query.getVal()), EnumConfig::getVal,
            query.getVal())
        .like(StringUtils.isNotBlank(query.getDescription()), EnumConfig::getDescription,
            query.getDescription())
        .orderByDesc(EnumConfig::getCreateTime)
        .page(query.getPageQuery());
  }

  @Override
  public List<EnumConfig> sysEnumConfigListByGroupName(String groupName) {
    return Optional.ofNullable(query()
            .eq(EnumConfig::getGroupName, groupName)
            .orderByDesc(EnumConfig::getSort).list())
        .orElse(Lists.newArrayList());
  }

  @Override
  public String getViolationAvatar(EnumConfigKey violationPicture) {
    EnumConfig sysEnumConfig = query()
        .eq(EnumConfig::getName, violationPicture.name())
        .last(PageConstant.LIMIT_ONE).getOne();
    return Optional.ofNullable(sysEnumConfig).map(EnumConfig::getVal)
        .orElse("");
  }

  @Override
  public void deleteBySysOriginAndName(String name, String sysOrigin) {
    ResponseAssert.isTrue(CommonErrorCode.DELETE_FAILURE, delete()
        .eq(EnumConfig::getName, name)
        .eq(EnumConfig::getSysOrigin, sysOrigin)
        .last(PageConstant.LIMIT_ONE)
        .execute());
  }

  @Override
  public void updateSort(List<SysEnumConfigSortDTO> params) {
    baseDAO.updateSort(params);
  }


}
