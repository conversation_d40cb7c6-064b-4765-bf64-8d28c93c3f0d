package com.red.circle.other.infra.database.rds.service.user.user;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.UserSubscription;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 用户关注列表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-20
 */
public interface UserSubscriptionService extends BaseService<UserSubscription> {

  /**
   * 移除订阅.
   *
   * @param userId                   用户id
   * @param cancelSubscriptionUserId 订阅用户id
   * @return ignore
   */
  boolean remove(Long userId, Long cancelSubscriptionUserId);

  /**
   * 设置为互相订阅用户.
   *
   * @param userId          用户id
   * @param subUserId       订阅用户id
   * @param mutualRelations 是否互相订阅
   */
  void updateMutualRelations(Long userId, Long subUserId, Boolean mutualRelations);

  /**
   * 添加.
   *
   * @param userId             用户id
   * @param subscriptionUserId 订阅用户id
   * @return ignore
   */
  boolean notExistsAdd(Long userId, Long subscriptionUserId);


  /**
   * 获取用户订阅列表.
   *
   * @param userId ignore
   * @param lastId ignore
   * @return ignore
   */
  List<UserSubscription> listByUserId(Long userId, Long lastId);

  /**
   * 获取订阅我的用于列表.
   *
   * @param userId ignore
   * @param lastId ignore
   * @return ignore
   */
  List<UserSubscription> getBySubscriptionUserId(Long userId, Long lastId);

  /**
   * 检测用户是否订阅.
   *
   * @param userId             ignore
   * @param subscriptionUserId ignore
   * @return ignore
   */
  boolean checkSubscription(Long userId, Long subscriptionUserId);

  /**
   * 检查用户相互订阅状态.
   *
   * @param userId             用户id
   * @param subscriptionUserId 订阅用户id
   * @return 用户id map
   */
  Map<Long, Boolean> checkMutualSubscription(Long userId, Long subscriptionUserId);

  /**
   * 获取用户订阅关系.
   *
   * @param userId             用户id
   * @param subscriptionUserId 订阅用户id
   * @return ignore
   */
  UserSubscription getBySubscription(Long userId, Long subscriptionUserId);

  /**
   * 用户是否订阅指定用户集合.
   *
   * @param userId              用户id
   * @param subscriptionUserIds 订阅用户
   * @return ignore
   */
  Map<Long, Boolean> mapIsSubscription(Long userId, Set<Long> subscriptionUserIds);

  /**
   * 获取订阅我的用于列表.
   *
   * @param userId ignore
   * @return ignore
   */
  List<UserSubscription> getAllBySubscriptionUserId(Long userId);

}
