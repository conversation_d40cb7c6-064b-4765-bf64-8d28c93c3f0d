package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.common.business.core.enums.GameStateEnum;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.GameIndoorTeamPkRecord;
import java.sql.Timestamp;
import java.util.List;

/**
 * <p>
 * 房间内团队PK 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface GameIndoorTeamPkRecordService extends BaseService<GameIndoorTeamPkRecord> {

  /**
   * 根据id与状态获得PK信息
   */
  GameIndoorTeamPkRecord getByIdByStatus(Long id, GameStateEnum gameStateEnum);

  /**
   * 根据roomId与状态获得PK信息
   */
  GameIndoorTeamPkRecord getByRoomIdByStatus(Long roomId, GameStateEnum gameStateEnum);

  /**
   * 根据roomId与状态获得有效的PK信息
   */
  GameIndoorTeamPkRecord getEfficientPkByRoomId(Long roomId);

  /**
   * 是否有资格创建游戏.
   */
  Boolean checkCreateGame(Long userId);

  /**
   * 根据id修改状态为主动结束
   */
  void updateGameOverById(Long id);

  /**
   * 校验是否有资格开始游戏
   *
   * @param userId 用户id
   * @param pkId   pkID
   * @return true有资格
   */
  Boolean checkStartGame(Long userId, Long pkId);

  /**
   * 查询当前时间大于结束时间(+1分钟 处理数据时间) ，且还在决斗中状态的PK记录
   */
  List<GameIndoorTeamPkRecord> listExpiredStartedRecordByNow(Timestamp nowTime);

  /**
   * 查询当前时间大于创建时间(+3分钟:等待时间， +1分钟 处理数据时间) ，且还在等待状态的PK记录
   */
  List<GameIndoorTeamPkRecord> listExpiredWaitingRecordByNow(Timestamp nowTime);

  /**
   * 是否有权限创建游戏
   *
   * @return true有权限 false无
   */
  Boolean isCanGame(Long userId, Long roomId);

}
