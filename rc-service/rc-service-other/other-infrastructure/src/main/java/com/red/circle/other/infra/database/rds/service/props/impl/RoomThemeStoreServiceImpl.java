package com.red.circle.other.infra.database.rds.service.props.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.props.RoomThemeStoreDAO;
import com.red.circle.other.infra.database.rds.entity.props.RoomThemeStore;
import com.red.circle.other.infra.database.rds.service.props.RoomThemeStoreService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 房间主题 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-15
 */
@Service
public class RoomThemeStoreServiceImpl extends
    BaseServiceImpl<RoomThemeStoreDAO, RoomThemeStore> implements RoomThemeStoreService {

}
