package com.red.circle.other.infra.database.rds.service.sys.impl;

import com.red.circle.framework.mybatis.service.impl.BaseServiceImpl;
import com.red.circle.other.infra.database.rds.dao.sys.OauthRegisterDAO;
import com.red.circle.other.infra.database.rds.entity.sys.OauthRegister;
import com.red.circle.other.infra.database.rds.service.sys.OauthRegisterService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统oauth 服务实现类.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Service
public class OauthRegisterServiceImpl extends
    BaseServiceImpl<OauthRegisterDAO, OauthRegister> implements OauthRegisterService {

}
