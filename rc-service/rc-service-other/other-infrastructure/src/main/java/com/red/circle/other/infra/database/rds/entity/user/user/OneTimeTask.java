package com.red.circle.other.infra.database.rds.entity.user.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 用户只可触发一次的任务.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("user_one_time_task")
public class OneTimeTask implements Serializable {

  @Serial
  private static final long serialVersionUID = 1L;

  /**
   * 主键标识.
   */
  @TableId(value = "id", type = IdType.ASSIGN_ID)
  private Long id;

  /**
   * 用户.
   */
  @TableField("user_id")
  private Long userId;

  /**
   * 来源系统.
   */
  @TableField("sys_origin")
  private String sysOrigin;

  /**
   * 奖励数量.
   */
  @TableField("reward_quantity")
  private BigDecimal rewardQuantity;

  /**
   * 是否已领取奖励 1.已领取 0.未领取.
   */
  @TableField("award_status")
  private Boolean awardStatus;

  /**
   * 任务类型(FIRST_CHARGE).
   */
  @TableField("task_type")
  private String taskType;

  /**
   * 创建时间.
   */
  @TableField("create_time")
  private LocalDateTime createTime;

  /**
   * 修改时间.
   */
  @TableField("update_time")
  private LocalDateTime updateTime;

  /**
   * 创建用户.
   */
  @TableField("create_user")
  private Long createUser;

  /**
   * 修改用户.
   */
  @TableField("update_user")
  private Long updateUser;


}
