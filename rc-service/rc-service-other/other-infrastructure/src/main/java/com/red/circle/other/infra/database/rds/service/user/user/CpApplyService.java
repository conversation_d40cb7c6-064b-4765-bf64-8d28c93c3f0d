package com.red.circle.other.infra.database.rds.service.user.user;


import com.red.circle.framework.dto.PageResult;
import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.user.user.CpApply;
import com.red.circle.other.infra.enums.user.user.CpApplyStatus;
import com.red.circle.other.inner.model.cmd.user.CpApplyQryCmd;
import java.util.List;

/**
 * <p>
 * CP申请记录 服务类.
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-13
 */
public interface CpApplyService extends BaseService<CpApply> {

  /**
   * 解散cp.
   */
  void dismiss(Long requestUserId);

  /**
   * 获取申请记录.
   *
   * @param acceptApplyUserId 接收人
   * @param sendApplyUserId   发送人
   * @return 申请记录ID.
   */
  Long getId(Long acceptApplyUserId, Long sendApplyUserId);

  /**
   * 申请记录是否存在.
   *
   * @param acceptApplyUserId 接收人
   * @param sendApplyUserId   发送人
   * @return true　存在，false 不存在
   */
  boolean exists(Long acceptApplyUserId, Long sendApplyUserId);

  /**
   * 是否存在发送记录.
   *
   * @param sendApplyUserId 发送人
   * @return true 存在，false 不存在
   */
  boolean existsNotProcessSendApply(Long sendApplyUserId);

  /**
   * 改变状态同意.
   *
   * @param id     记录id
   * @param status 状态
   * @return true 成功，false 失败
   */
  boolean changeStatusById(Long id, CpApplyStatus status);

  /**
   * 拒绝所有等待状态在用户.
   *
   * @param acceptApplyUserId 接收用户
   */
  void refuseStatusWait(Long acceptApplyUserId);

  /**
   * 获取我拒绝都信息.
   */
  List<CpApply> listRefuse(Long userId);

  /**
   * 获取cp申请信息.
   */
  PageResult<CpApply> pageCpApply(CpApplyQryCmd query);
}
