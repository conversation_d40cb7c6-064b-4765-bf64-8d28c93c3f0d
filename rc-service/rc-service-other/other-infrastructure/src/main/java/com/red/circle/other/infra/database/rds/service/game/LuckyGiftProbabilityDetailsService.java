package com.red.circle.other.infra.database.rds.service.game;

import com.red.circle.framework.mybatis.service.BaseService;
import com.red.circle.other.infra.database.rds.entity.game.LuckyGiftProbabilityDetails;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 礼物规格概率详情 服务类.
 * </p>
 *
 * <AUTHOR> on 2023-06-16 17:14
 */
public interface LuckyGiftProbabilityDetailsService extends
    BaseService<LuckyGiftProbabilityDetails> {

  /**
   * 根据概率基础配置id获得最大倍数.
   */
  Integer getMaxMultiple(Long probabilityId);

  /**
   * 根据概率基础配置id获得最大倍数.
   */
  Map<Long, Integer> mapMaxMultiple(Set<Long> probabilityIds);

  /**
   * 获得概率明细.
   */
  List<LuckyGiftProbabilityDetails> listByProbabilityId(Long probabilityId);

  void deleteByProbabilityIds(Set<Long> probabilityIds);

  Map<Long, List<LuckyGiftProbabilityDetails>> getLuckyGiftProbabilityDetails(String sysOrigin);

  List<LuckyGiftProbabilityDetails> getDetailByProbabilityId(Long id);

  void add(Long id, List<LuckyGiftProbabilityDetails> gameLuckyGiftProbabilityDetails);
}
