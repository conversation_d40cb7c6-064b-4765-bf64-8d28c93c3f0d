<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.red.circle.other.infra.database.rds.dao.approval.ApprovalUserSettingDataDAO">
  <select id="pageUserProfileApproval"
    resultType="com.red.circle.other.inner.model.dto.approval.ApprovalUserProfileDTO">
    SELECT
    ubi.user_avatar AS userAvatar,
    ubi.user_nickname AS userNickname,
    ubi.age AS age,
    ubi.user_sex AS userSex,
    ausd.user_id AS userId,
    ausd.machine_label AS machineLabel,
    ausd.update_time AS updateTime,
    ausd.update_user AS updateUser,
    ausd.sys_origin AS sysOrigin
    FROM
    approval_user_setting_data ausd
    INNER JOIN user_base_info ubi ON ausd.user_id = ubi.id
    WHERE ausd.approve_type = #{query.approveType}
    AND ausd.approve_status = #{query.approveStatus}
    <if test="query.sysOrigin != null and query.sysOrigin != '' ">
      AND ausd.sys_origin LIKE CONCAT(#{query.sysOrigin},'%')
    </if>
    <if test="query.userSex != null">
      AND ubi.user_sex = #{query.userSex}
    </if>
    <if test="query.userId != null">
      AND ubi.id = #{query.userId}
    </if>
    <if test="query.startDateTime != null">
      AND ausd.update_time >= #{query.startDateTime}
    </if>
    <if test="query.endDateTime != null">
      AND ausd.update_time &lt; #{query.endDateTime}
    </if>
  </select>
</mapper>
