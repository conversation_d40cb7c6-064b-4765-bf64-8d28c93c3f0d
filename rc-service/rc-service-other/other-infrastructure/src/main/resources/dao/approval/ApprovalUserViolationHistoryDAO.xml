<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.red.circle.other.infra.database.rds.dao.approval.ApprovalUserViolationHistoryDAO">

  <select id="pageUserDetailsPhotoWall"
    resultType="com.red.circle.other.inner.model.dto.approval.ApprovalUserPhotoWallDTO">
    SELECT h.id , h.user_id AS userId, h.content AS resourceUrl ,h.label_names AS
    labelNames
    FROM approval_user_violation_history h
    INNER JOIN user_base_info u ON u.id=h.user_id
    where h.approval_result='NOT_PASS' and u.user_type=0 and u.is_del=0
    <if test="type != null">
      AND h.violation_type = #{type}
    </if>
    <if test="query.userId != null">
      AND h.user_id = #{query.userId }
    </if>
    <if test="query.userNickname != null and query.userNickname !=''">
      AND INSTR(u.user_nickname,#{query.userNickname})
    </if>
    <if test="query.userSex != null">
      AND u.user_sex = #{query.userSex}
    </if>
    <if test="query.startCreateDate != null">
      AND h.create_time >= #{query.startCreateDate}
    </if>
    <if test="query.endCreateDate != null">
      AND h.create_time &lt;= #{query.endCreateDate}
    </if>
    ORDER BY h.create_time DESC
  </select>
</mapper>
