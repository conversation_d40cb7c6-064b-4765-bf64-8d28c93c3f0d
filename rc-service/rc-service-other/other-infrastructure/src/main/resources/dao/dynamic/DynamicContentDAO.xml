<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.dynamic.DynamicContentDAO">

  <select id="pageDynamic"
    resultType="com.red.circle.other.inner.model.dto.dynamic.PageDynamicContentDTO">

    select dc.id, dc.sys_origin ,dc.create_user, dc.content ,
    if(std.dynamic_id is null, 0, 1) as top, std.weights, dc.create_time
    from dynamic_content as dc
    left join sys_top_dynamic as std on std.dynamic_id = dc.id

    <where>
      dc.is_del = 0
      <if test="query.sysOrigin != '' and query.sysOrigin != null">
        and dc.sys_origin = #{ query.sysOrigin }
      </if>
      <if test="query.userId != null">
        and dc.create_user = #{ query.userId }
      </if>
      <if test="query.top == true">
        and if(std.dynamic_id is null, 0, 1) = 1
      </if>
      <if test="query.top == false">
        and if(std.dynamic_id is null, 0, 1) = 0
      </if>
      <if test="query.startCreateDate != null">
        and dc.create_time BETWEEN #{query.startCreateDate} AND #{query.endCreateDate}
      </if>
    </where>
    <if test="query.top == true">
      order by std.weights desc
    </if>
    <if test="query.top != true">
      order by dc.create_time desc
    </if>

  </select>

</mapper>
