<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.dynamic.DynamicPictureDAO">

  <insert id="updateApprovalBySelective">

    <foreach close=";" collection="list" item="item" open="" separator=";">
      UPDATE `dynamic_picture` SET update_time = CURRENT_TIMESTAMP
      <if test="item.violation != null">
        ,`violation` = #{item.violation}
      </if>
      <if test="item.labelNames != null">
        ,`label_names` = #{item.labelNames}
      </if>
      WHERE id = #{item.id} AND `violation` IN('NORMAL','SUSPECTED')
    </foreach>

  </insert>

</mapper>
