<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.dynamic.DynamicCommentDAO">

  <select id="getCommentQuantity" resultType="java.lang.Long">

    select count(1)
    from dynamic_comment
    where dynamic_content_id = #{dynamicContentId} limit 1;

  </select>

</mapper>
