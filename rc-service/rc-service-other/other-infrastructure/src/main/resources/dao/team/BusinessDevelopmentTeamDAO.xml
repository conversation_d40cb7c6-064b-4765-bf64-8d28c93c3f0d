<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.team.BusinessDevelopmentTeamDAO">

  <select id="countAgentTotalTarget" resultType="java.math.BigDecimal">
    SELECT sum(rasm.gift_value)
    FROM room_business_development_team rbdt,
         room_anchor_statistics_month rasm
    WHERE rbdt.agent_id = rasm.agent_user_id
      AND rasm.date_number = #{dateNumber}
      AND rbdt.user_id = #{userId}
  </select>

</mapper>
