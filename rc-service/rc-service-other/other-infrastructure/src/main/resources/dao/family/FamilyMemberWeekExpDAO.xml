<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.red.circle.other.infra.database.rds.dao.family.FamilyMemberWeekExpDAO">
  <select id="pageByFamilyId"
    resultType="com.red.circle.other.infra.database.rds.dto.family.FamilyMemberExpDTO">
    SELECT fm.id AS id,
    fm.sys_origin AS sysOrigin,
    fm.family_id AS family_id,
    fm.member_user_id AS memberUserId,
    fm.member_role AS memberRole,
    fmwe.exp AS exp
    FROM `family_member` fm
    LEFT JOIN family_member_week_exp fmwe ON fm.`member_user_id`= fmwe.`family_member_id`
    AND fm.`family_id`= fmwe.`family_id`
    WHERE fm.`family_id` = #{familyId}
    AND fm.member_role IN
    <foreach collection="roles" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    ORDER BY fmwe.exp DESC
    LIMIT #{start}, #{end}
  </select>

  <select id="listByFamilyId" resultType="com.red.circle.other.infra.database.rds.dto.family.FamilyMemberExpDTO">
    SELECT fm.id AS id,
    fm.sys_origin AS sysOrigin,
    fm.family_id AS family_id,
    fm.member_user_id AS memberUserId,
    fm.member_role AS memberRole,
    fmwe.exp AS exp
    FROM `family_member` fm
    LEFT JOIN family_member_week_exp fmwe ON fm.`member_user_id`= fmwe.`family_member_id`
    AND fm.`family_id`= fmwe.`family_id`
    WHERE fm.`family_id`  = #{familyId}
    AND  fm.member_role IN
    <foreach collection="roles" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    ORDER BY fmwe.exp DESC
  </select>


</mapper>
