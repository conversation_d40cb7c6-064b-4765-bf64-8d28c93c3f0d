<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.red.circle.other.infra.database.rds.dao.family.FamilyBatterIntegralRecordDAO">

  <select id="listRanking"
    resultType="com.red.circle.other.infra.database.rds.entity.family.FamilyBatterIntegralRecord">
    select family_id, sum(integral) as integral
    from family_batter_integral_record
    where sys_origin = #{sysOrigin}
      AND DAYOFYEAR(CONVERT_TZ(create_time, 'GMT', '+03:00')) &lt;=
          DAYOFYEAR(CONVERT_TZ('2021-12-03 00:00:00', 'GMT', '+03:00'))
    group by family_id
    order by integral desc
  </select>


</mapper>
