<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.props.PropsBackpackDAO">

  <select id="getWearMultipleAvatarFrameUserIds" resultType="java.lang.Long">

    SELECT user_id
    FROM (SELECT COUNT(`user_id`) as nums, `user_id`
          FROM `user_props_backpack`
          WHERE `type` = 'AVATAR_FRAME'
            and `is_use_props` = 1
            and `expire_time` > now()
          GROUP BY `user_id`) as tab
    where tab.nums > 1;

  </select>

</mapper>
