<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.gift.GiftBackpackDAO">

  <select id="ticketCount"
    resultType="com.red.circle.other.inner.model.dto.game.GameLuckyBoxTicketCountInfoDTO">
    SELECT
    SUM(obtain_quantity) AS ticketCount,
    SUM(consume_quantity) AS consumeTicketCount
    FROM
    user_gift_backpack
    <where>
      <if test="giftId != null">
        AND gift_id = #{giftId}
      </if>
      <if test="userId != null">
        AND user_id = #{userId}
      </if>
    </where>
  </select>
</mapper>
