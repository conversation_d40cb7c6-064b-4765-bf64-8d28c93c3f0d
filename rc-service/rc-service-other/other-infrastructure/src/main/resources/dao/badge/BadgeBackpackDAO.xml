<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.badge.BadgeBackpackDAO">


  <select id="getUserMaxLevelFamilyBadge"
          resultType="com.red.circle.other.infra.database.rds.entity.badge.BadgeBackpack">
    SELECT
      *
    FROM
      sys_badge_config
    WHERE
      type = 'FAMILY'
      AND is_del = 0
      AND id IN

    ORDER BY
      CASE badge_key
        WHEN 'PLATINUM_IIIII' THEN 1
        WHEN 'PLATINUM_IIII' THEN 2
        WHEN 'PLATINUM_III' THEN 3
        WHEN 'PLATINUM_II' THEN 4
        WHEN 'PLATINUM_I' THEN 5
        WHEN 'GOLD_IIIII' THEN 6
        WHEN 'GOLD_IIII' THEN 7
        WHEN 'GOLD_III' THEN 8
        WHEN 'GOLD_II' THEN 9
        WHEN 'GOLD_I' THEN 10
        WHEN 'SILVER_IIIII' THEN 11
        WHEN 'SILVER_IIII' THEN 12
        WHEN 'SILVER_III' THEN 13
        WHEN 'SILVER_II' THEN 14
        WHEN 'SILVER_I' THEN 15
        WHEN 'BRONZE_IIIII' THEN 16
        WHEN 'BRONZE_IIII' THEN 17
        WHEN 'BRONZE_III' THEN 18
        WHEN 'BRONZE_II' THEN 19
        WHEN 'BRONZE_I' THEN 20
        WHEN 'BLACK_IRON' THEN 21
        ELSE 22
        END ASC;
  </select>
</mapper>
