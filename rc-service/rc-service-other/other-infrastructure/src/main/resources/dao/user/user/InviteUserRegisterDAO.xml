<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.user.user.InviteUserRegisterDAO">

  <select id="pageQuery"
    resultType="com.red.circle.other.infra.database.rds.entity.user.user.InviteUserRegister">

    SELECT uiur.*
    FROM user_invite_user_register uiur
    <where>
      <if test="query.sysOrigin != null and query.sysOrigin != ''">
        AND uiur.sys_origin = #{query.sysOrigin}
      </if>
      <if test="query.userId != null">
        AND uiur.user_id = #{query.userId}
      </if>
      <if test="query.inviteUserId != null">
        AND uiur.invite_user_id = #{query.inviteUserId}
      </if>
      <if test="query.startTime != null">
        AND uiur.create_time >= #{query.startTime}
      </if>
      <if test="query.endTime != null">
        AND uiur.create_time &lt; #{query.endTime}
      </if>
      <if test="query.countryCode != null and query.countryCode != ''">
        AND EXISTS (SELECT ubi.id FROM user_base_info ubi
        WHERE ubi.id = uiur.user_id AND ubi.country_code = #{query.countryCode})
      </if>
    </where>
    ORDER BY uiur.create_time DESC
  </select>

</mapper>
