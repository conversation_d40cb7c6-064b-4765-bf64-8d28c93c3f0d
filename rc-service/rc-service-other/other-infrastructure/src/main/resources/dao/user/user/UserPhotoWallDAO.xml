<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.user.user.UserPhotoWallDAO">


  <select id="pageUserDetailsPhotoWall"
    resultType="com.red.circle.other.inner.model.dto.user.UserPhotoWallDTO">
    SELECT ubi.*
    FROM user_base_info u
    INNER JOIN user_photo_wall ubi ON u.id = ubi.user_id
    where u.user_type=0 and u.is_del=0
    <if test="query.status != null">
      and ubi.violation = #{query.status}
    </if>
    <if test="query.userId != null">
      AND ubi.user_id = #{query.userId }
    </if>
    <if test="query.userNickname != null and query.userNickname !=''">
      AND INSTR(u.user_nickname,#{query.userNickname})
    </if>
    <if test="query.userSex != null">
      AND u.user_sex = #{query.userSex}
    </if>
    <if test="query.startCreateDate != null">
      AND ubi.create_time >= #{query.startCreateDate}
    </if>
    <if test="query.endCreateDate != null">
      AND ubi.create_time &lt;= #{query.endCreateDate}
    </if>
    ORDER BY ubi.create_time DESC
  </select>

</mapper>
