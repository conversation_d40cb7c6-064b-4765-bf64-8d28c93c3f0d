<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.user.user.UserGoldCoinRechargeDAO">

  <select id="listRechargeCoinsByUserIds"
    resultType="com.red.circle.other.inner.model.dto.user.UserGoldCoinRechargeDTO">
    SELECT
    user_id,
    gold_coin_source,
    sum( recharge_amount ) AS rechargeAmount
    FROM
    `user_gold_coin_recharge`
    <foreach collection="userIds" open="(" separator="," close=")" item="userId">
      #{userId}
    </foreach>
    GROUP BY
    user_id,
    gold_coin_source
  </select>
  <select id="getInviteTotalAmount" resultType="java.math.BigDecimal">
      SELECT sum(recharge_amount)
      FROM user_gold_coin_recharge
      where create_time BETWEEN #{qryCmd.startDate} and #{qryCmd.endDate}
        and user_id in (
          WITH RECURSIVE RecursiveCTE AS (
            SELECT user_id FROM user_invite_user WHERE invite_user_id = #{userId}
            UNION ALL
            SELECT tn.user_id FROM user_invite_user AS tn JOIN RecursiveCTE AS rc ON tn.invite_user_id = rc.user_id
          )
        SELECT user_id FROM RecursiveCTE
      );
  </select>
</mapper>