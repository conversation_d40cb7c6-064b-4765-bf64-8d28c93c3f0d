<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.user.user.RelationshipFriendDAO">
  <select id="findPetBeanTop"
    resultType="com.red.circle.other.inner.model.dto.user.PetRelationshipFriendDTO">
    SELECT urf.id,
           urf.user_id                                AS userId,
           urf.friend_user_id                         AS friendUserId,
           (ubb.earn_points - ubb.consumption_points) AS beanBalance
    FROM user_relationship_friend urf
           LEFT JOIN user_bean_balance ubb ON ubb.user_id = urf.friend_user_id
    WHERE urf.user_id = #{userId} LIMIT #{topSize}
  </select>
</mapper>
