<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.user.user.UserInviteUserDAO">

  <select id="listInviteStatisticsByUserIds"
          resultType="com.red.circle.other.inner.model.dto.user.UserInviteUserCountDTO">
<!--    SELECT iu.invite_user_id              AS userId,-->
<!--           COUNT(DISTINCT iu.user_id)     AS level1Invitation,-->
<!--           COALESCE(SUM(level2.count), 0) AS level2Invitation,-->
<!--           COALESCE(SUM(level3.count), 0) AS level3Invitation,-->
<!--           COUNT(DISTINCT iu.user_id) + COALESCE(SUM(level2.count), 0) +-->
<!--           COALESCE(SUM(level3.count), 0) AS totalInvitation-->
<!--    FROM user_invite_user iu-->
<!--           LEFT JOIN LATERAL ( SELECT COUNT(*) AS count-->
<!--                               FROM user_invite_user-->
<!--                               WHERE invite_user_id = iu.user_id ) AS level2-->
<!--                     ON TRUE-->
<!--           LEFT JOIN LATERAL (-->
<!--      SELECT COUNT(*) AS count-->
<!--      FROM user_invite_user u-->
<!--             INNER JOIN user_invite_user uiu ON u.user_id = uiu.invite_user_id-->
<!--      WHERE u.invite_user_id = iu.user_id-->
<!--      ) AS level3 ON TRUE-->
<!--    WHERE iu.invite_user_id IN-->
<!--    <foreach collection="userIds" open="(" separator="," close=")" item="userId">-->
<!--      #{userId}-->
<!--    </foreach>-->
<!--    AND iu.create_time BETWEEN #{qryCmd.startDate}-->
<!--      and #{qryCmd.endDate}-->
<!--    GROUP BY iu.invite_user_id-->

    SELECT  iu.invite_user_id              AS userId,
            COUNT(DISTINCT iu.user_id)     AS level1Invitation,
            COALESCE(SUM(level2.count), 0) AS level2Invitation,
            COALESCE(SUM(level3.count), 0) AS level3Invitation,
            COUNT(DISTINCT iu.user_id) + COALESCE(SUM(level2.count), 0) + COALESCE(SUM(level3.count), 0) AS totalInvitation
    FROM user_invite_user iu,
    LATERAL (
        SELECT COUNT(1) AS count FROM user_invite_user WHERE invite_user_id = iu.user_id
    ) AS level2 ,
    LATERAL (
        SELECT COUNT(1) AS count FROM user_invite_user u INNER JOIN user_invite_user uiu ON u.user_id = uiu.invite_user_id WHERE u.invite_user_id = iu.user_id
    ) AS level3
    WHERE iu.invite_user_id IN
    <foreach collection="userIds" open="(" separator="," close=")" item="userId">
      #{userId}
    </foreach>
    AND iu.create_time BETWEEN #{qryCmd.startDate} and #{qryCmd.endDate}
    GROUP BY iu.invite_user_id;
  </select>
</mapper>
