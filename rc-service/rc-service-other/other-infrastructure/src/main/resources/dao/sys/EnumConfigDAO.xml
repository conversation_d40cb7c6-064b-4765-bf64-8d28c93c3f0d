<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.sys.EnumConfigDAO">
  <update id="updateSort">
    <foreach collection="list" item="item" separator=";">
      UPDATE sys_enum_config_v2 SET sort=#{item.sort} WHERE id = #{item.id}
    </foreach>
  </update>
</mapper>
