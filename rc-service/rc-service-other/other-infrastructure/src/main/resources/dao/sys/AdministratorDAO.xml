<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.sys.AdministratorDAO">

  <select id="pageUserDetails"
    resultType="com.red.circle.other.inner.model.dto.sys.SysAdministratorInfoDTO">
    SELECT
    m.id,
    m.status,
    m.roles,
    ubi.id as userId,
    ubi.account,
    ubi.user_avatar AS userAvatar ,
    ubi.user_nickname AS userNickname ,
    ubi.user_sex AS userSex ,
    ubi.user_type AS userType,
    ubi.country_id AS countryId,
    ubi.country_name AS countryName,
    ubi.country_code AS countryCode,
    ubi.create_time AS createTime,
    ubi.account_status AS accountStatus
    FROM
    sys_administrator m
    LEFT JOIN user_base_info ubi ON ubi.id = m.user_id
    <where>
      <if test="query.userId != null">
        AND ubi.id = #{query.userId }
      </if>
      <if test="query.account != null">
        AND ubi.account = #{query.account }
      </if>
      <if test="query.userNickname != null and query.userNickname !=''">
        AND INSTR(ubi.user_nickname,#{query.userNickname})
      </if>
    </where>
    ORDER BY m.create_time DESC
  </select>
</mapper>
