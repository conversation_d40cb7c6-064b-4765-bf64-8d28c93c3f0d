<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.sys.AdministratorAuthDAO">

  <select id="existsAuth" resultType="java.lang.Boolean">
    SELECT 1 FROM sys_administrator_auth saa,sys_administrator_auth_resource saar
    WHERE saa.resource_id = saar.id AND saa.user_id = #{userId} AND saar.auth IN
    <foreach collection="authResources" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="listAuthResources" resultType="java.lang.String">
    SELECT saar.auth
    FROM sys_administrator_auth saa,
         sys_administrator_auth_resource saar
    WHERE saa.resource_id = saar.id
      AND saa.user_id = #{userId}
  </select>

</mapper>
