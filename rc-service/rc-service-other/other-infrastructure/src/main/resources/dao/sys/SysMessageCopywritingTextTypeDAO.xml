<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.red.circle.other.infra.database.rds.dao.sys.SysMessageCopywritingTextTypeDAO">
  <select id="findMsgTypePushCopywriting"
    resultType="com.red.circle.other.infra.database.rds.dto.MessageCopywritingDTO">
    SELECT sptc.id            AS id,
           sptc.type_id       AS typeId,
           sptt.`msg_type`    AS msgType,
           sptc.`language`,
           sptc.language_name AS languageName,
           sptc.title,
           sptc.content
    FROM sys_message_copywriting_type sptt,
         sys_message_copywriting_content sptc
    WHERE sptt.id = sptc.type_id
      AND sptt.msg_type = 'PUSH'
      AND sptt.business_scene = #{template}
      AND sptc.`language` = #{lang} LIMIT 1
  </select>

  <select id="findGreetRandomText"
    resultType="com.red.circle.other.infra.database.rds.dto.MessageCopywritingDTO">
    SELECT sptc.id            AS id,
           sptc.type_id       AS typeId,
           sptt.`msg_type`    AS msgType,
           sptc.`language`,
           sptc.language_name AS languageName,
           sptc.title,
           sptc.content
    FROM sys_message_copywriting_type sptt,
         sys_message_copywriting_content sptc
    WHERE sptt.id = sptc.type_id
      AND sptt.msg_type = 'GREET'
      AND sptc.`language` = #{lang}
    ORDER BY RAND() LIMIT 1
  </select>
</mapper>
