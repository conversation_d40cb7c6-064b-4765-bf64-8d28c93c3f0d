<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameRoomPkWinnerRecordDAO">

  <select id="weekTop"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkWinnerRecord">

    <if test="isLastWeek">
      SELECT user_id, sum(total_integral) AS total_integral
      FROM game_room_pk_winner_record
      WHERE `sys_origin` = #{sysOrigin} and pk_type != 'ROOM_VS_ROOM'
      AND YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) = YEARWEEK(CONVERT_TZ(now(),
      'GMT', '+03:00'), 1) - 1
      GROUP BY user_id
      ORDER BY total_integral DESC LIMIT 3;
    </if>

    <if test="!isLastWeek">
      SELECT user_id, sum(total_integral) AS total_integral
      FROM game_room_pk_winner_record
      WHERE `sys_origin` = #{sysOrigin} and pk_type != 'ROOM_VS_ROOM'
      AND YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) = YEARWEEK(CONVERT_TZ(now(),
      'GMT', '+03:00'), 1)
      GROUP BY user_id
      ORDER BY total_integral DESC LIMIT 13;
    </if>

  </select>

  <!-- 辅助:查询指定时间数据 -->
  <select id="listSpecifiedTimeWeekTop"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkWinnerRecord">

    SELECT user_id, sum(total_integral) AS total_integral
    FROM game_room_pk_winner_record
    WHERE `sys_origin` = #{sysOrigin}
      and pk_type != 'ROOM_VS_ROOM'
      AND create_time >= #{startTime}
      and create_time
     &lt; #{endTime}
    GROUP BY user_id
    ORDER BY total_integral DESC LIMIT 3;
  </select>

  <select id="dayTop"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkWinnerRecord">

    <if test="isYesterday">
      SELECT user_id, sum(total_integral) AS total_integral
      FROM game_room_pk_winner_record
      WHERE `sys_origin` = #{sysOrigin} and pk_type != 'ROOM_VS_ROOM'
      AND DAYOFYEAR(CONVERT_TZ(create_time, 'GMT', '+03:00')) = DAYOFYEAR(CONVERT_TZ(now(),
      'GMT', '+03:00')) - 1
      GROUP BY user_id
      ORDER BY total_integral DESC LIMIT 3;
    </if>

    <if test="!isYesterday">
      SELECT user_id, sum(total_integral) AS total_integral
      FROM game_room_pk_winner_record
      WHERE `sys_origin` = #{sysOrigin} and pk_type != 'ROOM_VS_ROOM'
      AND DAYOFYEAR(CONVERT_TZ(create_time, 'GMT', '+03:00')) = DAYOFYEAR(CONVERT_TZ(now(),
      'GMT', '+03:00'))
      GROUP BY user_id
      ORDER BY total_integral DESC LIMIT 13;
    </if>

  </select>

  <select id="weekSinglesKing"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkWinnerRecord">

    <if test="isLastWeek">
      SELECT user_id, integral
      FROM game_room_pk_winner_record
      WHERE `sys_origin` = #{sysOrigin} and pk_type != 'ROOM_VS_ROOM'
      AND YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) = YEARWEEK(CONVERT_TZ(now(),
      'GMT', '+03:00'), 1) - 1
      ORDER BY integral DESC
      limit 1;

    </if>

    <if test="!isLastWeek">
      SELECT user_id, integral
      FROM game_room_pk_winner_record
      WHERE `sys_origin` = #{sysOrigin} and pk_type != 'ROOM_VS_ROOM'
      AND YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) = YEARWEEK(CONVERT_TZ(now(),
      'GMT', '+03:00'), 1)
      ORDER BY integral DESC
      limit 1;
    </if>
  </select>

  <!-- 辅助:查询指定时间数据 -->
  <select id="getSpecifiedTimeWeekSinglesKing"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkWinnerRecord">

    SELECT user_id, integral
    FROM game_room_pk_winner_record
    WHERE `sys_origin` = #{sysOrigin}
      and pk_type != 'ROOM_VS_ROOM'
      AND create_time >= #{startTime}
      and create_time
     &lt; #{endTime}
    ORDER BY integral DESC limit 1;

  </select>

</mapper>
