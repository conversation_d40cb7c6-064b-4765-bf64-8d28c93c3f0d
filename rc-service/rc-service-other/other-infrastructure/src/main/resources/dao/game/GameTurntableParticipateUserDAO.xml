<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
  namespace="com.red.circle.other.infra.database.rds.dao.game.GameTurntableParticipateUserDAO">

  <select id="listTop20BySysOrigin"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameTurntableParticipateUser">
    select gtpu.user_id, SUM(gtpu.game_golds) AS game_golds
    from game_turntable_participate_user as gtpu
           left join user_base_info as ubi on ubi.id = gtpu.user_id
    where CONVERT_TZ(gtpu.create_time, 'GMT', '+03:00') >=
          '2021-12-02 00:00:00'
      AND CONVERT_TZ(gtpu.create_time, 'GMT', '+03:00') <![CDATA[ <= ]]>
    '2021-12-09 00:00:00'
      AND ubi.origin_sys = #{sysOrigin}
      AND gtpu.is_jackpot = 1
      AND gtpu.is_reimburse = 0
    GROUP BY gtpu.user_id
    ORDER BY game_golds DESC LIMIT 20
  </select>

</mapper>
