<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameKingActivityRecordDAO">

  <select id="listFiveWeeksRounds" resultType="java.lang.Long">

    select rounds
    from game_king_activity_record
    where sys_origin = #{sysOrigin}
      and process = 6
    order by rounds desc limit 5

  </select>


</mapper>
