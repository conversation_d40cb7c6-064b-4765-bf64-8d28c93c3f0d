<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameUserLotteryRecordDAO">

  <select id="listTop20"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameUserLotteryRecord">

    select sum(lottery_fee) as lottery_fee, user_id
    from game_user_lottery_record
    where sys_origin = #{sysOrigin}
      and game_type = #{gameType}
      and CONVERT_TZ(create_time, 'GMT', '+03:00') >=
          DATE_SUB(CONVERT_TZ(now(), 'GMT', '+03:00'), INTERVAL 24 HOUR)
      and CONVERT_TZ(create_time, 'GMT', '+03:00') &lt;= CONVERT_TZ(now(), 'GMT', '+03:00')
    group by user_id
    order by lottery_fee desc limit 20
  </select>

  <select id="listByUserId"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameUserLotteryRecord">

    SELECT *
    FROM game_user_lottery_record
    <where>
      <if test="userId != null">
        and user_id = #{userId}
      </if>
      <if test="isSingle">
        and quantity = 1
      </if>
      <if test="!isSingle">
        and quantity > 1
      </if>
      <if test="gameType != null and gameType != ''">
        and game_type = #{gameType}
      </if>
    </where>
    ORDER BY create_time DESC
    LIMIT 20

  </select>

</mapper>
