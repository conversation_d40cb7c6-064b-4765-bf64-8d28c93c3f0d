<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.LuckyGiftProbabilityDetailsDAO">

  <select id="getMaxMultiple" resultType="java.lang.Integer">

    SELECT MAX(multiple) AS max_multiple
    FROM game_lucky_gift_probability_details
    WHERE probability_id = #{probabilityId} limit 1

  </select>

  <select id="listMaxMultiple" parameterType="java.util.List"
    resultType="com.red.circle.other.infra.database.rds.entity.game.LuckyGiftProbabilityDetails">

    SELECT MAX(multiple) AS multiple, probability_id
    FROM game_lucky_gift_probability_details
    WHERE probability_id IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
    GROUP BY probability_id
  </select>

</mapper>
