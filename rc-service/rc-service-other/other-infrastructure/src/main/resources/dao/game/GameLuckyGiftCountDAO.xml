<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameLuckyGiftCountDAO">
  <select id="countLuckyGiftGame"
    resultType="com.red.circle.other.inner.model.dto.game.GameLuckyGiftCountDTO">
    SELECT
    COUNT(DISTINCT user_id) AS userCount,
    SUM(pay_amount) AS userExpendCount,
    SUM(award_amount) AS awardCount
    FROM
    game_lucky_gift_count
    <where>
      <if test="query.userId != null">
        AND user_id = #{query.userId}
      </if>
      <if test="query.roomId != null">
        AND room_id = #{query.roomId}
      </if>
      <if test="query.giftId != null">
        AND gift_id = #{query.giftId}
      </if>
      <if test="query.quantity != null">
        AND quantity = #{query.quantity}
      </if>
      <if test="query.startTime != null">
        AND create_time >= #{query.startTime}
      </if>
      <if test="query.endTime != null">
        AND create_time &lt;= #{query.endTime}
      </if>
      <if test="query.sysOrigin != null and query.sysOrigin != ''">
        AND sys_origin = #{query.sysOrigin}
      </if>
    </where>
  </select>
</mapper>
