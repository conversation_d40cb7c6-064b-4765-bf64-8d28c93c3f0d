<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameIndoorTeamPkRecordDAO">

  <!-- 是否有权限创建游戏 -->
  <select id="isCanGame" resultType="java.lang.Long">

    select id
    from game_indoor_team_pk_record
    where (game_status = 'WAITING' or game_status = 'STARTED')
      and (create_user = #{userId} or room_id = #{roomId}) limit 1

  </select>

  <select id="checkCreateGame" resultType="java.lang.Long">
    select id
    from game_indoor_team_pk_record
    where game_status in ('WAITING','STARTED')
      and create_user = #{userId}
    order by create_time desc limit 1
  </select>

  <select id="getEfficientPkByRoomId"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameIndoorTeamPkRecord">

    select *
    from game_indoor_team_pk_record
    where (game_status = 'WAITING' or game_status = 'STARTED')
      and room_id = #{roomId}
    order by create_time desc limit 1

  </select>

  <!-- 查询当前时间大于结束时间(+1分钟 处理数据时间) ，且还在决斗中状态的PK记录  -->
  <select id="listExpiredStartedRecordByNow"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameIndoorTeamPkRecord">

    select *
    from game_indoor_team_pk_record
    where game_status = 'STARTED'
      and #{nowTime} > DATE_ADD(end_time, INTERVAL 1 MINUTE)

  </select>

  <!-- 查询当前时间大于创建时间(+3分钟:等待时间， +1分钟 处理数据时间) ，且还在等待状态的PK记录  -->
  <select id="listExpiredWaitingRecordByNow"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameIndoorTeamPkRecord">

    select *
    from game_indoor_team_pk_record
    where game_status = 'WAITING'
      and #{nowTime} > DATE_ADD(create_time, INTERVAL 4 MINUTE)

  </select>


</mapper>
