<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameRoomPkRecordDAO">

  <!-- 是否有权限创建游戏 -->
  <select id="isCanGame" resultType="java.lang.Long">

    select id
    from game_room_pk_record
    where (game_status = 'WAITING' or game_status = 'STARTED')
      and (sponsor_user_id = #{userId} or recipient_user_id = #{userId} or
           sponsor_room_id = #{roomId} or recipient_room_id = #{roomId})
      limit 1

  </select>

  <select id="isCanJoinOrCreateGameByUserId" resultType="java.lang.Long">
    select id
    from game_room_pk_record
    where   game_status in ('WAITING','STARTED')
      and (sponsor_user_id = #{userId} or recipient_user_id = #{userId})
    order by create_time desc limit 1
  </select>

  <select id="getByRoomIdByStatus"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkRecord">
    select *
    from game_room_pk_record
    where game_status = #{gameState}
      and (sponsor_room_id = #{roomId} or recipient_room_id = #{roomId})
    order by create_time desc limit 1
  </select>

  <select id="getByUserIdByStatus"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkRecord">

    select *
    from game_room_pk_record
    where game_status = #{gameState}
      and (sponsor_user_id = #{userId} or recipient_user_id = #{userId})
    order by create_time desc limit 1

  </select>

  <select id="getEfficientPkByRoomId"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkRecord">

    select *
    from game_room_pk_record
    where  game_status in ('WAITING','STARTED')
      and (sponsor_room_id = #{roomId} or recipient_room_id = #{roomId})
    order by create_time desc limit 1

  </select>

  <!-- 查询当前时间大于结束时间(+1分钟 处理数据时间) ，且还在决斗中状态的PK记录  -->
  <select id="listExpiredStartedRecordByNow"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkRecord">

    select *
    from game_room_pk_record
    where game_status = 'STARTED'
      and #{nowTime} > DATE_ADD(end_time, INTERVAL 1 MINUTE)

  </select>

  <!-- 查询当前时间大于创建时间(+3分钟:等待时间， +1分钟 处理数据时间) ，且还在等待状态的PK记录  -->
  <select id="listExpiredWaitingRecordByNow"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkRecord">

    select *
    from game_room_pk_record
    where game_status = 'WAITING'
      and #{nowTime} > DATE_ADD(create_time, INTERVAL 4 MINUTE)

  </select>


</mapper>
