<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameRoomPkIntegralRecordDAO">

  <select id="getTotalIntegralByPkId" resultType="java.lang.Long">

    select sum(integral)
    from game_room_pk_integral_record
    where game_room_pk_id = #{pkId}

  </select>

  <select id="getTotalIntegralAndUserIdByPkId"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkIntegralRecord">

    select sum(integral) as integral, receiver_user_id as receiverUserId
    from game_room_pk_integral_record
    where game_room_pk_id = #{pkId}
    group by receiver_user_id
    order by integral desc

  </select>

  <select id="saveIntegralToFansByPkId">

    insert into game_room_pk_fans_integral (sys_origin, fans_user_id, user_id, integral,
                                            create_time, create_user, pk_type)
    select sys_origin,
           create_user,
           receiver_user_id,
           sum(integral) as integral,
           NOW(),
           create_user,
           pk_type
    from game_room_pk_integral_record
    where game_room_pk_id = #{pkId}
    group by create_user, receiver_user_id, sys_origin, pk_type

  </select>

</mapper>
