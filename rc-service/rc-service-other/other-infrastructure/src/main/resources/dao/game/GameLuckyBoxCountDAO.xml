<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameLuckyBoxCountDAO">

  <select id="countLuckyBoxGame"
    resultType="com.red.circle.other.inner.model.dto.game.GameLuckyBoxCountInfoDTO">
    SELECT
    COUNT(DISTINCT user_id) AS userCount,
    SUM(pay_amount) AS userExpendCount,
    SUM(gift_amount) AS awardCount
    FROM
    game_lucky_box_count
    <where>
      <if test="query.userId != null">
        AND user_id = #{query.userId}
      </if>
      <if test="query.roomId != null">
        AND room_id = #{query.roomId}
      </if>
      <if test="query.startTime != null">
        AND create_time >= #{query.startTime}
      </if>
      <if test="query.endTime != null">
        AND create_time &lt;= #{query.endTime}
      </if>
      <if test="query.sysOrigin != null and query.sysOrigin != ''">
        AND sys_origin = #{query.sysOrigin}
      </if>
      <if test="query.frequency != null and query.frequency != ''">
        AND frequency = #{query.frequency}
      </if>
    </where>
  </select>
</mapper>
