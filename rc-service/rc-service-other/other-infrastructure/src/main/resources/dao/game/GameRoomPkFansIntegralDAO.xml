<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameRoomPkFansIntegralDAO">

  <select id="getBestFan"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkFansIntegral">

    <!-- timeType:1.上周 2.这周 3.昨天 4.今天 -->

    <if test="timeType == 1">
      select sum(integral) as integral, fans_user_id, user_id
      from game_room_pk_fans_integral
      where user_id = #{userId}
      AND YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) = YEARWEEK(CONVERT_TZ(now(),
      'GMT', '+03:00'), 1) - 1 and pk_type != 'ROOM_VS_ROOM'
      group by fans_user_id
      order by integral desc limit 1
    </if>

    <if test="timeType == 2">
      select sum(integral) as integral, fans_user_id, user_id
      from game_room_pk_fans_integral
      where user_id = #{userId}
      AND YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) = YEARWEEK(CONVERT_TZ(now(),
      'GMT', '+03:00'), 1) and pk_type != 'ROOM_VS_ROOM'
      group by fans_user_id
      order by integral desc limit 1
    </if>

    <if test="timeType == 3">
      select sum(integral) as integral, fans_user_id, user_id
      from game_room_pk_fans_integral
      where user_id = #{userId}
      AND DAYOFYEAR(CONVERT_TZ(create_time, 'GMT', '+03:00')) = DAYOFYEAR(CONVERT_TZ(now(),
      'GMT', '+03:00')) - 1 and pk_type != 'ROOM_VS_ROOM'
      group by fans_user_id
      order by integral desc limit 1
    </if>

    <if test="timeType == 4">
      select sum(integral) as integral, fans_user_id, user_id
      from game_room_pk_fans_integral
      where user_id = #{userId}
      AND DAYOFYEAR(CONVERT_TZ(create_time, 'GMT', '+03:00')) = DAYOFYEAR(CONVERT_TZ(now(),
      'GMT', '+03:00')) and pk_type != 'ROOM_VS_ROOM'
      group by fans_user_id
      order by integral desc limit 1
    </if>

  </select>

  <!-- 辅助:查询指定时间数据 -->
  <select id="getSpecifiedTimeBestFan"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameRoomPkFansIntegral">

    select sum(integral) as integral, fans_user_id, user_id
    from game_room_pk_fans_integral
    where user_id = #{userId}
      and pk_type != 'ROOM_VS_ROOM'
      AND create_time >= #{startTime}
      and create_time
     &lt; #{endTime}
    group by fans_user_id
    order by integral desc limit 1
  </select>

</mapper>
