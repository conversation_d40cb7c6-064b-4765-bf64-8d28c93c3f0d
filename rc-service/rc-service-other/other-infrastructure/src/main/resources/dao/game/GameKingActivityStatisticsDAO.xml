<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.red.circle.other.infra.database.rds.dao.game.GameKingActivityStatisticsDAO">

  <delete id="deleteBySysOriginByUserIds">
    delete
    from game_king_activity_statistics
    <where>
      <if test="sysOrigin != null and sysOrigin != ''">
        and sys_origin = #{sysOrigin}
      </if>
      <if test="userIds != null and userIds.size > 0">
        and user_id IN
        <foreach close=")" collection="userIds" item="item" open="(" separator=",">
          #{item}
        </foreach>
      </if>
    </where>
  </delete>

  <select id="listOldFruitGameBySysOriginByUserIds"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameKingActivityStatistics">
    <!-- 老水果游戏 create_time必须是用户玩游戏时的创建时间 -->
    select sys_origin, user_id, (bet_quantity * multiple) as quantity, CONVERT_TZ(create_time,
    'GMT', '+03:00') as create_time
    from game_fruit_machine_user_bet_record_v2
    where is_lottery = 1
    and receive_status = 'RECEIVED'
    and YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) =
    YEARWEEK(CONVERT_TZ(now(), 'GMT', '+03:00'), 1)
    <if test="sysOrigin != null and sysOrigin != ''">
      and sys_origin = #{sysOrigin}
    </if>
    <if test="userIds != null and userIds.size > 0">
      and user_id IN
      <foreach close=")" collection="userIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>
  <select id="listFruitGameBySysOriginByUserIds"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameKingActivityStatistics">
    <!-- 8种水果游戏 -->
    select sys_origin, user_id, (bet_quantity * multiple) as quantity, CONVERT_TZ(create_time,
    'GMT', '+03:00') as create_time
    from game_double_layer_fruit_user_record
    where is_lottery = 1
    and receive_status = 'RECEIVED'
    and YEARWEEK(CONVERT_TZ(create_time, 'GMT', '+03:00'), 1) =
    YEARWEEK(CONVERT_TZ(now(), 'GMT', '+03:00'), 1)
    <if test="sysOrigin != null and sysOrigin != ''">
      and sys_origin = #{sysOrigin}
    </if>
    <if test="userIds != null and userIds.size > 0">
      and user_id IN
      <foreach close=")" collection="userIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="listUserByCondition"
    resultType="com.red.circle.other.infra.database.rds.entity.game.GameKingActivityStatistics">

    select user_id, sum(quantity) as quantity
    from game_king_activity_statistics
    where sys_origin = #{sysOrigin}
    <if test="promotion == 1">
      <!-- 周一周二得出32强 -->
      and (DAYNAME(CONVERT_TZ(create_time, 'GMT', '+03:00')) = 'Monday' or
      DAYNAME(CONVERT_TZ(create_time, 'GMT', '+03:00')) = 'Tuesday')
      group by user_id
      order by quantity desc
      limit 32;
    </if>
    <if test="promotion == 2">
      <!-- 周三得出16强 -->
      and DAYNAME(CONVERT_TZ(create_time, 'GMT', '+03:00')) = 'Wednesday'
      and user_id IN
      <foreach close=")" collection="topUserIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
      group by user_id
      order by quantity desc
      limit 16;
    </if>
    <if test="promotion == 3">
      <!-- 周四得出8强 -->
      and DAYNAME(CONVERT_TZ(create_time, 'GMT', '+03:00')) = 'Thursday'
      and user_id IN
      <foreach close=")" collection="topUserIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
      group by user_id
      order by quantity desc
      limit 8;
    </if>
    <if test="promotion == 4">
      <!-- 周五得出4强，返回8个人，业务对比在java代码中写 -->
      and DAYNAME(CONVERT_TZ(create_time, 'GMT', '+03:00')) = 'Friday'
      and user_id IN
      <foreach close=")" collection="topUserIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
      group by user_id
      order by quantity desc
      limit 8;
    </if>
    <if test="promotion == 5">
      <!-- 周六出半决赛结果，返回4个人，业务对比在java代码中写 -->
      and DAYNAME(CONVERT_TZ(create_time, 'GMT', '+03:00')) = 'Saturday'
      and user_id IN
      <foreach close=")" collection="topUserIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
      group by user_id
      order by quantity desc
      limit 4;
    </if>
    <if test="promotion == 6">
      <!-- 周日出决赛结果 -->
      and DAYNAME(CONVERT_TZ(create_time, 'GMT', '+03:00')) = 'Sunday'
      and user_id IN
      <foreach close=")" collection="topUserIds" item="item" open="(" separator=",">
        #{item}
      </foreach>
      group by user_id
      order by quantity desc
      limit 2;
    </if>

  </select>

</mapper>
